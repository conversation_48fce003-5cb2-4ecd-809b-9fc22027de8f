{":wallet:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"*********\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON>son\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":wallet:entry:default@PreBuild", "_executionId": ":wallet:entry:default@PreBuild:1750836604945", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "bd5d73dcd5c4a3da7a337ec8b0d0f870"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\build-profile.json5", {"fileSnapShotHashValue": "d6e4e3f6b623f30d5d18a9ae8c85a180"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "f73311f89ed8b137007e2768c06e785f"}], ["D:\\vue\\daxiangmuwallet\\wallet\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "292e8df8413f77f25951022589c777bd"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "214c89d78cb9691df413f8064dc0a807"}], ["D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5", {"fileSnapShotHashValue": "d598e3ca9be157ef14b3fb83e9d27d06"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":wallet:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.icss.myapplication\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"46b32dc768a487f1e93ae46caedab037\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\".preview\",\"_valueType\":\"string\",\"_hash\":\"88e9a315669657b02bc470a13f92befe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"44964f8abd318606862a2cee062554fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"e0aa1132f06f8ec686a7fcdcdb02041e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"b2877a8a5e4d85b48b0208a17da3ae75\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":wallet:entry:default@MergeProfile", "_executionId": ":wallet:entry:default@MergeProfile:1750835550312", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "bd5d73dcd5c4a3da7a337ec8b0d0f870"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "37940d25db47a8772aade52e5f504e91"}]]}}, ":wallet:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":wallet:entry:default@CreateBuildProfile", "_executionId": ":wallet:entry:default@CreateBuildProfile:1750824873499", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "a97543b30f7e182ee8d0bd92f654a1ea"}]]}}, ":wallet:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\",\"_hash\":\"52638b5b8d5967d85f7d558e6c0897dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\",\"_hash\":\"65eb1ae89d72758b386a93dddd5db61d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\",\"_hash\":\"6fbb0d2287cd1f34051162075393f37a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":wallet:entry:default@GeneratePkgContextInfo", "_executionId": ":wallet:entry:default@GeneratePkgContextInfo:1750824873519", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}]]}}, ":wallet:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"713e499a13beffe12f1dfb936f957a2a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"955f8760c6b7289b81ed107c2c4df075\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":wallet:entry:default@ProcessProfile", "_executionId": ":wallet:entry:default@ProcessProfile:1750835550343", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "37940d25db47a8772aade52e5f504e91"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "f6830780467fc1c30b9da2341ea3a59f"}]]}}, ":wallet:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":wallet:entry:default@ProcessRouterMap", "_executionId": ":wallet:entry:default@ProcessRouterMap:1750835550460", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "214c89d78cb9691df413f8064dc0a807"}], ["D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5", {"fileSnapShotHashValue": "d598e3ca9be157ef14b3fb83e9d27d06"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "bd5d73dcd5c4a3da7a337ec8b0d0f870"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "38924378c568474906e11adad4b0ec87"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "ae7e695646994c5b0bb863d65057e390"}]]}}, ":wallet:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"8b6f45ce7dfe9a8d10f683536ae25c0e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"0253181d04285d4e3455a12351c13288\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"974757f304b5bfd1c1454ea7a38cd0d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"df7da4e632138f82ee16fd3304eb99e6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_valueType\":\"undefined\",\"_hash\":\"4620e35a57f3f6f55564cea6f6128e50\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\",\"_valueType\":\"string\",\"_hash\":\"c6664a2785540e945547d725cf43c8aa\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":wallet:entry:default@GenerateLoaderJson", "_executionId": ":wallet:entry:default@GenerateLoaderJson:1750835550488", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "38924378c568474906e11adad4b0ec87"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "ca70a2bc4fa3ff788fc4600f2d47bc73"}]]}}, ":wallet:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":wallet:entry:default@PreviewCompileResource", "_executionId": ":wallet:entry:default@PreviewCompileResource:1750836605029", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "359ba008339c53003cfe224ce513c412"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "37940d25db47a8772aade52e5f504e91"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "c91a617885ba002c26dba03dbecee571"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "b481361bf21ea2873a39b855831c2124"}]]}}, ":wallet:entry:default@CopyPreviewProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@CopyPreviewProfile", "_key": ":wallet:entry:default@CopyPreviewProfile", "_executionId": ":wallet:entry:default@CopyPreviewProfile:1750836605197", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile", {"fileSnapShotHashValue": "62ac83b7517847fe898ade6a06cc774d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"fileSnapShotHashValue": "01f87d949842bb61f443e5243485cb5c"}]]}}, ":wallet:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewBuildConfigJson\",\"_value\":\"{\\\"deviceType\\\":\\\"phone,tablet,2in1\\\",\\\"buildMode\\\":\\\"debug\\\",\\\"note\\\":\\\"false\\\",\\\"logLevel\\\":\\\"3\\\",\\\"isPreview\\\":\\\"true\\\",\\\"checkEntry\\\":\\\"true\\\",\\\"localPropertiesPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\local.properties\\\",\\\"Path\\\":\\\"D:\\\\\\\\app\\\\\\\\devecostudio\\\\\\\\DevEco Studio\\\\\\\\tools\\\\\\\\node\\\\\\\\\\\",\\\"aceProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\",\\\"hapMode\\\":\\\"false\\\",\\\"img2bin\\\":\\\"true\\\",\\\"projectProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\build-profile.json5\\\",\\\"watchMode\\\":\\\"true\\\",\\\"appResource\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ResourceTable.txt\\\",\\\"aceBuildJson\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\loader\\\\\\\\default\\\\\\\\loader.json\\\",\\\"aceModuleRoot\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\ets\\\",\\\"aceSoPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\nativeDependencies.txt\\\",\\\"cachePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\.default\\\",\\\"aceModuleBuild\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\assets\\\\\\\\default\\\\\\\\ets\\\",\\\"aceModuleJsonPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"stageRouterConfig\\\":{\\\"paths\\\":[\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\\\\\\main_pages.json\\\"],\\\"contents\\\":[\\\"{\\\\\\\"module\\\\\\\":{\\\\\\\"pages\\\\\\\":\\\\\\\"$profile:main_pages\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"entry\\\\\\\"}}\\\",\\\"{\\\\\\\"src\\\\\\\":[\\\\\\\"pages/Index\\\\\\\",\\\\\\\"pages/LoginPage\\\\\\\",\\\\\\\"pages/MainPage\\\\\\\",\\\\\\\"pages/TransactionRecordsPage\\\\\\\",\\\\\\\"pages/BankCardPage\\\\\\\",\\\\\\\"pages/SettingsPage\\\\\\\",\\\\\\\"pages/WalletOperationPage\\\\\\\",\\\\\\\"pages/PaymentPage\\\\\\\",\\\\\\\"pages/TransactionDetailPage\\\\\\\"]}\\\"]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":wallet:entry:default@PreviewUpdateAssets", "_executionId": ":wallet:entry:default@PreviewUpdateAssets:*************", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "e15a689bb951ef7fc58b1cf50c277838"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "4bd08ddb46da9c64669f024f1e297d7b"}]]}}, ":wallet:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":wallet:entry:default@PreviewArkTS", "_executionId": ":wallet:entry:default@PreviewArkTS:1750836605225", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "ff8c2a830521e1de0521c543b3b62707"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "77aadaef679ee75096c82f055f9635b8"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "f6830780467fc1c30b9da2341ea3a59f"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "01f87d949842bb61f443e5243485cb5c"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "248d1273afecc185c87e048796dba96b"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "a97543b30f7e182ee8d0bd92f654a1ea"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}