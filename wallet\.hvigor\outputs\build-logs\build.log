[2025-06-25T12:14:05.874] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-06-25T12:14:08.739] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: false,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:08.793] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:20.590] [DEBUG] debug-file - hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }
[2025-06-25T12:14:21.068] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:21.091] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-06-25T12:14:21.097] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-06-25T12:14:21.110] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:21.144] [DEBUG] debug-file - Sdk init in 59 ms 
[2025-06-25T12:14:21.184] [DEBUG] debug-file - Project task initialization takes 37 ms 
[2025-06-25T12:14:21.200] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.231] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:21.242] [DEBUG] debug-file - Module entry task initialization takes 5 ms 
[2025-06-25T12:14:21.344] [DEBUG] debug-file - Configuration task cost before running: 12 s 640 ms 
[2025-06-25T12:14:21.248] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:21.349] [DEBUG] debug-file - Executing task :entry:clean
[2025-06-25T12:14:05.876] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.3',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-06-25T12:14:21.355] [DEBUG] debug-file - entry : clean cost memory 0.2402801513671875
[2025-06-25T12:14:08.743] [DEBUG] debug-file - Since current hvigor version 5.15.3 differs from last hvigor version 
      undefined, delete file-cache.json and task-cache.json.
[2025-06-25T12:14:21.402] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-06-25T12:14:21.076] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:20.591] [DEBUG] debug-file - hvigorfile, binding system plugins [Function: appTasks]
[2025-06-25T12:14:21.069] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:21.209] [DEBUG] debug-file - hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }
[2025-06-25T12:14:21.231] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:21.242] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:21.249] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:21.350] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-25T12:14:05.878] [DEBUG] debug-file - env: daemon=false
[2025-06-25T12:14:21.355] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 651 ms 
[2025-06-25T12:14:08.744] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-25T12:14:21.404] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-06-25T12:14:21.084] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:21.375] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:21.069] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:21.209] [DEBUG] debug-file - hvigorfile, binding system plugins [Function: hapTasks]
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:21.243] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.316] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:05.878] [DEBUG] debug-file - no-daemon, use the parent process.execArgv --max-old-space-size=8192,--expose-gc
[2025-06-25T12:14:21.356] [INFO] debug-file - Finished :entry:clean... after 6 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current worker pool is stopped or closed.
[2025-06-25T12:14:21.381] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-25T12:14:21.069] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:21.243] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.318] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:21.356] [DEBUG] debug-file - Executing task ::clean
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Clear worker 0.
[2025-06-25T12:14:21.394] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:21.330] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:21.356] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Worker 0 has been cleared.
[2025-06-25T12:14:21.394] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-06-25T12:14:21.232] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:21.330] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:21.357] [DEBUG] debug-file - wallet : clean cost memory 0.0303955078125
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-25T12:14:21.233] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:21.331] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:21.357] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 653 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current busy worker size: 0.
[2025-06-25T12:14:21.335] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:21.357] [INFO] debug-file - Finished ::clean... after 1 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Clear worker 1.
[2025-06-25T12:14:21.335] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:21.357] [DEBUG] debug-file - Executing task :entry:init
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Worker 1 has been cleared.
[2025-06-25T12:14:21.340] [DEBUG] debug-file - Configuration phase cost:12 s 577 ms 
[2025-06-25T12:14:21.357] [DEBUG] debug-file - entry : init cost memory 0.01016998291015625
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current idle worker size: 0.
[2025-06-25T12:14:21.357] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 654 ms 
[2025-06-25T12:14:21.408] [DEBUG] debug-file - Current busy worker size: 0.
[2025-06-25T12:14:21.357] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-25T12:14:21.408] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - Executing task ::init
[2025-06-25T12:14:21.425] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - wallet : init cost memory 0.00897216796875
[2025-06-25T12:14:21.425] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 654 ms 
[2025-06-25T12:14:21.426] [DEBUG] debug-file - Current worker pool is terminated.
[2025-06-25T12:14:21.358] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-25T12:14:26.261] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-06-25T12:14:26.529] [DEBUG] debug-file - env: daemon=true
[2025-06-25T12:14:26.264] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.3',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-06-25T12:14:28.811] [DEBUG] debug-file - java daemon tryConnect failed Error: connect ECONNREFUSED 127.0.0.1:45050
[2025-06-25T12:14:28.866] [DEBUG] debug-file - java daemon started at port 45050 pid 6328
[2025-06-25T12:14:28.898] [DEBUG] debug-file - session manager: set active socket. socketId=qSgahDwZSYEKw4ITAAAB
[2025-06-25T12:14:29.737] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:29.763] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:29.765] [DEBUG] debug-file - Cache service initialization finished in 3 ms 
[2025-06-25T12:14:29.778] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.277] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:32.279] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:32.507] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:32.508] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:32.509] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:32.510] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:32.516] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:32.533] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:32.540] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-06-25T12:14:32.546] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-06-25T12:14:32.560] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:32.591] [DEBUG] debug-file - Sdk init in 57 ms 
[2025-06-25T12:14:32.622] [DEBUG] debug-file - Project task initialization takes 29 ms 
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.631] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.638] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:32.638] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:32.653] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:32.653] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:32.655] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:32.663] [DEBUG] debug-file - Module entry task initialization takes 5 ms 
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.680] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 15 ms 
[2025-06-25T12:14:32.682] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:32.684] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:32.756] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:32.758] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:32.772] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:32.772] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:32.773] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:32.778] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:32.778] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:32.788] [DEBUG] debug-file - Configuration phase cost:3 s 18 ms 
[2025-06-25T12:14:32.791] [DEBUG] debug-file - Configuration task cost before running: 3 s 48 ms 
[2025-06-25T12:14:32.791] [DEBUG] debug-file - Executing task :entry:init
[2025-06-25T12:14:32.792] [DEBUG] debug-file - entry : init cost memory 0.0649261474609375
[2025-06-25T12:14:32.792] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 49 ms 
[2025-06-25T12:14:32.792] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-25T12:14:32.792] [DEBUG] debug-file - Executing task ::init
[2025-06-25T12:14:32.792] [DEBUG] debug-file - wallet : init cost memory 0.01282501220703125
[2025-06-25T12:14:32.792] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 49 ms 
[2025-06-25T12:14:32.793] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-25T12:14:32.815] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:32.824] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-25T12:14:32.835] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-06-25T12:14:32.851] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:14:32.852] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:14:32.835] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-06-25T12:14:32.838] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-06-25T12:14:32.841] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Cleanup worker 0.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Worker 0 has been cleaned up.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Cleanup worker 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current idle worker size: 0.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-25T12:14:32.846] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T12:14:32.848] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:32.849] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:32.857] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-06-25T12:14:32.862] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-06-25T12:14:33.133] [DEBUG] debug-file - session manager: set active socket. socketId=pdjZSrDoZXBA_742AAAD
[2025-06-25T12:14:33.137] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:33.154] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:33.155] [DEBUG] debug-file - Cache service initialization finished in 2 ms 
[2025-06-25T12:14:33.171] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.176] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:33.176] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:33.183] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:33.183] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:33.184] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:33.184] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:33.186] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:33.190] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:33.199] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:33.220] [DEBUG] debug-file - Sdk init in 30 ms 
[2025-06-25T12:14:33.242] [DEBUG] debug-file - Project task initialization takes 21 ms 
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.249] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.255] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:33.255] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:33.262] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:33.262] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:33.266] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.281] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 14 ms 
[2025-06-25T12:14:33.283] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:33.286] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:33.347] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:33.348] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:33.355] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:33.356] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:33.358] [DEBUG] debug-file - Configuration phase cost:197 ms 
[2025-06-25T12:14:33.359] [DEBUG] debug-file - Configuration task cost before running: 219 ms 
[2025-06-25T12:14:33.362] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.362] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.366] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T12:14:33.373] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.373] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.479] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T12:14:33.480] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T12:14:33.480] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T12:14:33.481] [DEBUG] debug-file - entry : default@PreBuild cost memory 12.284629821777344
[2025-06-25T12:14:33.481] [DEBUG] debug-file - runTaskFromQueue task cost before running: 341 ms 
[2025-06-25T12:14:33.483] [INFO] debug-file - Finished :entry:default@PreBuild... after 115 ms 
[2025-06-25T12:14:33.486] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.486] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.487] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T12:14:33.489] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.489] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app target API version with '50003015'
[2025-06-25T12:14:33.490] [DEBUG] debug-file - Change app minimum API version with '50003015'
[2025-06-25T12:14:33.490] [DEBUG] debug-file - Use cli appEnvironment
[2025-06-25T12:14:33.494] [DEBUG] debug-file - entry : default@MergeProfile cost memory -13.746086120605469
[2025-06-25T12:14:33.494] [DEBUG] debug-file - runTaskFromQueue task cost before running: 354 ms 
[2025-06-25T12:14:33.494] [INFO] debug-file - Finished :entry:default@MergeProfile... after 8 ms 
[2025-06-25T12:14:33.497] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.497] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.498] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T12:14:33.499] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-25T12:14:33.499] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.499] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.502] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1068267822265625
[2025-06-25T12:14:33.502] [DEBUG] debug-file - runTaskFromQueue task cost before running: 362 ms 
[2025-06-25T12:14:33.502] [INFO] debug-file - Finished :entry:default@CreateBuildProfile... after 5 ms 
[2025-06-25T12:14:33.504] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.504] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.505] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T12:14:33.505] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.505] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.506] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03759765625
[2025-06-25T12:14:33.506] [DEBUG] debug-file - runTaskFromQueue task cost before running: 366 ms 
[2025-06-25T12:14:33.506] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T12:14:33.508] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.509] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.518] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T12:14:33.518] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T12:14:33.519] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.04282379150390625
[2025-06-25T12:14:33.519] [DEBUG] debug-file - runTaskFromQueue task cost before running: 379 ms 
[2025-06-25T12:14:33.520] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 2 ms 
[2025-06-25T12:14:33.522] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.522] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.524] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T12:14:33.524] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.524] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.526] [DEBUG] debug-file - [
  'D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\node.exe',
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\ts2abc.js',
  '--target-api-version',
  '15'
]
[2025-06-25T12:14:33.709] [DEBUG] debug-file - ********
[2025-06-25T12:14:33.714] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 1.9788284301757812
[2025-06-25T12:14:33.714] [DEBUG] debug-file - runTaskFromQueue task cost before running: 574 ms 
[2025-06-25T12:14:33.714] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 190 ms 
[2025-06-25T12:14:33.718] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.718] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.720] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T12:14:33.723] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.724] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.727] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.150177001953125
[2025-06-25T12:14:33.727] [DEBUG] debug-file - runTaskFromQueue task cost before running: 587 ms 
[2025-06-25T12:14:33.729] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 7 ms 
[2025-06-25T12:14:33.731] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.732] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.734] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T12:14:33.738] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T12:14:33.738] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.738] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.738] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.07375335693359375
[2025-06-25T12:14:33.739] [DEBUG] debug-file - runTaskFromQueue task cost before running: 599 ms 
[2025-06-25T12:14:33.741] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 3 ms 
[2025-06-25T12:14:33.743] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.743] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.748] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T12:14:33.772] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7722091674804688
[2025-06-25T12:14:33.772] [DEBUG] debug-file - runTaskFromQueue task cost before running: 632 ms 
[2025-06-25T12:14:33.779] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 25 ms 
[2025-06-25T12:14:33.781] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.781] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.782] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T12:14:33.785] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T12:14:33.793] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T12:14:33.795] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209657856,
  heapTotal: 158199808,
  heapUsed: 130996128,
  external: 3295817,
  arrayBuffers: 285072
} os memoryUsage :12.628124237060547
[2025-06-25T12:14:33.894] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:33.897] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T12:14:33.899] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209686528,
  heapTotal: 158199808,
  heapUsed: 131258120,
  external: 3295943,
  arrayBuffers: 285213
} os memoryUsage :12.629745483398438
[2025-06-25T12:14:33.934] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:33.944] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***n',
  '-r',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T12:14:33.946] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209768448,
  heapTotal: 158199808,
  heapUsed: 131534608,
  external: 3304261,
  arrayBuffers: 294238
} os memoryUsage :12.63656234741211
[2025-06-25T12:14:34.007] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:34.027] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.1359710693359375
[2025-06-25T12:14:34.027] [DEBUG] debug-file - runTaskFromQueue task cost before running: 887 ms 
[2025-06-25T12:14:34.028] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 243 ms 
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.039154052734375
[2025-06-25T12:14:34.031] [DEBUG] debug-file - runTaskFromQueue task cost before running: 891 ms 
[2025-06-25T12:14:34.031] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T12:14:34.034] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.034] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.035] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T12:14:34.035] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.035] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.042] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.40596771240234375
[2025-06-25T12:14:34.042] [DEBUG] debug-file - runTaskFromQueue task cost before running: 902 ms 
[2025-06-25T12:14:34.043] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 8 ms 
[2025-06-25T12:14:34.045] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.045] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.046] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T12:14:34.046] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.046] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.047] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03907012939453125
[2025-06-25T12:14:34.047] [DEBUG] debug-file - runTaskFromQueue task cost before running: 907 ms 
[2025-06-25T12:14:34.047] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T12:14:34.049] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T12:14:34.049] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01219940185546875
[2025-06-25T12:14:34.049] [DEBUG] debug-file - runTaskFromQueue task cost before running: 909 ms 
[2025-06-25T12:14:34.049] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T12:14:34.052] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.052] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.054] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T12:14:34.057] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.057] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.068] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.08829498291015625
[2025-06-25T12:14:34.068] [DEBUG] debug-file - runTaskFromQueue task cost before running: 928 ms 
[2025-06-25T12:14:34.069] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 15 ms 
[2025-06-25T12:14:34.074] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.074] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.083] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T12:14:34.131] [DEBUG] debug-file - session manager: binding session. socketId=pdjZSrDoZXBA_742AAAD, threadId=1@1.
[2025-06-25T12:14:34.122] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 1.3090362548828125
[2025-06-25T12:14:40.264] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-25T12:14:40.281] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-25T12:14:55.528] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-25T12:14:55.529] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T12:14:55.545] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-25T12:14:55.551] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 21 s 460 ms 
[2025-06-25T12:14:55.557] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-25T12:14:55.557] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.02152252197265625
[2025-06-25T12:14:55.558] [DEBUG] debug-file - runTaskFromQueue task cost before running: 22 s 418 ms 
[2025-06-25T12:14:55.558] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-25T12:14:55.575] [DEBUG] debug-file - BUILD SUCCESSFUL in 22 s 435 ms 
[2025-06-25T12:14:55.579] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:14:55.581] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:14:55.582] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T12:14:55.583] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\build-profile.json5 cache by regenerate.
[2025-06-25T12:14:55.584] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json cache by regenerate.
[2025-06-25T12:14:55.585] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-25T12:14:55.586] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:14:55.587] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T12:14:55.588] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T12:14:55.590] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:13 ms .
[2025-06-25T12:14:55.591] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:14:55.592] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T12:14:55.593] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:14:55.594] [DEBUG] debug-file - Update task entry:default@MergeProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache.
[2025-06-25T12:14:55.595] [DEBUG] debug-file - Incremental task entry:default@MergeProfile post-execution cost:6 ms .
[2025-06-25T12:14:55.595] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:14:55.596] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T12:14:55.597] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache.
[2025-06-25T12:14:55.598] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile post-execution cost:3 ms .
[2025-06-25T12:14:55.598] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-25T12:14:55.599] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-25T12:14:55.599] [DEBUG] debug-file - Update task entry:default@ProcessProfile input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T12:14:55.600] [DEBUG] debug-file - Update task entry:default@ProcessProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache.
[2025-06-25T12:14:55.601] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile post-execution cost:2 ms .
[2025-06-25T12:14:55.605] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T12:14:55.605] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T12:14:55.607] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:14:55.607] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:14:55.608] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache.
[2025-06-25T12:14:55.609] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\loader-router-map.json cache.
[2025-06-25T12:14:55.610] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap post-execution cost:10 ms .
[2025-06-25T12:14:55.618] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:14:55.619] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:14:55.620] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache by regenerate.
[2025-06-25T12:14:55.621] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\loader.json cache.
[2025-06-25T12:14:55.622] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson post-execution cost:12 ms .
[2025-06-25T12:14:55.623] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources cache by regenerate.
[2025-06-25T12:14:55.634] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T12:14:55.635] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-25T12:14:55.652] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\r\default cache.
[2025-06-25T12:14:55.654] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:33 ms .
[2025-06-25T12:14:55.655] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-25T12:14:55.657] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-25T12:14:55.659] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:6 ms .
[2025-06-25T12:14:55.661] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-25T12:14:55.662] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-25T12:14:55.662] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:3 ms .
[2025-06-25T12:14:55.670] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T12:14:55.672] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T12:14:55.673] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T12:14:55.674] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T12:14:55.676] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T12:14:55.680] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:14:55.681] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T12:14:55.682] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:14:55.683] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T12:14:55.684] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:22 ms .
[2025-06-25T12:14:55.728] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T12:14:55.729] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:05:02.512] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-25T15:05:02.549] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-25T15:05:13.362] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-25T15:05:13.366] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-25T15:06:23.322] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:23.324] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:23.339] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-25T15:06:23.372] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:06:23.375] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-25T15:06:24.235] [DEBUG] debug-file - session manager: set active socket. socketId=ekhrpL6KYUBZ0PkrAAAF
[2025-06-25T15:06:24.264] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:06:24.317] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:06:24.331] [DEBUG] debug-file - Cache service initialization finished in 13 ms 
[2025-06-25T15:06:24.361] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:24.390] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:06:24.391] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:06:24.420] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:06:24.420] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:06:24.421] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:06:24.421] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:06:24.427] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:06:24.440] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:06:24.468] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:06:24.533] [DEBUG] debug-file - Sdk init in 90 ms 
[2025-06-25T15:06:24.603] [DEBUG] debug-file - Project task initialization takes 66 ms 
[2025-06-25T15:06:24.604] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:06:24.604] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:24.604] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:24.619] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:24.625] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:06:24.625] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:06:24.641] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:06:24.641] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:06:24.642] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:06:24.642] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:06:24.643] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:06:24.643] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:06:24.643] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:06:24.649] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-25T15:06:24.649] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:06:24.650] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:24.650] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:24.713] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 59 ms 
[2025-06-25T15:06:24.716] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:06:24.725] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:06:24.747] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:06:24.751] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:06:24.779] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:06:24.779] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:06:24.780] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:06:24.794] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:06:24.794] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:06:24.803] [DEBUG] debug-file - Configuration phase cost:463 ms 
[2025-06-25T15:06:24.807] [DEBUG] debug-file - Configuration task cost before running: 526 ms 
[2025-06-25T15:06:24.814] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.814] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.821] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T15:06:24.843] [DEBUG] debug-file - entry:default@PreBuild is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json' has been changed.
[2025-06-25T15:06:24.843] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 16 ms .
[2025-06-25T15:06:24.844] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.844] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.861] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T15:06:24.863] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T15:06:24.863] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T15:06:24.864] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.5323638916015625
[2025-06-25T15:06:24.864] [DEBUG] debug-file - runTaskFromQueue task cost before running: 582 ms 
[2025-06-25T15:06:24.870] [INFO] debug-file - Finished :entry:default@PreBuild... after 43 ms 
[2025-06-25T15:06:24.874] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.874] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.876] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T15:06:24.882] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 6 ms .
[2025-06-25T15:06:24.882] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1116485595703125
[2025-06-25T15:06:24.883] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-25T15:06:24.888] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.888] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.890] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T15:06:24.893] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms 
[2025-06-25T15:06:24.899] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 5 ms .
[2025-06-25T15:06:24.900] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory -1.6363372802734375
[2025-06-25T15:06:24.900] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-25T15:06:24.904] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.905] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.906] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T15:06:24.906] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.906] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.907] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037994384765625
[2025-06-25T15:06:24.907] [DEBUG] debug-file - runTaskFromQueue task cost before running: 625 ms 
[2025-06-25T15:06:24.907] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T15:06:24.912] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.912] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.933] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T15:06:24.933] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:06:24.936] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 3 ms .
[2025-06-25T15:06:24.937] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06836700439453125
[2025-06-25T15:06:24.937] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-25T15:06:24.944] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.945] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.949] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T15:06:24.952] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .
[2025-06-25T15:06:24.953] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.06578826904296875
[2025-06-25T15:06:24.953] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-25T15:06:24.959] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.959] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.963] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T15:06:24.974] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .
[2025-06-25T15:06:24.975] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.21306610107421875
[2025-06-25T15:06:24.977] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-25T15:06:24.982] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.982] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.984] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T15:06:24.992] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T15:06:24.993] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:24.993] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:24.994] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.1920013427734375
[2025-06-25T15:06:24.999] [DEBUG] debug-file - runTaskFromQueue task cost before running: 717 ms 
[2025-06-25T15:06:25.002] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 7 ms 
[2025-06-25T15:06:25.009] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.009] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.017] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T15:06:25.103] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 49 ms .
[2025-06-25T15:06:25.104] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7763214111328125
[2025-06-25T15:06:25.126] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-25T15:06:25.130] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.130] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.132] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T15:06:25.139] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T15:06:25.155] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources' has been changed.
[2025-06-25T15:06:25.156] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 17 ms .
[2025-06-25T15:06:25.215] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T15:06:25.219] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 89165824,
  heapTotal: 119926784,
  heapUsed: 111750240,
  external: 3124482,
  arrayBuffers: 118383
} os memoryUsage :12.973506927490234
[2025-06-25T15:06:25.286] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:06:25.291] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T15:06:25.300] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 89489408,
  heapTotal: 119926784,
  heapUsed: 112005344,
  external: 3124608,
  arrayBuffers: 118524
} os memoryUsage :12.969558715820312
[2025-06-25T15:06:25.389] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:06:25.396] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***n',
  '-r',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T15:06:25.399] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 89579520,
  heapTotal: 119926784,
  heapUsed: 112282152,
  external: 3124734,
  arrayBuffers: 119529
} os memoryUsage :12.968482971191406
[2025-06-25T15:06:25.498] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:06:25.505] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.5144577026367188
[2025-06-25T15:06:25.506] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 224 ms 
[2025-06-25T15:06:25.508] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 368 ms 
[2025-06-25T15:06:25.517] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.517] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.517] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T15:06:25.518] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.518] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.518] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-25T15:06:25.519] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 237 ms 
[2025-06-25T15:06:25.519] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 2 ms 
[2025-06-25T15:06:25.528] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.528] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.530] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T15:06:25.536] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile' has been changed.
[2025-06-25T15:06:25.537] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 6 ms .
[2025-06-25T15:06:25.537] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.537] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.556] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.22550201416015625
[2025-06-25T15:06:25.557] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 275 ms 
[2025-06-25T15:06:25.558] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 27 ms 
[2025-06-25T15:06:25.566] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.566] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.570] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T15:06:25.570] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.570] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.571] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-25T15:06:25.571] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 289 ms 
[2025-06-25T15:06:25.572] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 2 ms 
[2025-06-25T15:06:25.578] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T15:06:25.579] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01186370849609375
[2025-06-25T15:06:25.579] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 297 ms 
[2025-06-25T15:06:25.579] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 2 ms 
[2025-06-25T15:06:25.587] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.587] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.590] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T15:06:25.595] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-25T15:06:25.596] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .
[2025-06-25T15:06:25.596] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.596] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.602] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.181182861328125
[2025-06-25T15:06:25.603] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 321 ms 
[2025-06-25T15:06:25.607] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 14 ms 
[2025-06-25T15:06:25.615] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:25.615] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:25.633] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T15:06:25.685] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-25T15:06:25.686] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 31 ms .
[2025-06-25T15:06:25.761] [DEBUG] debug-file - session manager: binding session. socketId=ekhrpL6KYUBZ0PkrAAAF, threadId=1@2.
[2025-06-25T15:06:25.770] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -0.5794601440429688
[2025-06-25T15:06:30.723] [DEBUG] debug-file - default@PreviewArkTS watch work[2] failed.
[2025-06-25T15:06:30.725] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T15:06:30.726] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T15:06:30.735] [DEBUG] debug-file - ERROR: stacktrace = Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:06:30.735] [ERROR] debug-file - Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:06:30.751] [WARN] debug-file - BUILD FAILED in 6 s 469 ms 
[2025-06-25T15:06:30.752] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T15:06:30.753] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T15:06:30.753] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T15:06:30.753] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\build-profile.json5 cache by regenerate.
[2025-06-25T15:06:30.754] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json cache from map.
[2025-06-25T15:06:30.754] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-25T15:06:30.754] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:06:30.755] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T15:06:30.755] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:5 ms .
[2025-06-25T15:06:30.722] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-25T15:06:30.756] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-25T15:06:30.757] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources cache from map.
[2025-06-25T15:06:30.757] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T15:06:30.757] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-25T15:06:30.765] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\r\default cache.
[2025-06-25T15:06:30.766] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:10 ms .
[2025-06-25T15:06:30.766] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile cache from map.
[2025-06-25T15:06:30.767] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-25T15:06:30.768] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-25T15:06:30.768] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-25T15:06:30.769] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-25T15:06:30.769] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-25T15:06:30.772] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T15:06:30.773] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-25T15:06:30.773] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T15:06:30.773] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T15:06:30.774] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T15:06:30.777] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:06:30.777] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T15:06:30.778] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:06:30.778] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T15:06:30.778] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:10 ms .
[2025-06-25T15:06:30.794] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:06:30.795] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:06:30.805] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:30.806] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:30.806] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:06:30.807] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-25T15:06:42.355] [DEBUG] debug-file - session manager: set active socket. socketId=5_2M1udHyfP2gnbQAAAH
[2025-06-25T15:06:42.365] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:06:42.380] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:06:42.384] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-25T15:06:42.395] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:42.399] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:06:42.399] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:06:42.406] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:06:42.406] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:06:42.406] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:06:42.406] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:06:42.408] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:06:42.411] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:06:42.421] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:06:42.441] [DEBUG] debug-file - Sdk init in 29 ms 
[2025-06-25T15:06:42.460] [DEBUG] debug-file - Project task initialization takes 18 ms 
[2025-06-25T15:06:42.460] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:06:42.460] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:42.460] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:06:42.466] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:42.470] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:06:42.470] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:06:42.476] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:06:42.476] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:06:42.476] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:06:42.476] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:06:42.477] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:06:42.477] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:06:42.477] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:06:42.480] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T15:06:42.480] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:06:42.480] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:42.480] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:06:42.493] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 12 ms 
[2025-06-25T15:06:42.494] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:06:42.495] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:06:42.501] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:06:42.502] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:06:42.506] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:06:42.506] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:06:42.507] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:06:42.508] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:06:42.508] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:06:42.511] [DEBUG] debug-file - Configuration phase cost:122 ms 
[2025-06-25T15:06:42.512] [DEBUG] debug-file - Configuration task cost before running: 144 ms 
[2025-06-25T15:06:42.514] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.514] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.517] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T15:06:42.524] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 5 ms .
[2025-06-25T15:06:42.524] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.28369140625
[2025-06-25T15:06:42.526] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-25T15:06:42.528] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.528] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.528] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T15:06:42.530] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-25T15:06:42.530] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.10677337646484375
[2025-06-25T15:06:42.531] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-25T15:06:42.533] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.533] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.534] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T15:06:42.534] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:06:42.535] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-25T15:06:42.536] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.100372314453125
[2025-06-25T15:06:42.536] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-25T15:06:42.538] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.538] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.538] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T15:06:42.539] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.539] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.539] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-25T15:06:42.539] [DEBUG] debug-file - runTaskFromQueue task cost before running: 170 ms 
[2025-06-25T15:06:42.539] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T15:06:42.540] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.541] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.547] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T15:06:42.547] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:06:42.548] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-25T15:06:42.548] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06725311279296875
[2025-06-25T15:06:42.548] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-25T15:06:42.550] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.550] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.551] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T15:06:42.552] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-25T15:06:42.552] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.0588531494140625
[2025-06-25T15:06:42.552] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-25T15:06:42.554] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.554] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.555] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T15:06:42.560] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .
[2025-06-25T15:06:42.561] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.23589324951171875
[2025-06-25T15:06:42.562] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-25T15:06:42.563] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.563] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.564] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T15:06:42.567] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T15:06:42.567] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.567] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.567] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.0696258544921875
[2025-06-25T15:06:42.568] [DEBUG] debug-file - runTaskFromQueue task cost before running: 199 ms 
[2025-06-25T15:06:42.569] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-25T15:06:42.571] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.571] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.573] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T15:06:42.592] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-25T15:06:42.592] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.9459991455078125
[2025-06-25T15:06:42.598] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-25T15:06:42.601] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.601] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.604] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T15:06:42.606] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T15:06:42.619] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 13 ms .
[2025-06-25T15:06:42.620] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.5409698486328125
[2025-06-25T15:06:42.621] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-25T15:06:42.625] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.625] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.625] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T15:06:42.625] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.625] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.625] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384979248046875
[2025-06-25T15:06:42.625] [DEBUG] debug-file - runTaskFromQueue task cost before running: 257 ms 
[2025-06-25T15:06:42.625] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T15:06:42.628] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.628] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.628] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T15:06:42.631] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-25T15:06:42.631] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.10504150390625
[2025-06-25T15:06:42.631] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-25T15:06:42.633] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.633] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.633] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T15:06:42.633] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.634] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.634] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-25T15:06:42.634] [DEBUG] debug-file - runTaskFromQueue task cost before running: 265 ms 
[2025-06-25T15:06:42.634] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T15:06:42.635] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T15:06:42.635] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-25T15:06:42.636] [DEBUG] debug-file - runTaskFromQueue task cost before running: 267 ms 
[2025-06-25T15:06:42.636] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T15:06:42.637] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.638] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.638] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T15:06:42.640] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-25T15:06:42.640] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.1125335693359375
[2025-06-25T15:06:42.641] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-25T15:06:42.643] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:06:42.643] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:06:42.647] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T15:06:42.671] [DEBUG] debug-file - session manager: binding session. socketId=5_2M1udHyfP2gnbQAAAH, threadId=1@3.
[2025-06-25T15:06:42.669] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -0.8572616577148438
[2025-06-25T15:06:46.246] [DEBUG] debug-file - default@PreviewArkTS watch work[3] failed.
[2025-06-25T15:06:46.246] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T15:06:46.246] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T15:06:46.248] [DEBUG] debug-file - ERROR: stacktrace = Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:06:46.248] [ERROR] debug-file - Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:06:46.266] [WARN] debug-file - BUILD FAILED in 3 s 897 ms 
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-25T15:06:46.266] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-25T15:06:46.269] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T15:06:46.270] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T15:06:46.270] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T15:06:46.270] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T15:06:46.246] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T15:06:46.271] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T15:06:46.276] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:06:46.277] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T15:06:46.277] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:06:46.277] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T15:06:46.278] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:12 ms .
[2025-06-25T15:06:46.294] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:06:46.295] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:06:46.305] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:46.305] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:06:46.305] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:06:46.305] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-25T15:12:25.939] [DEBUG] debug-file - session manager: set active socket. socketId=xr7l_jvJ2AYjq1DCAAAJ
[2025-06-25T15:12:25.962] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:12:25.982] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:12:25.985] [DEBUG] debug-file - Cache service initialization finished in 3 ms 
[2025-06-25T15:12:25.995] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:26.003] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:12:26.003] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:12:26.009] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:12:26.009] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:12:26.009] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:12:26.009] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:12:26.011] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:12:26.015] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:12:26.022] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:12:26.043] [DEBUG] debug-file - Sdk init in 28 ms 
[2025-06-25T15:12:26.065] [DEBUG] debug-file - Project task initialization takes 21 ms 
[2025-06-25T15:12:26.065] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:12:26.065] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:26.065] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:26.071] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:26.076] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:12:26.076] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:12:26.083] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:12:26.083] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:12:26.083] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:12:26.083] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:12:26.083] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:12:26.083] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:12:26.084] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:12:26.086] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T15:12:26.087] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:12:26.087] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:26.087] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:26.127] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 40 ms 
[2025-06-25T15:12:26.129] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:12:26.130] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:12:26.137] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:12:26.139] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:12:26.146] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:12:26.146] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:12:26.146] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:12:26.149] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:12:26.149] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:12:26.152] [DEBUG] debug-file - Configuration phase cost:163 ms 
[2025-06-25T15:12:26.154] [DEBUG] debug-file - Configuration task cost before running: 185 ms 
[2025-06-25T15:12:26.154] [DEBUG] debug-file - Executing task :entry:init
[2025-06-25T15:12:26.154] [DEBUG] debug-file - entry : init cost memory 0.01332855224609375
[2025-06-25T15:12:26.155] [DEBUG] debug-file - runTaskFromQueue task cost before running: 185 ms 
[2025-06-25T15:12:26.155] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-25T15:12:26.155] [DEBUG] debug-file - Executing task ::init
[2025-06-25T15:12:26.155] [DEBUG] debug-file - wallet : init cost memory 0.0118865966796875
[2025-06-25T15:12:26.155] [DEBUG] debug-file - runTaskFromQueue task cost before running: 186 ms 
[2025-06-25T15:12:26.155] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-25T15:12:26.173] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:12:26.178] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-25T15:12:26.181] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:12:26.182] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:12:26.187] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:12:26.188] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:12:26.188] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:12:26.189] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:12:30.119] [DEBUG] debug-file - session manager: set active socket. socketId=wCEVwVDTyfnxWh_YAAAL
[2025-06-25T15:12:30.137] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:12:30.153] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:12:30.156] [DEBUG] debug-file - Cache service initialization finished in 3 ms 
[2025-06-25T15:12:30.166] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:30.172] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:12:30.172] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:12:30.178] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:12:30.178] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:12:30.179] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:12:30.179] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:12:30.180] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:12:30.184] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:12:30.193] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:12:30.210] [DEBUG] debug-file - Sdk init in 26 ms 
[2025-06-25T15:12:30.228] [DEBUG] debug-file - Project task initialization takes 17 ms 
[2025-06-25T15:12:30.228] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:12:30.229] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:30.229] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:12:30.235] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:30.238] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:12:30.238] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:12:30.245] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:12:30.246] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:12:30.246] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:12:30.246] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:12:30.246] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:12:30.246] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:12:30.246] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:12:30.249] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T15:12:30.249] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:12:30.249] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:30.249] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:12:30.263] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 12 ms 
[2025-06-25T15:12:30.264] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:12:30.266] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:12:30.271] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:12:30.272] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:12:30.280] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:12:30.280] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:12:30.281] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:12:30.283] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:12:30.284] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:12:30.286] [DEBUG] debug-file - Configuration phase cost:126 ms 
[2025-06-25T15:12:30.288] [DEBUG] debug-file - Configuration task cost before running: 147 ms 
[2025-06-25T15:12:30.290] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.290] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.292] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T15:12:30.298] [DEBUG] debug-file - entry:default@PreBuild is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5' has been changed.
[2025-06-25T15:12:30.298] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 4 ms .
[2025-06-25T15:12:30.299] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.299] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.304] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T15:12:30.305] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T15:12:30.305] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T15:12:30.305] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.591094970703125
[2025-06-25T15:12:30.305] [DEBUG] debug-file - runTaskFromQueue task cost before running: 165 ms 
[2025-06-25T15:12:30.307] [INFO] debug-file - Finished :entry:default@PreBuild... after 13 ms 
[2025-06-25T15:12:30.310] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.311] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.311] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T15:12:30.313] [DEBUG] debug-file - entry:default@MergeProfile is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5' has been changed.
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-25T15:12:30.313] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.313] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Change app target API version with '50003015'
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Change app minimum API version with '50003015'
[2025-06-25T15:12:30.313] [DEBUG] debug-file - Use cli appEnvironment
[2025-06-25T15:12:30.318] [DEBUG] debug-file - entry : default@MergeProfile cost memory -3.2611083984375
[2025-06-25T15:12:30.318] [DEBUG] debug-file - runTaskFromQueue task cost before running: 178 ms 
[2025-06-25T15:12:30.318] [INFO] debug-file - Finished :entry:default@MergeProfile... after 7 ms 
[2025-06-25T15:12:30.321] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.321] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.321] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T15:12:30.322] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:12:30.323] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-25T15:12:30.324] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.**********
[2025-06-25T15:12:30.324] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-25T15:12:30.326] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.326] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.327] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T15:12:30.327] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.327] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.328] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-25T15:12:30.328] [DEBUG] debug-file - runTaskFromQueue task cost before running: 187 ms 
[2025-06-25T15:12:30.328] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T15:12:30.331] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.331] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.338] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T15:12:30.339] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:12:30.339] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-25T15:12:30.339] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06748199462890625
[2025-06-25T15:12:30.339] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-25T15:12:30.341] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.342] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.343] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T15:12:30.343] [DEBUG] debug-file - entry:default@ProcessProfile is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json' has been changed.
[2025-06-25T15:12:30.343] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-25T15:12:30.343] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.344] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.345] [DEBUG] debug-file - [
  'D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\node.exe',
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\ts2abc.js',
  '--target-api-version',
  '15'
]
[2025-06-25T15:12:30.449] [DEBUG] debug-file - ********
[2025-06-25T15:12:30.452] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.33666229248046875
[2025-06-25T15:12:30.452] [DEBUG] debug-file - runTaskFromQueue task cost before running: 312 ms 
[2025-06-25T15:12:30.452] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 110 ms 
[2025-06-25T15:12:30.455] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.455] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.458] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T15:12:30.463] [DEBUG] debug-file - entry:default@ProcessRouterMap is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5' has been changed.
[2025-06-25T15:12:30.463] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .
[2025-06-25T15:12:30.463] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.463] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.465] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.21598052978515625
[2025-06-25T15:12:30.465] [DEBUG] debug-file - runTaskFromQueue task cost before running: 324 ms 
[2025-06-25T15:12:30.466] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 8 ms 
[2025-06-25T15:12:30.469] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.469] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.470] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T15:12:30.473] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T15:12:30.473] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.473] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.473] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.0774688720703125
[2025-06-25T15:12:30.474] [DEBUG] debug-file - runTaskFromQueue task cost before running: 334 ms 
[2025-06-25T15:12:30.475] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-25T15:12:30.477] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.477] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.480] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T15:12:30.498] [DEBUG] debug-file - entry:default@GenerateLoaderJson is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\temp-router-map.json' has been changed.
[2025-06-25T15:12:30.498] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .
[2025-06-25T15:12:30.503] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -2.4556961059570312
[2025-06-25T15:12:30.504] [DEBUG] debug-file - runTaskFromQueue task cost before running: 363 ms 
[2025-06-25T15:12:30.509] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 24 ms 
[2025-06-25T15:12:30.512] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.512] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.513] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T15:12:30.515] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T15:12:30.522] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources' has been changed.
[2025-06-25T15:12:30.522] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 7 ms .
[2025-06-25T15:12:30.539] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T15:12:30.541] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 123564032,
  heapTotal: 131686400,
  heapUsed: 109718472,
  external: 3141071,
  arrayBuffers: 134972
} os memoryUsage :12.895980834960938
[2025-06-25T15:12:30.586] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:12:30.588] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T15:12:30.590] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 123564032,
  heapTotal: 131686400,
  heapUsed: 109999432,
  external: 3149389,
  arrayBuffers: 143305
} os memoryUsage :12.905906677246094
[2025-06-25T15:12:30.625] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:12:30.628] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***n',
  '-r',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T15:12:30.630] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 123564032,
  heapTotal: 131686400,
  heapUsed: 110330944,
  external: 3149515,
  arrayBuffers: 144310
} os memoryUsage :12.907928466796875
[2025-06-25T15:12:30.695] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:12:30.700] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.3556900024414062
[2025-06-25T15:12:30.701] [DEBUG] debug-file - runTaskFromQueue task cost before running: 560 ms 
[2025-06-25T15:12:30.702] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 186 ms 
[2025-06-25T15:12:30.705] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.706] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.706] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T15:12:30.706] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.706] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.706] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-25T15:12:30.706] [DEBUG] debug-file - runTaskFromQueue task cost before running: 566 ms 
[2025-06-25T15:12:30.706] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T15:12:30.710] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.710] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.711] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T15:12:30.712] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-25T15:12:30.712] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-25T15:12:30.712] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.712] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.719] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.23236083984375
[2025-06-25T15:12:30.719] [DEBUG] debug-file - runTaskFromQueue task cost before running: 579 ms 
[2025-06-25T15:12:30.720] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 9 ms 
[2025-06-25T15:12:30.722] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.722] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.723] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T15:12:30.723] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.723] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.723] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-25T15:12:30.723] [DEBUG] debug-file - runTaskFromQueue task cost before running: 583 ms 
[2025-06-25T15:12:30.723] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T15:12:30.726] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T15:12:30.726] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-25T15:12:30.726] [DEBUG] debug-file - runTaskFromQueue task cost before running: 586 ms 
[2025-06-25T15:12:30.726] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T15:12:30.730] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.730] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.731] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T15:12:30.733] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-25T15:12:30.733] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-25T15:12:30.733] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.733] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.737] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.1370391845703125
[2025-06-25T15:12:30.737] [DEBUG] debug-file - runTaskFromQueue task cost before running: 596 ms 
[2025-06-25T15:12:30.738] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 6 ms 
[2025-06-25T15:12:30.740] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:12:30.740] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:12:30.745] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T15:12:30.765] [DEBUG] debug-file - session manager: binding session. socketId=wCEVwVDTyfnxWh_YAAAL, threadId=1@4.
[2025-06-25T15:12:30.769] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.9585113525390625
[2025-06-25T15:12:34.668] [DEBUG] debug-file - default@PreviewArkTS watch work[4] failed.
[2025-06-25T15:12:34.670] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T15:12:34.670] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T15:12:34.674] [DEBUG] debug-file - ERROR: stacktrace = Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:12:34.674] [ERROR] debug-file - Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:12:34.671] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T15:12:34.703] [WARN] debug-file - BUILD FAILED in 4 s 562 ms 
[2025-06-25T15:12:34.705] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T15:12:34.705] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache from map.
[2025-06-25T15:12:34.705] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T15:12:34.706] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\build-profile.json5 cache by regenerate.
[2025-06-25T15:12:34.707] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json cache by regenerate.
[2025-06-25T15:12:34.707] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-25T15:12:34.707] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:12:34.708] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T15:12:34.708] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T15:12:34.708] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:6 ms .
[2025-06-25T15:12:34.708] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T15:12:34.709] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T15:12:34.709] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache from map.
[2025-06-25T15:12:34.709] [DEBUG] debug-file - Update task entry:default@MergeProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache.
[2025-06-25T15:12:34.709] [DEBUG] debug-file - Incremental task entry:default@MergeProfile post-execution cost:1 ms .
[2025-06-25T15:12:34.709] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-25T15:12:34.710] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-25T15:12:34.710] [DEBUG] debug-file - Update task entry:default@ProcessProfile input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache from map.
[2025-06-25T15:12:34.710] [DEBUG] debug-file - Update task entry:default@ProcessProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache.
[2025-06-25T15:12:34.711] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile post-execution cost:2 ms .
[2025-06-25T15:12:34.713] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T15:12:34.713] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T15:12:34.714] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache from map.
[2025-06-25T15:12:34.714] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:12:34.714] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache.
[2025-06-25T15:12:34.715] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\loader-router-map.json cache.
[2025-06-25T15:12:34.715] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap post-execution cost:4 ms .
[2025-06-25T15:12:34.720] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:12:34.721] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:12:34.721] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache from map.
[2025-06-25T15:12:34.721] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\loader.json cache.
[2025-06-25T15:12:34.721] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson post-execution cost:7 ms .
[2025-06-25T15:12:34.729] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources cache from map.
[2025-06-25T15:12:34.730] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T15:12:34.731] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-25T15:12:34.738] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\r\default cache.
[2025-06-25T15:12:34.739] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:18 ms .
[2025-06-25T15:12:34.739] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-25T15:12:34.740] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-25T15:12:34.741] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-25T15:12:34.741] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-25T15:12:34.741] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-25T15:12:34.742] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-25T15:12:34.746] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T15:12:34.746] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T15:12:34.747] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T15:12:34.747] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T15:12:34.748] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T15:12:34.752] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:12:34.753] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T15:12:34.753] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:12:34.753] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T15:12:34.754] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:12 ms .
[2025-06-25T15:12:34.771] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:12:34.772] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:12:34.781] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:12:34.782] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:12:34.788] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:12:34.788] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-25T15:13:24.064] [DEBUG] debug-file - session manager: set active socket. socketId=Hqlp5UIeaT1ywaLhAAAN
[2025-06-25T15:13:24.092] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:13:24.134] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:13:24.144] [DEBUG] debug-file - Cache service initialization finished in 10 ms 
[2025-06-25T15:13:24.188] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:13:24.203] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:13:24.204] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:13:24.224] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:13:24.224] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:13:24.224] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:13:24.225] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:13:24.233] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:13:24.248] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:13:24.277] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:13:24.346] [DEBUG] debug-file - Sdk init in 96 ms 
[2025-06-25T15:13:24.416] [DEBUG] debug-file - Project task initialization takes 66 ms 
[2025-06-25T15:13:24.416] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:13:24.416] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:13:24.417] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:13:24.439] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:13:24.446] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:13:24.446] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:13:24.470] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:13:24.470] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:13:24.470] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:13:24.470] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:13:24.471] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:13:24.471] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:13:24.471] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:13:24.481] [DEBUG] debug-file - Module entry task initialization takes 3 ms 
[2025-06-25T15:13:24.482] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:13:24.482] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:13:24.482] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:13:24.545] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 60 ms 
[2025-06-25T15:13:24.550] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:13:24.560] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:13:24.589] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:13:24.593] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:13:24.609] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:13:24.609] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:13:24.610] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:13:24.617] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:13:24.617] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:13:24.631] [DEBUG] debug-file - Configuration phase cost:465 ms 
[2025-06-25T15:13:24.640] [DEBUG] debug-file - Configuration task cost before running: 540 ms 
[2025-06-25T15:13:24.648] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.649] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.660] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T15:13:24.688] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 20 ms .
[2025-06-25T15:13:24.688] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.29473876953125
[2025-06-25T15:13:24.696] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-25T15:13:24.703] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.703] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.705] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T15:13:24.710] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .
[2025-06-25T15:13:24.711] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1115875244140625
[2025-06-25T15:13:24.711] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-25T15:13:24.716] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.716] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.719] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T15:13:24.721] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms 
[2025-06-25T15:13:24.724] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .
[2025-06-25T15:13:24.725] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.0991058349609375
[2025-06-25T15:13:24.725] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-25T15:13:24.731] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.732] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.733] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T15:13:24.733] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.733] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.734] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-25T15:13:24.734] [DEBUG] debug-file - runTaskFromQueue task cost before running: 633 ms 
[2025-06-25T15:13:24.734] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T15:13:24.739] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.740] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.758] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T15:13:24.758] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:13:24.760] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .
[2025-06-25T15:13:24.760] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06710052490234375
[2025-06-25T15:13:24.760] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-25T15:13:24.765] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.766] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.769] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T15:13:24.772] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .
[2025-06-25T15:13:24.772] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05767822265625
[2025-06-25T15:13:24.772] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-25T15:13:24.777] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.777] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.781] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T15:13:24.794] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .
[2025-06-25T15:13:24.794] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.1990966796875
[2025-06-25T15:13:24.798] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-25T15:13:24.804] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.804] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.807] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T15:13:24.814] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T15:13:24.815] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.815] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.815] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.06943511962890625
[2025-06-25T15:13:24.819] [DEBUG] debug-file - runTaskFromQueue task cost before running: 719 ms 
[2025-06-25T15:13:24.823] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 5 ms 
[2025-06-25T15:13:24.829] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.830] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.840] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T15:13:24.893] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .
[2025-06-25T15:13:24.893] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7484512329101562
[2025-06-25T15:13:24.913] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-25T15:13:24.921] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:24.921] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:24.924] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T15:13:24.933] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T15:13:24.987] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 52 ms .
[2025-06-25T15:13:24.987] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.49627685546875
[2025-06-25T15:13:24.990] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-25T15:13:25.000] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.001] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.001] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T15:13:25.001] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.001] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.002] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-25T15:13:25.002] [DEBUG] debug-file - runTaskFromQueue task cost before running: 901 ms 
[2025-06-25T15:13:25.002] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T15:13:25.010] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.010] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.011] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T15:13:25.017] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 6 ms .
[2025-06-25T15:13:25.017] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.099456787109375
[2025-06-25T15:13:25.018] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-25T15:13:25.021] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.022] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.023] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T15:13:25.023] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.023] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.023] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-25T15:13:25.023] [DEBUG] debug-file - runTaskFromQueue task cost before running: 923 ms 
[2025-06-25T15:13:25.024] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T15:13:25.029] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T15:13:25.030] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-25T15:13:25.030] [DEBUG] debug-file - runTaskFromQueue task cost before running: 930 ms 
[2025-06-25T15:13:25.030] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T15:13:25.034] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.034] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.035] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T15:13:25.040] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .
[2025-06-25T15:13:25.040] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory -1.65771484375
[2025-06-25T15:13:25.041] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-25T15:13:25.045] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:13:25.045] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:13:25.053] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T15:13:25.108] [DEBUG] debug-file - session manager: binding session. socketId=Hqlp5UIeaT1ywaLhAAAN, threadId=1@5.
[2025-06-25T15:13:25.113] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.9474029541015625
[2025-06-25T15:13:29.119] [DEBUG] debug-file - default@PreviewArkTS watch work[5] failed.
[2025-06-25T15:13:29.120] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T15:13:29.122] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T15:13:29.125] [DEBUG] debug-file - ERROR: stacktrace = Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:13:29.125] [ERROR] debug-file - Error: [31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. [39m
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:13:29.142] [WARN] debug-file - BUILD FAILED in 5 s 42 ms 
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-25T15:13:29.142] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-25T15:13:29.143] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-25T15:13:29.143] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-25T15:13:29.147] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T15:13:29.119] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T15:13:29.150] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T15:13:29.150] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T15:13:29.151] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T15:13:29.152] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T15:13:29.157] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:13:29.158] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T15:13:29.158] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:13:29.158] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T15:13:29.158] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:16 ms .
[2025-06-25T15:13:29.172] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:13:29.172] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:13:29.178] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:13:29.179] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:13:29.183] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:13:29.183] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-25T15:30:04.777] [DEBUG] debug-file - session manager: set active socket. socketId=bCTqOmNg0nJg_muxAAAP
[2025-06-25T15:30:04.790] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T15:30:04.806] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T15:30:04.809] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-25T15:30:04.820] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:30:04.824] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:30:04.825] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:30:04.831] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T15:30:04.831] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T15:30:04.831] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T15:30:04.831] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T15:30:04.833] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T15:30:04.837] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T15:30:04.846] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T15:30:04.866] [DEBUG] debug-file - Sdk init in 28 ms 
[2025-06-25T15:30:04.885] [DEBUG] debug-file - Project task initialization takes 19 ms 
[2025-06-25T15:30:04.885] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:30:04.885] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:30:04.885] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T15:30:04.890] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:30:04.893] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T15:30:04.893] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T15:30:04.899] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T15:30:04.899] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T15:30:04.900] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T15:30:04.900] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T15:30:04.900] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T15:30:04.900] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T15:30:04.900] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T15:30:04.902] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T15:30:04.902] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T15:30:04.903] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:30:04.903] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T15:30:04.915] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 12 ms 
[2025-06-25T15:30:04.916] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T15:30:04.918] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T15:30:04.926] [DEBUG] debug-file - load to the disk finished
[2025-06-25T15:30:04.927] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:30:04.932] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T15:30:04.933] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T15:30:04.933] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T15:30:04.934] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T15:30:04.935] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T15:30:04.937] [DEBUG] debug-file - Configuration phase cost:124 ms 
[2025-06-25T15:30:04.939] [DEBUG] debug-file - Configuration task cost before running: 146 ms 
[2025-06-25T15:30:04.940] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.940] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.943] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T15:30:04.948] [DEBUG] debug-file - entry:default@PreBuild is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json' has been changed.
[2025-06-25T15:30:04.948] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 4 ms .
[2025-06-25T15:30:04.948] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.948] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.952] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T15:30:04.953] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T15:30:04.953] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T15:30:04.953] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.579193115234375
[2025-06-25T15:30:04.953] [DEBUG] debug-file - runTaskFromQueue task cost before running: 160 ms 
[2025-06-25T15:30:04.955] [INFO] debug-file - Finished :entry:default@PreBuild... after 11 ms 
[2025-06-25T15:30:04.957] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.957] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.958] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T15:30:04.959] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-25T15:30:04.959] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.111419677734375
[2025-06-25T15:30:04.960] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-25T15:30:04.961] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.961] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.962] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T15:30:04.963] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:30:04.964] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-25T15:30:04.964] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09746551513671875
[2025-06-25T15:30:04.964] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-25T15:30:04.966] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.966] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.966] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T15:30:04.966] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.966] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.966] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-25T15:30:04.967] [DEBUG] debug-file - runTaskFromQueue task cost before running: 173 ms 
[2025-06-25T15:30:04.967] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T15:30:04.968] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.968] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.974] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T15:30:04.974] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T15:30:04.974] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-25T15:30:04.975] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0663299560546875
[2025-06-25T15:30:04.975] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-25T15:30:04.976] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.976] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.977] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T15:30:04.978] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-25T15:30:04.978] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.0569000244140625
[2025-06-25T15:30:04.978] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-25T15:30:04.981] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.981] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.983] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T15:30:04.987] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .
[2025-06-25T15:30:04.987] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.1991424560546875
[2025-06-25T15:30:04.988] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-25T15:30:04.990] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.990] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.991] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T15:30:04.994] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T15:30:04.994] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.994] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:04.994] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.06964111328125
[2025-06-25T15:30:04.995] [DEBUG] debug-file - runTaskFromQueue task cost before running: 202 ms 
[2025-06-25T15:30:04.996] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-25T15:30:04.998] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:04.998] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.001] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T15:30:05.017] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .
[2025-06-25T15:30:05.017] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.99456787109375
[2025-06-25T15:30:05.023] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-25T15:30:05.025] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.025] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.026] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T15:30:05.028] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T15:30:05.033] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources' has been changed.
[2025-06-25T15:30:05.034] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 5 ms .
[2025-06-25T15:30:05.048] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T15:30:05.050] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 112418816,
  heapTotal: 120414208,
  heapUsed: 112518856,
  external: 3149548,
  arrayBuffers: 143449
} os memoryUsage :13.288135528564453
[2025-06-25T15:30:05.082] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:30:05.084] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T15:30:05.086] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 112517120,
  heapTotal: 120414208,
  heapUsed: 112773472,
  external: 3157866,
  arrayBuffers: 151782
} os memoryUsage :13.289318084716797
[2025-06-25T15:30:05.118] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:30:05.122] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***n',
  '-r',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T15:30:05.123] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 112521216,
  heapTotal: 120414208,
  heapUsed: 113042352,
  external: 3157992,
  arrayBuffers: 152787
} os memoryUsage :13.298091888427734
[2025-06-25T15:30:05.187] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T15:30:05.191] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.36035919189453125
[2025-06-25T15:30:05.191] [DEBUG] debug-file - runTaskFromQueue task cost before running: 398 ms 
[2025-06-25T15:30:05.192] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 163 ms 
[2025-06-25T15:30:05.194] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.194] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.194] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T15:30:05.194] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.194] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.194] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-25T15:30:05.194] [DEBUG] debug-file - runTaskFromQueue task cost before running: 401 ms 
[2025-06-25T15:30:05.194] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T15:30:05.196] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.196] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.197] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T15:30:05.198] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the input file 'D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile' has been changed.
[2025-06-25T15:30:05.198] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .
[2025-06-25T15:30:05.198] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.198] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.202] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.22259521484375
[2025-06-25T15:30:05.202] [DEBUG] debug-file - runTaskFromQueue task cost before running: 409 ms 
[2025-06-25T15:30:05.202] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 6 ms 
[2025-06-25T15:30:05.204] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.205] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.205] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T15:30:05.205] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.205] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.205] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-25T15:30:05.206] [DEBUG] debug-file - runTaskFromQueue task cost before running: 412 ms 
[2025-06-25T15:30:05.206] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T15:30:05.207] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T15:30:05.207] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-25T15:30:05.207] [DEBUG] debug-file - runTaskFromQueue task cost before running: 414 ms 
[2025-06-25T15:30:05.207] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T15:30:05.209] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.209] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.210] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T15:30:05.212] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-25T15:30:05.212] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-25T15:30:05.212] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.212] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.214] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.126556396484375
[2025-06-25T15:30:05.214] [DEBUG] debug-file - runTaskFromQueue task cost before running: 421 ms 
[2025-06-25T15:30:05.215] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 4 ms 
[2025-06-25T15:30:05.216] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T15:30:05.217] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T15:30:05.221] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T15:30:05.238] [DEBUG] debug-file - session manager: binding session. socketId=bCTqOmNg0nJg_muxAAAP, threadId=1@6.
[2025-06-25T15:30:05.239] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.88665771484375
[2025-06-25T15:30:08.830] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-25T15:30:08.831] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-25T15:30:13.642] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.644] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.644] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.647] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.646] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.649] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.647] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.651] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.650] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.654] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.651] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.656] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.653] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.659] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.656] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.661] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.657] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.662] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.659] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.664] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.660] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.665] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.661] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.667] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.662] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.668] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.663] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.670] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.664] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.671] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.665] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.673] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.667] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.674] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.669] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.676] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.670] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.678] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.672] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.679] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.673] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.680] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.674] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.682] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.676] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.683] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.678] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.684] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.679] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.686] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.679] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.688] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.680] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.690] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.682] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.692] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.683] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.693] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.684] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.694] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.685] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.695] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.686] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.696] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.688] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.697] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.689] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.699] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.691] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.700] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.693] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.702] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.694] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.703] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.695] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.705] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.696] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.706] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.697] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.708] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.698] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.710] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.699] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.711] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.700] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.712] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.701] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.713] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.702] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.714] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.702] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.715] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.703] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.716] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.704] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.717] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.706] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.718] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.707] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.719] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.708] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.720] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.709] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.721] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.710] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.722] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.710] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.723] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.711] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.724] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.712] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.725] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.713] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.726] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.713] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.727] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.714] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.728] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.715] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.729] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.717] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.730] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.718] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.731] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.719] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.732] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.719] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.733] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.720] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.735] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.721] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.737] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.722] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.738] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.722] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.740] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.723] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.741] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.724] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.742] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.726] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.744] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.727] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.745] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.728] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.746] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.730] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.747] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.731] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.748] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.732] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.749] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.734] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.750] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.735] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.751] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.736] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.752] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.738] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.754] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.739] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.756] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.741] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.757] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.742] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.759] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.744] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.761] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.745] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.763] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.747] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.765] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.747] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.766] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.748] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.767] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.749] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.769] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.750] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.770] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.750] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.771] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.752] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.772] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.753] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.773] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.754] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.775] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.755] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.778] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.755] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.779] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.756] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.781] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.783] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.785] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.758] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.787] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.759] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.789] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.760] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.790] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.760] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.791] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.761] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.793] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.762] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.794] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.763] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.796] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.764] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.798] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.765] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.799] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.766] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.801] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.767] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.802] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.768] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.803] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.769] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.804] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.769] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.806] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.771] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.808] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.773] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.809] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.774] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.810] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.776] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.812] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.777] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.814] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.779] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.815] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.781] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.816] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.783] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.817] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.784] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.818] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.786] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.819] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.788] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.820] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.790] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.821] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.792] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.822] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.793] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.823] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.795] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.824] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.796] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.825] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.798] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.826] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.800] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.827] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.801] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.829] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.803] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.830] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.805] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.831] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.807] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.833] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.808] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.835] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.809] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.836] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.810] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.837] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.810] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.838] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.812] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.839] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.814] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.841] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.815] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.842] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.816] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.843] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.817] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.845] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.819] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.847] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.820] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.848] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.820] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.849] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.821] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.851] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.822] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.852] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.823] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.853] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.824] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.854] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.825] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.856] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.825] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.857] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.826] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.858] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.827] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.859] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.828] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.861] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.829] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.862] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.830] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.863] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.831] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.864] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.832] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.865] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.834] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.866] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.835] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.867] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.836] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.868] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.837] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.869] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.838] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.870] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.839] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.871] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.841] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.873] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.842] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.874] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.844] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.876] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.845] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.877] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.845] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.878] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.847] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.879] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.848] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.880] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.849] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.882] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.850] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.883] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.851] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.884] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.851] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.885] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.853] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.886] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.855] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.888] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.856] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.889] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.857] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.891] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.858] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.892] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.859] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.893] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.860] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.894] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.861] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.895] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.861] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.896] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.862] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.898] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.863] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.899] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.864] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.900] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.864] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.902] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.865] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.903] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.866] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.904] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.867] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.905] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:13.867] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:13.907] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:14.070] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:14.072] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:14.099] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:14.102] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:14.101] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:14.103] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:14.103] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T15:30:14.104] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T15:30:15.923] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-25T15:30:15.923] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T15:30:15.925] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-25T15:30:15.925] [DEBUG] debug-file - default@PreviewArkTS watch work[6] failed.
[2025-06-25T15:30:15.925] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T15:30:15.925] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T15:30:15.926] [DEBUG] debug-file - ERROR: stacktrace = Error: [31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:69:45
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:72:7
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:141:12
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:185:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:214:44
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:221:43
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:237:39
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:25:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:46:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:65:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:81:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:96:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:114:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:133:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:153:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:172:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:191:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:217:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:236:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:257:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:278:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:14:27
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:42:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:66:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:93:26
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:94:9
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:102:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:134:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:163:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:192:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:227:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:272:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:287:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:15:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:35:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:45:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:65:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:94:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:123:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:152:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:181:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:191:100
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:193:46
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:202:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:228:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:257:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:276:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:287:22
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:18:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:15:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:18:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:24:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:37:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:34:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:37:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:43:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:56:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:53:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:62:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:89:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:72:27
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:86:46
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:95:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:109:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:123:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:137:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:151:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:164:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:161:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:183:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:180:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:183:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:189:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:203:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:218:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:232:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:246:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:268:42
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:50:85
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:24:13
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:46:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:50:77
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:67:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:80:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:77:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:80:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:86:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:99:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:96:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:105:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:122:28
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:126:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:151:20
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:154:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:165:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:175:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:185:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:195:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:216:85
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:212:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:216:77
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:233:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:241:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:34:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:41:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:136:9
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:175:35
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:44:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:95:13
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:32:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:36:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:23:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:123:22
 No overload matches this call.
  The last overload gave the following error.
    Argument of type '"error"' is not assignable to parameter of type '"dataSendProgress"'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:386:20
 Argument of type '{ vertical: number; horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:395:20
 Argument of type '{ vertical: number; horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:563:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:575:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:585:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:24:31
 Cannot find name 'URLSearchParams'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:384:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:400:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:405:15
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:454:31
 Type 'number' is not assignable to type 'LengthMetrics'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:504:27
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:514:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:524:31
 Type 'number' is not assignable to type 'LengthMetrics'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:562:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:238:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:252:20
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:267:20
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:274:16
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:413:16
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:450:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:462:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:497:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:364:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:402:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:416:16
 Property 'margin' does not exist on type 'void'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:420:20
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:447:26
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:490:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:502:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:604:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:617:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:627:8
 Property 'maxHeight' does not exist on type 'ColumnAttribute'. Did you mean 'height'?
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:697:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:95:42
 Property 'getUserProfile' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:142:21
 Property 'changePassword' does not exist on type 'typeof UserApi'. Did you mean 'changePayPassword'?
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:188:21
 Property 'setPaymentPassword' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:233:21
 Property 'setTransactionLimits' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:268:21
 Property 'updateProfile' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:366:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:390:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:423:34
 Property 'email' does not exist on type 'UserInfo'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:424:36
 Property 'email' does not exist on type 'UserInfo'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:551:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:612:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:627:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:684:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:698:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:757:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:769:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:824:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:836:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:345:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:373:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:408:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:490:21
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:601:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:609:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:662:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:675:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:118:7
 Type 'WalletInfo' is not assignable to type 'number'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:159:11
 Expected 4-5 arguments, but got 6.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:167:25
 Property 'bankCardPay' does not exist on type 'typeof WalletApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:356:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:382:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:453:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:686:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:694:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:757:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:770:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:820:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:856:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:145:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:189:20
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:226:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:250:30
 Property 'fromAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:251:56
 Property 'fromAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:254:30
 Property 'toAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:255:56
 Property 'toAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:659:29
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:199:28
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:233:28
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:272:28
 Only UI component syntax can be written in build method.
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:30:15.927] [ERROR] debug-file - Error: [31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:69:45
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:72:7
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:141:12
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:185:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:214:44
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:221:43
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:237:39
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:25:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:46:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:65:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:81:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:96:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:114:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:133:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:153:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:172:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:191:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:217:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:236:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:257:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/UserApi.ets:278:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:14:27
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:42:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:66:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:93:26
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:94:9
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:102:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:134:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:163:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:192:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:227:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:272:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/storage/StorageManager.ets:287:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:15:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:35:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:45:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:65:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:94:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:123:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:152:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:181:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:191:100
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:193:46
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:202:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:228:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:257:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:276:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/WalletApi.ets:287:22
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:18:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:15:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:18:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:24:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:37:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:34:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:37:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:43:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:56:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:53:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:62:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:89:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:72:27
 Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:86:46
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:95:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:109:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:123:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:137:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:151:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:164:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:161:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:183:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:180:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:183:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:189:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:203:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:218:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:232:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:246:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/BankCardApi.ets:268:42
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:50:85
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:24:13
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:46:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:50:77
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:67:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:80:42
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:77:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:80:34
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:86:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:99:16
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:96:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:105:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:122:28
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:126:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:151:20
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:154:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:165:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:175:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:185:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:195:12
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:216:85
 Using "this" inside stand-alone functions is not supported (arkts-no-standalone-this)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:212:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:216:77
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:233:7
 "throw" statements cannot accept values of arbitrary types (arkts-limited-throw)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:241:45
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:34:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:41:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:136:9
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:175:35
 It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:44:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:95:13
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:32:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:36:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:23:57
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/common/http/HttpClient.ets:123:22
 No overload matches this call.
  The last overload gave the following error.
    Argument of type '"error"' is not assignable to parameter of type '"dataSendProgress"'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:386:20
 Argument of type '{ vertical: number; horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:395:20
 Argument of type '{ vertical: number; horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:563:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:575:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/LoginPage.ets:585:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/api/TransactionApi.ets:24:31
 Cannot find name 'URLSearchParams'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:384:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:400:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:405:15
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:454:31
 Type 'number' is not assignable to type 'LengthMetrics'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:504:27
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:514:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:524:31
 Type 'number' is not assignable to type 'LengthMetrics'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets:562:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:238:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:252:20
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:267:20
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:274:16
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:413:16
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:450:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:462:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets:497:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:364:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:402:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:416:16
 Property 'margin' does not exist on type 'void'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:420:20
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:447:26
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:490:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:502:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:604:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:617:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:627:8
 Property 'maxHeight' does not exist on type 'ColumnAttribute'. Did you mean 'height'?
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:697:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:95:42
 Property 'getUserProfile' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:142:21
 Property 'changePassword' does not exist on type 'typeof UserApi'. Did you mean 'changePayPassword'?
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:188:21
 Property 'setPaymentPassword' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:233:21
 Property 'setTransactionLimits' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:268:21
 Property 'updateProfile' does not exist on type 'typeof UserApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:366:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:390:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:423:34
 Property 'email' does not exist on type 'UserInfo'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:424:36
 Property 'email' does not exist on type 'UserInfo'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:551:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:612:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:627:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:684:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:698:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:757:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:769:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:824:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/SettingsPage.ets:836:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:345:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:373:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:408:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:490:21
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:601:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:609:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:662:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/WalletOperationPage.ets:675:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:118:7
 Type 'WalletInfo' is not assignable to type 'number'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:159:11
 Expected 4-5 arguments, but got 6.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:167:25
 Property 'bankCardPay' does not exist on type 'typeof WalletApi'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:356:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:382:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:453:18
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:686:23
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:694:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:757:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:770:24
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:820:22
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/PaymentPage.ets:856:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:145:16
 Argument of type '{ horizontal: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:189:20
 Argument of type '{ horizontal: number; vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'horizontal' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:226:16
 Argument of type '{ vertical: number; }' is not assignable to parameter of type 'Length | Padding | LocalizedPadding'.
  Object literal may only specify known properties, and 'vertical' does not exist in type 'Resource | Padding | LocalizedPadding'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:250:30
 Property 'fromAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:251:56
 Property 'fromAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:254:30
 Property 'toAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:255:56
 Property 'toAccount' does not exist on type 'Transaction'.
[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/BankCardPage.ets:659:29
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:199:28
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:233:28
 Only UI component syntax can be written in build method.[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionDetailPage.ets:272:28
 Only UI component syntax can be written in build method.
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T15:30:15.938] [WARN] debug-file - BUILD FAILED in 11 s 144 ms 
[2025-06-25T15:30:15.939] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\AppScope\app.json5 cache by regenerate.
[2025-06-25T15:30:15.939] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T15:30:15.939] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\build-profile.json5 cache by regenerate.
[2025-06-25T15:30:15.939] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\build-profile.json5 cache by regenerate.
[2025-06-25T15:30:15.940] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile\main_pages.json cache from map.
[2025-06-25T15:30:15.940] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-25T15:30:15.941] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:30:15.941] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\entry\oh-package.json5 cache by regenerate.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\wallet\oh-package.json5 cache by regenerate.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:5 ms .
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-25T15:30:15.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-25T15:30:15.944] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources cache from map.
[2025-06-25T15:30:15.944] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T15:30:15.944] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-25T15:30:15.951] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\r\default cache.
[2025-06-25T15:30:15.951] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:9 ms .
[2025-06-25T15:30:15.952] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\resources\base\profile cache from map.
[2025-06-25T15:30:15.952] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-25T15:30:15.953] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-25T15:30:15.953] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-25T15:30:15.953] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-25T15:30:15.954] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .
[2025-06-25T15:30:15.956] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T15:30:15.957] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T15:30:15.957] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T15:30:15.958] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T15:30:15.958] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\main\ets cache by regenerate.
[2025-06-25T15:30:15.963] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T15:30:15.963] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T15:30:15.963] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T15:30:15.964] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T15:30:15.965] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:11 ms .
[2025-06-25T15:30:15.983] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T15:30:15.983] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T15:30:15.990] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:30:15.990] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T15:30:16.003] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T15:30:16.003] [DEBUG] debug-file - Server currently has 0 watch-worker
