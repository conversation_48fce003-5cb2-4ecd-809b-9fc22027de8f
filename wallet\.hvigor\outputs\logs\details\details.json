{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 11897500, "PreCheckSyscap": 250900, "PreviewProcessResource": 3028300, "PreviewCompileResource": 163737300, "PreviewHookCompileResource": 245000, "CopyPreviewProfile": 5590200, "ReplacePreviewerPage": 356000, "buildPreviewerResource": 227800, "PreviewUpdateAssets": 4628600}}, "TOTAL_TIME": 11143846300, "BUILD_ID": "202506251530047930", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750836615925"}}}