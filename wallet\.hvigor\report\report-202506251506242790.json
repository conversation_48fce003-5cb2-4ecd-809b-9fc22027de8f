{"version": "2.0", "ppid": 12876, "events": [{"head": {"id": "47262f1b-6fa4-432d-9a0f-a563adce1b23", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338490200200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4114974f-8d08-48e1-aac8-f91015b0ecc7", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21545247053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f54f70-961f-4519-8d14-c4170131082a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21545291616100, "endTime": 21545293089400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "02c9ae26-6e49-4c16-b845-2ccf14eba6a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02c9ae26-6e49-4c16-b845-2ccf14eba6a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21545291616100, "endTime": 21545293089400}, "additional": {"logType": "info", "children": [], "durationId": "d7f54f70-961f-4519-8d14-c4170131082a"}}, {"head": {"id": "26dabc19-d7ae-4d52-960c-9c41aac191b9", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21556109005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11cd9a0f-b072-4c5e-a2e6-75fe89673bdb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21556113243600, "endTime": 21556113288700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "de85e11b-a5e4-4216-82b9-b4c6ecc27214"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de85e11b-a5e4-4216-82b9-b4c6ecc27214", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21556113243600, "endTime": 21556113288700}, "additional": {"logType": "info", "children": [], "durationId": "11cd9a0f-b072-4c5e-a2e6-75fe89673bdb"}}, {"head": {"id": "377073d6-0a15-4e77-8af7-d05062dc181d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21626124904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d63a6ae-a821-493d-b67a-5e1b7a32e836", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21626127397100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69195a57-f465-4aad-9d8b-e8dd98966195", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627016348700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627046724500, "endTime": 21627560362300}, "additional": {"children": ["2c7d418a-f761-4d19-a0ff-7b1351dce456", "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "260fe5db-0a6e-4a36-8297-5827e96adaff", "66045da6-6b5d-4edb-900f-a166af9af2d8", "a6a665f6-3243-4bbe-bcb3-0d61d5f3de77", "f8620fdc-197f-42ca-b89a-deeff035c3a9", "424765ba-3730-4946-b253-61cefedbfde0"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c7d418a-f761-4d19-a0ff-7b1351dce456", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627046729400, "endTime": 21627083918700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "7658e08e-e9de-4509-8750-df4a6c9ad227"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627084012800, "endTime": 21627556727800}, "additional": {"children": ["b7eb9879-c036-4d3d-99ad-7a8fac06550a", "1ab2827e-9a80-45bd-a1b5-0e21a54924b0", "a1e81971-7f07-40f0-97bf-a68cebada4f5", "17df8ab1-6191-4d07-8c34-4251261b2be1", "4d9f8673-5990-4a7c-ac18-d9ae1d27d5d8", "21af2493-2905-4850-9ada-0920b7ea4ff8", "c0824f7f-098a-4a23-bcc9-65bd7e8e0097", "9fad74d0-f518-4f73-a8cf-2840e6ce64d2", "92a7df5f-f57d-4bc4-b5c1-b18f8c682f67"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "260fe5db-0a6e-4a36-8297-5827e96adaff", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627556784700, "endTime": 21627560296300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "e52099b5-5978-4ccd-96de-d8671350bb2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66045da6-6b5d-4edb-900f-a166af9af2d8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627560309900, "endTime": 21627560350400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "b5eef870-a886-47c6-b2e4-42e7fe3613a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6a665f6-3243-4bbe-bcb3-0d61d5f3de77", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627060018700, "endTime": 21627060107400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "9e0f13ea-54ee-4087-a228-a0d178d4ad22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e0f13ea-54ee-4087-a228-a0d178d4ad22", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627060018700, "endTime": 21627060107400}, "additional": {"logType": "info", "children": [], "durationId": "a6a665f6-3243-4bbe-bcb3-0d61d5f3de77", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "f8620fdc-197f-42ca-b89a-deeff035c3a9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627070377700, "endTime": 21627070399600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "a9f38172-54de-4fcb-ad2b-a90e866e47a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f38172-54de-4fcb-ad2b-a90e866e47a5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627070377700, "endTime": 21627070399600}, "additional": {"logType": "info", "children": [], "durationId": "f8620fdc-197f-42ca-b89a-deeff035c3a9", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "859497af-5819-4720-ab0f-5d730f43e9f8", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627070612900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6295d7ac-5bc7-4082-b6a7-d46b437f2e7e", "name": "Cache service initialization finished in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627083667100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7658e08e-e9de-4509-8750-df4a6c9ad227", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627046729400, "endTime": 21627083918700}, "additional": {"logType": "info", "children": [], "durationId": "2c7d418a-f761-4d19-a0ff-7b1351dce456", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "b7eb9879-c036-4d3d-99ad-7a8fac06550a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627093835000, "endTime": 21627093865500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "97391de9-1612-40da-9ce8-ab721fc6d6ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ab2827e-9a80-45bd-a1b5-0e21a54924b0", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627093916300, "endTime": 21627105822100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "66077c18-9b8d-4e54-9df5-a5bf8677a4b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1e81971-7f07-40f0-97bf-a68cebada4f5", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627105842100, "endTime": 21627359104500}, "additional": {"children": ["fc92f540-cdff-46f3-8192-dfaec0b4992c", "7120372a-c3a0-4915-abc6-c18bffb0014a", "8ad8de95-8e0e-4436-a9a9-46379e883859"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "828ed2c7-f7a1-4966-9e1a-c54012c70434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17df8ab1-6191-4d07-8c34-4251261b2be1", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627359126200, "endTime": 21627406777800}, "additional": {"children": ["f0d35408-1623-4c2c-872a-435e4de79f8b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "92bb8c48-6750-4bac-8ac0-315fc00e39a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d9f8673-5990-4a7c-ac18-d9ae1d27d5d8", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627406791400, "endTime": 21627466000400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "7ab8647f-1c5d-4d33-9c1b-9788ed25d099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21af2493-2905-4850-9ada-0920b7ea4ff8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627470150100, "endTime": 21627503911900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "15a54398-89e3-48ec-af15-6b8371462c18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0824f7f-098a-4a23-bcc9-65bd7e8e0097", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627503988500, "endTime": 21627556257400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "369c5c2b-a14d-424a-bbe6-a3ee8822e3f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fad74d0-f518-4f73-a8cf-2840e6ce64d2", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627556329900, "endTime": 21627556690900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "07807f94-43b0-454c-84c7-acc2bdfeef5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97391de9-1612-40da-9ce8-ab721fc6d6ea", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627093835000, "endTime": 21627093865500}, "additional": {"logType": "info", "children": [], "durationId": "b7eb9879-c036-4d3d-99ad-7a8fac06550a", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "66077c18-9b8d-4e54-9df5-a5bf8677a4b5", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627093916300, "endTime": 21627105822100}, "additional": {"logType": "info", "children": [], "durationId": "1ab2827e-9a80-45bd-a1b5-0e21a54924b0", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "fc92f540-cdff-46f3-8192-dfaec0b4992c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627108118600, "endTime": 21627108197000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1e81971-7f07-40f0-97bf-a68cebada4f5", "logId": "6d1cc1e8-9a13-452a-90d2-aa28b3a65fff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d1cc1e8-9a13-452a-90d2-aa28b3a65fff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627108118600, "endTime": 21627108197000}, "additional": {"logType": "info", "children": [], "durationId": "fc92f540-cdff-46f3-8192-dfaec0b4992c", "parent": "828ed2c7-f7a1-4966-9e1a-c54012c70434"}}, {"head": {"id": "7120372a-c3a0-4915-abc6-c18bffb0014a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627114224300, "endTime": 21627357715600}, "additional": {"children": ["23ed1989-667f-41e7-9ec5-6017db207dd1", "a55e432e-6e22-4eb8-8a93-69b76740c770"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1e81971-7f07-40f0-97bf-a68cebada4f5", "logId": "35ea74ca-c77a-46bb-969c-917f3c1612f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23ed1989-667f-41e7-9ec5-6017db207dd1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627114228200, "endTime": 21627143931600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7120372a-c3a0-4915-abc6-c18bffb0014a", "logId": "07734a6e-6688-4d2f-9349-dd54f43dc851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a55e432e-6e22-4eb8-8a93-69b76740c770", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627143959100, "endTime": 21627357679800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7120372a-c3a0-4915-abc6-c18bffb0014a", "logId": "359caa3f-e07c-47f3-8ab3-47b5cf89a9ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2884a9c-b06b-4575-8521-d152c0413737", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627114250500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af1cebc-85a9-4cf2-a694-0870f10f70a3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627143437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07734a6e-6688-4d2f-9349-dd54f43dc851", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627114228200, "endTime": 21627143931600}, "additional": {"logType": "info", "children": [], "durationId": "23ed1989-667f-41e7-9ec5-6017db207dd1", "parent": "35ea74ca-c77a-46bb-969c-917f3c1612f1"}}, {"head": {"id": "fad8d32a-cdc3-4e6e-b231-d3cf85ac4422", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627143982500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e6dc68-0574-4cc9-96a0-8ec6820ab3f2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627173031100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c494bf0f-70dc-4c64-8d04-48071883eaad", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627173301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7903515-aa6e-4251-99b8-28b8d50eb1bf", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627173745400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acdf1f90-786d-4419-b104-5d5aa1ce0703", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627174252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d54317-dea4-48d5-84dc-8ac06c47cfac", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627180527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef03c946-754a-4a06-8f26-00e4671b8b81", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627193359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c655a1-d026-4226-8f53-925495c1b7e6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627220902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4dcca3b-1f2c-4c53-9543-702870389426", "name": "Sdk init in 90 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627286241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f8d525-3779-4e91-95a0-2f263b44e72b", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627286540300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "markType": "other"}}, {"head": {"id": "29ea1f65-a942-4c73-8939-a215210463f5", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627286573800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "markType": "other"}}, {"head": {"id": "f5622b3f-43ee-434e-a632-b57192f54bcd", "name": "Project task initialization takes 66 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627356535100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f3e134-3694-4da3-9329-f6869d273850", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627356818900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e1f91f-5cac-4aaf-968d-6e0ef092c279", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627357443700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed03d6ef-1459-4e33-b034-1ff6ca90dc6d", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627357588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "359caa3f-e07c-47f3-8ab3-47b5cf89a9ea", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627143959100, "endTime": 21627357679800}, "additional": {"logType": "info", "children": [], "durationId": "a55e432e-6e22-4eb8-8a93-69b76740c770", "parent": "35ea74ca-c77a-46bb-969c-917f3c1612f1"}}, {"head": {"id": "35ea74ca-c77a-46bb-969c-917f3c1612f1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627114224300, "endTime": 21627357715600}, "additional": {"logType": "info", "children": ["07734a6e-6688-4d2f-9349-dd54f43dc851", "359caa3f-e07c-47f3-8ab3-47b5cf89a9ea"], "durationId": "7120372a-c3a0-4915-abc6-c18bffb0014a", "parent": "828ed2c7-f7a1-4966-9e1a-c54012c70434"}}, {"head": {"id": "8ad8de95-8e0e-4436-a9a9-46379e883859", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627359061300, "endTime": 21627359082100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1e81971-7f07-40f0-97bf-a68cebada4f5", "logId": "69834c04-37d2-41b5-bdc6-10b27649157b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69834c04-37d2-41b5-bdc6-10b27649157b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627359061300, "endTime": 21627359082100}, "additional": {"logType": "info", "children": [], "durationId": "8ad8de95-8e0e-4436-a9a9-46379e883859", "parent": "828ed2c7-f7a1-4966-9e1a-c54012c70434"}}, {"head": {"id": "828ed2c7-f7a1-4966-9e1a-c54012c70434", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627105842100, "endTime": 21627359104500}, "additional": {"logType": "info", "children": ["6d1cc1e8-9a13-452a-90d2-aa28b3a65fff", "35ea74ca-c77a-46bb-969c-917f3c1612f1", "69834c04-37d2-41b5-bdc6-10b27649157b"], "durationId": "a1e81971-7f07-40f0-97bf-a68cebada4f5", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "f0d35408-1623-4c2c-872a-435e4de79f8b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627360535600, "endTime": 21627406754900}, "additional": {"children": ["cd11438f-1218-47c3-bc74-883663650d7f", "902eff7d-6639-42b4-acc0-e8373d7f047c", "f293ab48-2ea5-4129-824b-622ab3a0d113"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "17df8ab1-6191-4d07-8c34-4251261b2be1", "logId": "84798287-8feb-4081-b60a-6f6aa24a3c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd11438f-1218-47c3-bc74-883663650d7f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627367457300, "endTime": 21627367484300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0d35408-1623-4c2c-872a-435e4de79f8b", "logId": "2c0568ec-f280-4b8f-9742-98f902c86576"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c0568ec-f280-4b8f-9742-98f902c86576", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627367457300, "endTime": 21627367484300}, "additional": {"logType": "info", "children": [], "durationId": "cd11438f-1218-47c3-bc74-883663650d7f", "parent": "84798287-8feb-4081-b60a-6f6aa24a3c1b"}}, {"head": {"id": "902eff7d-6639-42b4-acc0-e8373d7f047c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627372057700, "endTime": 21627402919700}, "additional": {"children": ["787e3b2c-f97a-48b2-8c4b-d0eb1594c1e2", "35e13ec6-d455-450d-8dcd-e482be96eb84"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0d35408-1623-4c2c-872a-435e4de79f8b", "logId": "59bda42b-9ac9-4b6a-af19-1e935420928d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "787e3b2c-f97a-48b2-8c4b-d0eb1594c1e2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627372060100, "endTime": 21627377951000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "902eff7d-6639-42b4-acc0-e8373d7f047c", "logId": "65cbb270-82cb-4a83-9c62-ad86e5d9bb2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e13ec6-d455-450d-8dcd-e482be96eb84", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627377976900, "endTime": 21627402900700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "902eff7d-6639-42b4-acc0-e8373d7f047c", "logId": "a6bce1a0-0b8a-45ff-a6c1-5c953696e2cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5ec9049-aa7c-4b4f-b52e-de62a257c556", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627372074500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56425863-9977-4157-bdc6-7458d49cce31", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627377696000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65cbb270-82cb-4a83-9c62-ad86e5d9bb2f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627372060100, "endTime": 21627377951000}, "additional": {"logType": "info", "children": [], "durationId": "787e3b2c-f97a-48b2-8c4b-d0eb1594c1e2", "parent": "59bda42b-9ac9-4b6a-af19-1e935420928d"}}, {"head": {"id": "075d0078-cc5d-4767-bc61-2fa36f6cd981", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627378001700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693ea34c-a32d-4854-9fd2-566333c0ca66", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627394209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb23a65-0f06-4420-b264-85bc73a2aa63", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627394507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28e8cf5c-789b-4f54-920d-bebe23083ccf", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627395259700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4032c280-e717-42c2-a662-a6d44d2a4942", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627395651300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c79a0f-c03e-4263-ad0b-8853c8af8ac4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627395792100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec7454a7-25e6-4868-87f8-6878dd430566", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627395892900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd9b6ea1-cb67-471e-a44d-1a296cdf3c25", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627396010700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4827a22-5fe6-4889-bff8-b39bf26cf246", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627402342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ee1697-691c-43ea-bf5d-1df21beda985", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627402585500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff28566a-58c3-4726-b87c-5b0487db1afb", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627402710100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c868976d-fce6-4243-96e8-fcaec73c862c", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627402801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6bce1a0-0b8a-45ff-a6c1-5c953696e2cd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627377976900, "endTime": 21627402900700}, "additional": {"logType": "info", "children": [], "durationId": "35e13ec6-d455-450d-8dcd-e482be96eb84", "parent": "59bda42b-9ac9-4b6a-af19-1e935420928d"}}, {"head": {"id": "59bda42b-9ac9-4b6a-af19-1e935420928d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627372057700, "endTime": 21627402919700}, "additional": {"logType": "info", "children": ["65cbb270-82cb-4a83-9c62-ad86e5d9bb2f", "a6bce1a0-0b8a-45ff-a6c1-5c953696e2cd"], "durationId": "902eff7d-6639-42b4-acc0-e8373d7f047c", "parent": "84798287-8feb-4081-b60a-6f6aa24a3c1b"}}, {"head": {"id": "f293ab48-2ea5-4129-824b-622ab3a0d113", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627406680400, "endTime": 21627406721900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0d35408-1623-4c2c-872a-435e4de79f8b", "logId": "18ceb53a-990c-4d93-abd6-06b7a427288e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18ceb53a-990c-4d93-abd6-06b7a427288e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627406680400, "endTime": 21627406721900}, "additional": {"logType": "info", "children": [], "durationId": "f293ab48-2ea5-4129-824b-622ab3a0d113", "parent": "84798287-8feb-4081-b60a-6f6aa24a3c1b"}}, {"head": {"id": "84798287-8feb-4081-b60a-6f6aa24a3c1b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627360535600, "endTime": 21627406754900}, "additional": {"logType": "info", "children": ["2c0568ec-f280-4b8f-9742-98f902c86576", "59bda42b-9ac9-4b6a-af19-1e935420928d", "18ceb53a-990c-4d93-abd6-06b7a427288e"], "durationId": "f0d35408-1623-4c2c-872a-435e4de79f8b", "parent": "92bb8c48-6750-4bac-8ac0-315fc00e39a0"}}, {"head": {"id": "92bb8c48-6750-4bac-8ac0-315fc00e39a0", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627359126200, "endTime": 21627406777800}, "additional": {"logType": "info", "children": ["84798287-8feb-4081-b60a-6f6aa24a3c1b"], "durationId": "17df8ab1-6191-4d07-8c34-4251261b2be1", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "659c98cf-12e3-4fb4-9ee3-09f562993911", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627463001300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d07e6cf-7323-4c1f-9d7a-ea623b4d0d2a", "name": "hvigorfile, resolve hvigorfile dependencies in 59 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627465714300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ab8647f-1c5d-4d33-9c1b-9788ed25d099", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627406791400, "endTime": 21627466000400}, "additional": {"logType": "info", "children": [], "durationId": "4d9f8673-5990-4a7c-ac18-d9ae1d27d5d8", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "92a7df5f-f57d-4bc4-b5c1-b18f8c682f67", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627469364200, "endTime": 21627470092300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "logId": "d7f9cdc5-9a0a-4d9b-96b7-db3dca3bdc6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9699c6f8-ec1f-461f-8a6b-4e7bb22c4516", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627469459100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f9cdc5-9a0a-4d9b-96b7-db3dca3bdc6e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627469364200, "endTime": 21627470092300}, "additional": {"logType": "info", "children": [], "durationId": "92a7df5f-f57d-4bc4-b5c1-b18f8c682f67", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "b57ec713-2738-4e88-8029-5acf4bdb2f1a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627477875500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61846813-41d7-4b42-8e84-8b9ba91e4d8b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627499876100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a54398-89e3-48ec-af15-6b8371462c18", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627470150100, "endTime": 21627503911900}, "additional": {"logType": "info", "children": [], "durationId": "21af2493-2905-4850-9ada-0920b7ea4ff8", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "995472d2-5f91-4c07-98af-a3f7646f898b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627504079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e62939-dd35-401b-867b-2cc1e6f791c0", "name": "<PERSON><PERSON>le wallet Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627532145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab9d03b-36c7-4671-85e4-57728aeb1c07", "name": "Module wallet's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627532460300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5dd379a-6415-4308-ba39-dbba0aa45adc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627533450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec0ad8d-7366-4ba7-96a0-223577a06acb", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627546631500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44877625-9020-4863-b81b-ff113f61000a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627547056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "369c5c2b-a14d-424a-bbe6-a3ee8822e3f6", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627503988500, "endTime": 21627556257400}, "additional": {"logType": "info", "children": [], "durationId": "c0824f7f-098a-4a23-bcc9-65bd7e8e0097", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "bb00c811-cafe-4e82-aa96-d43b04f9b0d0", "name": "Configuration phase cost:463 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627556407600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07807f94-43b0-454c-84c7-acc2bdfeef5c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627556329900, "endTime": 21627556690900}, "additional": {"logType": "info", "children": [], "durationId": "9fad74d0-f518-4f73-a8cf-2840e6ce64d2", "parent": "30ab275d-603f-4bd7-8cf6-81565bb0c286"}}, {"head": {"id": "30ab275d-603f-4bd7-8cf6-81565bb0c286", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627084012800, "endTime": 21627556727800}, "additional": {"logType": "info", "children": ["97391de9-1612-40da-9ce8-ab721fc6d6ea", "66077c18-9b8d-4e54-9df5-a5bf8677a4b5", "828ed2c7-f7a1-4966-9e1a-c54012c70434", "92bb8c48-6750-4bac-8ac0-315fc00e39a0", "7ab8647f-1c5d-4d33-9c1b-9788ed25d099", "15a54398-89e3-48ec-af15-6b8371462c18", "369c5c2b-a14d-424a-bbe6-a3ee8822e3f6", "07807f94-43b0-454c-84c7-acc2bdfeef5c", "d7f9cdc5-9a0a-4d9b-96b7-db3dca3bdc6e"], "durationId": "2733122a-f7c7-4118-a12e-08cd0e9f30d0", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "424765ba-3730-4946-b253-61cefedbfde0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627560235200, "endTime": 21627560269600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12d8ad88-201e-48cc-8e08-d9167c51be4a", "logId": "bdb1da38-7597-4df5-b189-30b2b666be34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdb1da38-7597-4df5-b189-30b2b666be34", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627560235200, "endTime": 21627560269600}, "additional": {"logType": "info", "children": [], "durationId": "424765ba-3730-4946-b253-61cefedbfde0", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "e52099b5-5978-4ccd-96de-d8671350bb2e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627556784700, "endTime": 21627560296300}, "additional": {"logType": "info", "children": [], "durationId": "260fe5db-0a6e-4a36-8297-5827e96adaff", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "b5eef870-a886-47c6-b2e4-42e7fe3613a4", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627560309900, "endTime": 21627560350400}, "additional": {"logType": "info", "children": [], "durationId": "66045da6-6b5d-4edb-900f-a166af9af2d8", "parent": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5"}}, {"head": {"id": "259504ee-bbe7-48e2-aa50-f3dbd23c01a5", "name": "init", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627046724500, "endTime": 21627560362300}, "additional": {"logType": "info", "children": ["7658e08e-e9de-4509-8750-df4a6c9ad227", "30ab275d-603f-4bd7-8cf6-81565bb0c286", "e52099b5-5978-4ccd-96de-d8671350bb2e", "b5eef870-a886-47c6-b2e4-42e7fe3613a4", "9e0f13ea-54ee-4087-a228-a0d178d4ad22", "a9f38172-54de-4fcb-ad2b-a90e866e47a5", "bdb1da38-7597-4df5-b189-30b2b666be34"], "durationId": "12d8ad88-201e-48cc-8e08-d9167c51be4a"}}, {"head": {"id": "a5574659-843d-4e16-8cdc-82b5cf275a67", "name": "Configuration task cost before running: 526 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627560640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda57226-8608-4f1d-952d-574a4326551e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627574234500, "endTime": 21627617239100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed."], "detailId": "78d45df0-c5e9-4c9d-8945-cfa79c17f504", "logId": "6492c3bd-3406-4ecf-a64f-ea27475fe225"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78d45df0-c5e9-4c9d-8945-cfa79c17f504", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627564976800}, "additional": {"logType": "detail", "children": [], "durationId": "dda57226-8608-4f1d-952d-574a4326551e"}}, {"head": {"id": "3a67e7a9-769e-4716-87ff-59f32674c8ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627566764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b69d29e8-ee97-473d-b32e-28152ef1e13c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627566953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10560d17-1bf1-4257-8463-697c1468df88", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627574262600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc95fcb-6928-4209-b43c-b93d38bcb0f2", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627595775400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a20a16-25d6-4fe7-a6c3-f68a2f54ef41", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627596337200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb40ebb7-6ac8-4f17-94bf-c2d905f03408", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627596697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56d79c7-5256-4b25-96c0-2ed0f0fa7cb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627596899200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ad3d5a-f315-4f4b-86fc-491a493cdaf5", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627613649300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456631d4-e876-4338-80c0-455c0ebd0cd1", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627615765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781e5122-54bd-4c79-8146-a58dc52cd0e0", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627616360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395bb746-1fed-4414-aea4-e57f11db6aca", "name": "entry : default@PreBuild cost memory 0.5323638916015625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627616767600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c717a21-e5b4-4681-ad44-32a0a857e151", "name": "runTaskFromQueue task cost before running: 582 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627617025400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6492c3bd-3406-4ecf-a64f-ea27475fe225", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627574234500, "endTime": 21627617239100, "totalTime": 42721300}, "additional": {"logType": "info", "children": [], "durationId": "dda57226-8608-4f1d-952d-574a4326551e"}}, {"head": {"id": "ed97cc53-dd11-46ec-971d-d948d7e1e7e1", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627628725900, "endTime": 21627635643200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "66d9b238-c6fe-4f71-af81-9f8afda03d80", "logId": "ed2d2773-4219-49e8-a196-5b248501a089"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66d9b238-c6fe-4f71-af81-9f8afda03d80", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627626391600}, "additional": {"logType": "detail", "children": [], "durationId": "ed97cc53-dd11-46ec-971d-d948d7e1e7e1"}}, {"head": {"id": "7b601f80-ce33-435f-84cf-3b50e3e627a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627627320400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d79c21d6-da70-4034-9b6c-f345d566c505", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627627478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d9fe78-224a-46c7-bc76-4bcd0a91b773", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627628741800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b354652c-a6eb-4670-823b-8bd93fe57122", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627635043400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d334f0-9832-4ef9-b634-f7c58361b34d", "name": "entry : default@MergeProfile cost memory 0.1116485595703125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627635344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed2d2773-4219-49e8-a196-5b248501a089", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627628725900, "endTime": 21627635643200}, "additional": {"logType": "info", "children": [], "durationId": "ed97cc53-dd11-46ec-971d-d948d7e1e7e1"}}, {"head": {"id": "7be2c632-7118-47e6-be2e-650932a4292f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627643052300, "endTime": 21627652863600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a94bf157-5232-4ad5-9e18-8a509bd02360", "logId": "f88c79ab-49a7-44f1-9ad8-9c1cfe6a7ba8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a94bf157-5232-4ad5-9e18-8a509bd02360", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627639441800}, "additional": {"logType": "detail", "children": [], "durationId": "7be2c632-7118-47e6-be2e-650932a4292f"}}, {"head": {"id": "4cd5af7f-efa0-4f7d-b47d-f42f18e1031b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627640898700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e1416f-e044-4018-91dc-23f801673834", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627641117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6faad7cc-3129-4510-b9f3-c8b057d586ff", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627643073600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a007512-6a97-405b-a75d-0bd414868eb7", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627646523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e470e4d-b9c7-43d8-bcb2-9c6c68b6e673", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627652432600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add8d28e-58be-4a02-85ef-52a5b08a8339", "name": "entry : default@CreateBuildProfile cost memory -1.6363372802734375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627652728000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88c79ab-49a7-44f1-9ad8-9c1cfe6a7ba8", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627643052300, "endTime": 21627652863600}, "additional": {"logType": "info", "children": [], "durationId": "7be2c632-7118-47e6-be2e-650932a4292f"}}, {"head": {"id": "b4a1ad09-9bb8-45f2-9b3c-5038a9fa0df6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659304200, "endTime": 21627659972700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e0925b11-92ed-40dd-9f59-256f45b01b8f", "logId": "dae27ef2-d802-428a-8186-4ecc89dfea07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0925b11-92ed-40dd-9f59-256f45b01b8f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627656470200}, "additional": {"logType": "detail", "children": [], "durationId": "b4a1ad09-9bb8-45f2-9b3c-5038a9fa0df6"}}, {"head": {"id": "cba9dc42-9928-47bf-8024-1086d1ae2acb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627657523400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b451307-8a6a-4edb-91c0-bc071f488e20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627657697400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c488d977-bb29-47af-9871-7f0ee5799d48", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659324800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d8cf65-b3da-4b61-b327-7fcec9b745e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659540100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68936682-1607-42b5-91b6-01decdc1a0a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659627400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d95534-cd52-4214-90a1-82c3951aec75", "name": "entry : default@PreCheckSyscap cost memory 0.037994384765625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8850288e-457f-4fd7-b9ae-24adcd8187c5", "name": "runTaskFromQueue task cost before running: 625 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae27ef2-d802-428a-8186-4ecc89dfea07", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627659304200, "endTime": 21627659972700, "totalTime": 531200}, "additional": {"logType": "info", "children": [], "durationId": "b4a1ad09-9bb8-45f2-9b3c-5038a9fa0df6"}}, {"head": {"id": "a568141b-5632-4f2a-994b-f3d02f949539", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627685936700, "endTime": 21627690219000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "278ec772-6eeb-44cf-9352-a29436b05941", "logId": "8e419606-dcb5-4a94-9e2e-afec95c77fd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "278ec772-6eeb-44cf-9352-a29436b05941", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627664246900}, "additional": {"logType": "detail", "children": [], "durationId": "a568141b-5632-4f2a-994b-f3d02f949539"}}, {"head": {"id": "39e22926-359b-48f8-9df9-b969d7e75f42", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627665335200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bcf3000-17ac-4e98-acff-e9a73f49021d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627665510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66df5d1-0b78-4015-a331-672bc60a88c9", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627685975800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f16768-492e-42d9-be55-3a8d19fabdc1", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627686649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bd0a671-d8f0-4db5-b8e0-e257f1c2b28f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627689559900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ef92c0-c385-414c-a78f-f805b7145602", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06836700439453125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627689965600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e419606-dcb5-4a94-9e2e-afec95c77fd6", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627685936700, "endTime": 21627690219000}, "additional": {"logType": "info", "children": [], "durationId": "a568141b-5632-4f2a-994b-f3d02f949539"}}, {"head": {"id": "b61846ed-7e77-473c-a45d-9e7f3bd54412", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627701799600, "endTime": 21627705852100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6531b47e-c2ce-4925-bea8-37d0eb743b3b", "logId": "d23c2aca-76ba-46da-b2e8-69b45c787527"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6531b47e-c2ce-4925-bea8-37d0eb743b3b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627695996400}, "additional": {"logType": "detail", "children": [], "durationId": "b61846ed-7e77-473c-a45d-9e7f3bd54412"}}, {"head": {"id": "2452a9c6-1987-4ddc-9644-586e2929338a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627697476500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6662a6f-0877-4d4b-841d-17316887b869", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627697717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c907ef-4927-443e-8d95-af7cc7a9c5d2", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627701828200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703b19ed-defe-4995-9be3-15c3725c2683", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627705405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "871810b8-b802-4027-9573-c21e1b259074", "name": "entry : default@ProcessProfile cost memory 0.06578826904296875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627705686400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23c2aca-76ba-46da-b2e8-69b45c787527", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627701799600, "endTime": 21627705852100}, "additional": {"logType": "info", "children": [], "durationId": "b61846ed-7e77-473c-a45d-9e7f3bd54412"}}, {"head": {"id": "d32f60df-8690-4996-a107-db3bb557f44c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627715979100, "endTime": 21627727805700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eca166ca-62a9-4f2b-8304-8deb36e7a530", "logId": "fa023c6a-8b1d-4b0e-aa89-879aac3d816f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eca166ca-62a9-4f2b-8304-8deb36e7a530", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627710598400}, "additional": {"logType": "detail", "children": [], "durationId": "d32f60df-8690-4996-a107-db3bb557f44c"}}, {"head": {"id": "de5c3346-556e-4213-b154-5238148b4a31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627711912600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99cd71ef-93af-49c5-b406-3adde51085c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627712122300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f717ca-e23e-4278-a0c1-b560c89020dc", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627716051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b4e43b-4452-4998-b2db-3e01a72dd065", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627727436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d46793-73a1-439a-8369-00e0f3c8d32a", "name": "entry : default@ProcessRouterMap cost memory 0.21306610107421875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627727667800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa023c6a-8b1d-4b0e-aa89-879aac3d816f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627715979100, "endTime": 21627727805700}, "additional": {"logType": "info", "children": [], "durationId": "d32f60df-8690-4996-a107-db3bb557f44c"}}, {"head": {"id": "a4f6eec1-633c-4ec7-ad5e-5bc7b68dceb1", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627740702400, "endTime": 21627752322200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "52778a23-967c-42ee-bd45-85e172ff8329", "logId": "02df2426-00d7-4c2b-9c1e-68d255754ad7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52778a23-967c-42ee-bd45-85e172ff8329", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627733919600}, "additional": {"logType": "detail", "children": [], "durationId": "a4f6eec1-633c-4ec7-ad5e-5bc7b68dceb1"}}, {"head": {"id": "6205df62-24b4-4b7a-9858-0ba590a0a946", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627734863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013565c0-b68f-4d93-9e12-8cc4db689343", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627735040000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75bfd774-f8cc-4175-9c3c-3fd8f5d0f697", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627736838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad2cdc7-3b4f-40b8-af15-1c00667df398", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627745417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd47a021-79a1-4649-9da6-66dbf8b0349e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627746022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d44a48-8ae2-4a54-b262-0fd3da6ac690", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627746307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6401062-f8ca-4db9-a11c-ae46aff53f92", "name": "entry : default@PreviewProcessResource cost memory 0.1920013427734375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627746733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39b8979e-99c9-4a01-803e-cde9e95f053f", "name": "runTaskFromQueue task cost before running: 717 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627752082900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02df2426-00d7-4c2b-9c1e-68d255754ad7", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627740702400, "endTime": 21627752322200, "totalTime": 6337000}, "additional": {"logType": "info", "children": [], "durationId": "a4f6eec1-633c-4ec7-ad5e-5bc7b68dceb1"}}, {"head": {"id": "87cfc5d8-48a5-40fc-8acb-e8a9573dccc0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627769851000, "endTime": 21627856948100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0ad2393b-2358-4808-aa1a-add617359e99", "logId": "fff06e98-59da-4fb3-b4e7-d836b296291d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ad2393b-2358-4808-aa1a-add617359e99", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627760570700}, "additional": {"logType": "detail", "children": [], "durationId": "87cfc5d8-48a5-40fc-8acb-e8a9573dccc0"}}, {"head": {"id": "a62eb3f7-0eda-4a5e-9c32-9af7e97f2989", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627762034500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e7664a-9b95-4324-ac3f-791375d510c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627762300900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b5cccb-2c53-4b83-ab28-157332a90ca4", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627769878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1760c234-8278-475c-bf02-20ae61d2d17d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 49 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627856224600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2654a58-d9f4-49dc-97e6-167113699d91", "name": "entry : default@GenerateLoaderJson cost memory 0.7763214111328125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627856698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff06e98-59da-4fb3-b4e7-d836b296291d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627769851000, "endTime": 21627856948100}, "additional": {"logType": "info", "children": [], "durationId": "87cfc5d8-48a5-40fc-8acb-e8a9573dccc0"}}, {"head": {"id": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627891685700, "endTime": 21628259395200}, "additional": {"children": ["2ae7c0ef-094b-433a-85dd-5a2bb88afd92", "090bff9d-0ee2-451f-a380-081a2f7f96d1", "66f6228a-b97a-4645-92ff-93d836a03e46", "bd3c7b34-87b9-4660-a2c9-2bacb56b54d1"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources' has been changed."], "detailId": "daa4cd4e-a58f-48bc-b563-22ac541a5a70", "logId": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daa4cd4e-a58f-48bc-b563-22ac541a5a70", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627882248700}, "additional": {"logType": "detail", "children": [], "durationId": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8"}}, {"head": {"id": "153973a8-7c06-4921-bdd8-46df4a472644", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627883179600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7e23fb-2add-4c72-9737-87dc19aca265", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627883349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f41bad9-d1dc-4cd4-b599-94cc9d227834", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627885389400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33bc3fa-d24a-46a1-937a-f390ec7d07be", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627891732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eecbf8a2-ebee-48ac-803f-4c485828a109", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627908204500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a9d3ac-4674-448b-823a-65dc00b7e141", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627909240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae7c0ef-094b-433a-85dd-5a2bb88afd92", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627913256400, "endTime": 21627968005700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8", "logId": "40560bed-b97e-41a2-a816-425a27778eda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40560bed-b97e-41a2-a816-425a27778eda", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627913256400, "endTime": 21627968005700}, "additional": {"logType": "info", "children": [], "durationId": "2ae7c0ef-094b-433a-85dd-5a2bb88afd92", "parent": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d"}}, {"head": {"id": "60508bb0-e5e8-4a45-b41f-200ebf6991ed", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627968466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090bff9d-0ee2-451f-a380-081a2f7f96d1", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627970184700, "endTime": 21628043246600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8", "logId": "500f2c61-25d1-42f8-960d-d4f4a2e81f29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cc2c971-04e2-47e3-aa5c-f5165d2b0642", "name": "current process  memoryUsage: {\n  rss: 89165824,\n  heapTotal: 119926784,\n  heapUsed: 111750240,\n  external: 3124482,\n  arrayBuffers: 118383\n} os memoryUsage :12.973506927490234", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627972135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e6689e-feb0-498d-a7b6-577f12481092", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628039433900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "500f2c61-25d1-42f8-960d-d4f4a2e81f29", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627970184700, "endTime": 21628043246600}, "additional": {"logType": "info", "children": [], "durationId": "090bff9d-0ee2-451f-a380-081a2f7f96d1", "parent": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d"}}, {"head": {"id": "e1f26a66-480b-4ab9-8f30-122f1871cd38", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628043793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f6228a-b97a-4645-92ff-93d836a03e46", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628048640900, "endTime": 21628148464400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8", "logId": "5e58e474-02f9-431e-be7f-98f3c02b0716"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0eb73242-eed9-4633-946c-41dcaffeb35a", "name": "current process  memoryUsage: {\n  rss: 89489408,\n  heapTotal: 119926784,\n  heapUsed: 112005344,\n  external: 3124608,\n  arrayBuffers: 118524\n} os memoryUsage :12.969558715820312", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628053045800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4cc257-0226-410c-acfa-07d781ac012a", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628142186000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e58e474-02f9-431e-be7f-98f3c02b0716", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628048640900, "endTime": 21628148464400}, "additional": {"logType": "info", "children": [], "durationId": "66f6228a-b97a-4645-92ff-93d836a03e46", "parent": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d"}}, {"head": {"id": "5b7fa644-7807-4571-b4f7-4fd9321228fc", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***n',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628148841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd3c7b34-87b9-4660-a2c9-2bacb56b54d1", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628150661800, "endTime": 21628254819200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8", "logId": "53883567-798a-44ab-966f-a8f73d59f707"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65c40b9b-2c4e-48f4-a8d0-070c5a01ebd6", "name": "current process  memoryUsage: {\n  rss: 89579520,\n  heapTotal: 119926784,\n  heapUsed: 112282152,\n  external: 3124734,\n  arrayBuffers: 119529\n} os memoryUsage :12.968482971191406", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628152250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a50bc4-cbea-4f6e-bcf8-a2a7794b32e8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628251316600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53883567-798a-44ab-966f-a8f73d59f707", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628150661800, "endTime": 21628254819200}, "additional": {"logType": "info", "children": [], "durationId": "bd3c7b34-87b9-4660-a2c9-2bacb56b54d1", "parent": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d"}}, {"head": {"id": "102d7149-26ab-4b0f-bac5-57840183451a", "name": "entry : default@PreviewCompileResource cost memory 1.5144577026367188", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628258467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59b5ad1-3029-4f6e-884e-5184c8a60b25", "name": "runTaskFromQueue task cost before running: 1 s 224 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628259185300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f3f769-52d9-4a5e-ac72-4453ba06b05d", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627891685700, "endTime": 21628259395200, "totalTime": 367186700}, "additional": {"logType": "info", "children": ["40560bed-b97e-41a2-a816-425a27778eda", "500f2c61-25d1-42f8-960d-d4f4a2e81f29", "5e58e474-02f9-431e-be7f-98f3c02b0716", "53883567-798a-44ab-966f-a8f73d59f707"], "durationId": "9d0f5e4f-3a0e-468a-b2fc-07faa0e513d8"}}, {"head": {"id": "96770a0e-7ffa-4e80-97ed-7036b5b5493e", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628270483000, "endTime": 21628271883800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "30e19fc4-fe2e-4fb5-8b6d-1bc361661802", "logId": "172693b6-3d79-422b-b52f-6cf2306693ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30e19fc4-fe2e-4fb5-8b6d-1bc361661802", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628267589300}, "additional": {"logType": "detail", "children": [], "durationId": "96770a0e-7ffa-4e80-97ed-7036b5b5493e"}}, {"head": {"id": "086ed667-f40b-4636-9f99-d773f5b7f58f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628269863000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1178d0fc-b11d-403b-88e3-e7f5ea8e94e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628270190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e894e58a-e154-456c-ae07-256bd68dae07", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628270511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0942b9c2-dec4-434d-bb94-e723e1de5d46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628270827500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4341989-c74e-4028-8fd3-4cbf4d049e44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628271086600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5e8a29-ae50-4ca8-b5a5-3ead401a2fbd", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628271375400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3ae826-1c17-4d2e-9513-ccba7f43ed4c", "name": "runTaskFromQueue task cost before running: 1 s 237 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628271668100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172693b6-3d79-422b-b52f-6cf2306693ef", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628270483000, "endTime": 21628271883800, "totalTime": 1112900}, "additional": {"logType": "info", "children": [], "durationId": "96770a0e-7ffa-4e80-97ed-7036b5b5493e"}}, {"head": {"id": "db945927-65c0-4b75-bbaf-5fec8296c179", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628283487300, "endTime": 21628310042700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile' has been changed."], "detailId": "91d81b34-3993-4a97-b172-b05b34d4ffdc", "logId": "af6b2096-3a34-400e-9196-29aaf0af98a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91d81b34-3993-4a97-b172-b05b34d4ffdc", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628278374700}, "additional": {"logType": "detail", "children": [], "durationId": "db945927-65c0-4b75-bbaf-5fec8296c179"}}, {"head": {"id": "7684e679-49ff-4bc5-9c91-14f6c04aa6f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628280939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cda2e2-7fbc-4d70-b08e-e85209f64506", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628281217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd02a77b-14d5-447e-9fed-08c2fcfe11d7", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628283523500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c53b7a-46f1-4b10-9a45-79131f7d443b", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628289182400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97f5f08b-72f6-41ed-9543-2f9e35291322", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628289682400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d31c774b-f67b-4f1a-8c00-a108e3153575", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628290214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50ccccd-5488-4dcb-8708-46960d77a59a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628290425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbea6e9d-4648-405e-aff9-89436159ea51", "name": "entry : default@CopyPreviewProfile cost memory 0.22550201416015625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628309606700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d146da-a179-47b3-b4ab-669f3b6fa90a", "name": "runTaskFromQueue task cost before running: 1 s 275 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628309910700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af6b2096-3a34-400e-9196-29aaf0af98a0", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628283487300, "endTime": 21628310042700, "totalTime": 26354600}, "additional": {"logType": "info", "children": [], "durationId": "db945927-65c0-4b75-bbaf-5fec8296c179"}}, {"head": {"id": "59ca5c98-383c-4270-866c-0c9187718def", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323018900, "endTime": 21628324438500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "4da722a2-efe9-44ed-a691-c1d584945eb3", "logId": "3215c38f-4552-4a8e-bcea-8e7b1b8fa462"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4da722a2-efe9-44ed-a691-c1d584945eb3", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628317162300}, "additional": {"logType": "detail", "children": [], "durationId": "59ca5c98-383c-4270-866c-0c9187718def"}}, {"head": {"id": "48c0443c-d402-4749-817f-77836532897a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628319273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da453989-8c59-4c35-ad4c-3001807dca32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628319545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebfcaa34-c285-45fd-a571-4bac19b7283c", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb7e0dc9-c9eb-4c58-b157-fb091f686c11", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a28405ce-d345-46df-87af-353ab9ccce48", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718bd22b-5895-4d0b-a4c8-a62bdd61bea8", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323875500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08637fa9-e353-4146-bccc-b8dfba0e751f", "name": "runTaskFromQueue task cost before running: 1 s 289 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628324222900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3215c38f-4552-4a8e-bcea-8e7b1b8fa462", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628323018900, "endTime": 21628324438500, "totalTime": 1050500}, "additional": {"logType": "info", "children": [], "durationId": "59ca5c98-383c-4270-866c-0c9187718def"}}, {"head": {"id": "7e8e951b-e710-444d-9c83-3fe5115b028e", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628331268900, "endTime": 21628332476300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fadc041b-2bc3-4180-9022-3c4c580e817e", "logId": "7231613a-8a28-44d4-9567-611815a5fec5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fadc041b-2bc3-4180-9022-3c4c580e817e", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628331085800}, "additional": {"logType": "detail", "children": [], "durationId": "7e8e951b-e710-444d-9c83-3fe5115b028e"}}, {"head": {"id": "ba531179-fcc8-4cf9-be80-3ec9ebb98a63", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628331298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15599cc8-57b5-4b3a-8d9d-5914cb6c8d65", "name": "entry : buildPreviewerResource cost memory 0.01186370849609375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628332009400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3489964e-a0cf-4bb2-9d76-01a39e3ede26", "name": "runTaskFromQueue task cost before running: 1 s 297 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628332326000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7231613a-8a28-44d4-9567-611815a5fec5", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628331268900, "endTime": 21628332476300, "totalTime": 1001000}, "additional": {"logType": "info", "children": [], "durationId": "7e8e951b-e710-444d-9c83-3fe5115b028e"}}, {"head": {"id": "1417e350-70ae-4563-9a3b-2fec0c6b4447", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628342775400, "endTime": 21628356564700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "93127922-daa4-4698-a421-746ef8b0cbbc", "logId": "6012a0a6-c2bf-4c79-af6b-442d615aef76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93127922-daa4-4698-a421-746ef8b0cbbc", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628338185700}, "additional": {"logType": "detail", "children": [], "durationId": "1417e350-70ae-4563-9a3b-2fec0c6b4447"}}, {"head": {"id": "eb967854-9f4c-4bc8-83c0-0d00db2695eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628340205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ebc9109-c667-4431-a3f6-25828be7eaa1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628340454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62253576-d700-473a-b575-3f5f23f62fc0", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628342800100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235b80be-4c55-48d1-984a-65f0930e37a8", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628348582500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc18117-f5f9-4adf-b3fa-457962350cb7", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628348960900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37170c73-bd4c-47c5-ac0b-a510124c77eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628349335800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4f500e-e00f-496b-893a-bd67625c1210", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628349521500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2c5efa-92d6-4e6a-ae8f-3a3ea9118994", "name": "entry : default@PreviewUpdateAssets cost memory 0.181182861328125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628355506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f828f3f-30be-4109-991a-e0c62ce1ef1b", "name": "runTaskFromQueue task cost before running: 1 s 321 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628356082800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6012a0a6-c2bf-4c79-af6b-442d615aef76", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628342775400, "endTime": 21628356564700, "totalTime": 13143300}, "additional": {"logType": "info", "children": [], "durationId": "1417e350-70ae-4563-9a3b-2fec0c6b4447"}}, {"head": {"id": "ec51f6ec-170c-42d8-badb-86299119a33b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628386363800, "endTime": 21633477079200}, "additional": {"children": ["591fa768-9b6c-495b-8ef2-849cff8364c6"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "fcf68424-4b50-4849-b09a-f8b2dec3eb50", "logId": "44e826d3-1f9a-41f5-ac84-01199e8d31b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcf68424-4b50-4849-b09a-f8b2dec3eb50", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628365865400}, "additional": {"logType": "detail", "children": [], "durationId": "ec51f6ec-170c-42d8-badb-86299119a33b"}}, {"head": {"id": "ecb7db03-5895-4576-92f4-4279b92f7771", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628367945300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bdb0324-e607-4de6-a02d-a6f29733189a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628368217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f438bb-c8df-40d8-968e-3a553c3ee202", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628386419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de2f4d4-f1a5-4936-9801-abfef04fb239", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628438521200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d8b9ad-e577-4b3d-9df8-94d003f62cc9", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628439186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591fa768-9b6c-495b-8ef2-849cff8364c6", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker2", "startTime": 21628508205500, "endTime": 21633475611100}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ec51f6ec-170c-42d8-badb-86299119a33b", "logId": "8ca35eac-27d6-4102-9dad-5881076d0a64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0d7c627-8841-45ac-b1cd-4ee86a053d5b", "name": "entry : default@PreviewArkTS cost memory -0.5794601440429688", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628523420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca35eac-27d6-4102-9dad-5881076d0a64", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Worker2", "startTime": 21628508205500, "endTime": 21633475611100}, "additional": {"logType": "error", "children": [], "durationId": "591fa768-9b6c-495b-8ef2-849cff8364c6", "parent": "44e826d3-1f9a-41f5-ac84-01199e8d31b8"}}, {"head": {"id": "0809e897-a758-42eb-8b63-ace70f5ef0f3", "name": "default@PreviewArkTS watch work[2] failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633475893200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e826d3-1f9a-41f5-ac84-01199e8d31b8", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21628386363800, "endTime": 21633477079200}, "additional": {"logType": "error", "children": ["8ca35eac-27d6-4102-9dad-5881076d0a64"], "durationId": "ec51f6ec-170c-42d8-badb-86299119a33b"}}, {"head": {"id": "147c19f5-49f9-48f4-8c58-334e9b39c7e9", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633478634800}, "additional": {"logType": "debug", "children": [], "durationId": "ec51f6ec-170c-42d8-badb-86299119a33b"}}, {"head": {"id": "e7a7c8c0-f411-4aa2-ab3c-0001fb7fb46e", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633488170300}, "additional": {"logType": "debug", "children": [], "durationId": "ec51f6ec-170c-42d8-badb-86299119a33b"}}, {"head": {"id": "68f055c4-771a-47ad-bdfe-de08f4ba8b5e", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633503450100, "endTime": 21633503725900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ab18151-7248-499b-9247-7387d446802d", "logId": "5cae2240-1585-4fd9-93ec-bdc80dc14917"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cae2240-1585-4fd9-93ec-bdc80dc14917", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633503450100, "endTime": 21633503725900}, "additional": {"logType": "info", "children": [], "durationId": "68f055c4-771a-47ad-bdfe-de08f4ba8b5e"}}, {"head": {"id": "824ca612-62d7-4451-a278-e6d9c96a1b87", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21627035486200, "endTime": 21633503937100}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "bc200d13-6214-4e59-be35-79525780e581", "name": "BUILD FAILED in 6 s 469 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633504018000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "e65bd56a-8b2d-415f-bc73-9378c8e5a0e5", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633505452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b11caf3e-8868-40da-9d7d-ca3e927b1f26", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633505823100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e091284-fa5d-454e-a383-fa746398433b", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633506240200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5986e77-caa6-46a0-a498-6873b517759e", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633506506900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b8f955-5004-4bd3-9170-4e65f862b1c2", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633507035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84fae0b5-5c33-4362-b8c8-979728b6291f", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633507155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40bb0851-1ecc-4672-a286-e7bcad711e22", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633507584100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bc389d-f248-4193-bf56-aa807ff603e4", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633508049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fe41913-9824-4810-8586-f91e0e238a47", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633508468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d603cf-2e7a-43d2-939b-93244ecf9bf1", "name": "Incremental task entry:default@PreBuild post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633508914200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2389fb5f-1851-4291-b68a-48eb190cf8ce", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4efcfbac-d5bc-4b0a-ab57-ca067b13bb21", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509069700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e206f8-4f72-4d1b-b37c-a8b09f0a5354", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245c6fb7-d318-498b-802a-21b756fc268a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509162100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a0441e-a4ce-4974-8e36-46515cbed84e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabf3c2f-5f2a-4b8b-9c10-f6ec4e6add77", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633509232600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75208296-a17b-4fb2-a51f-c2a8307423c3", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633510090600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a67d81-3701-4120-8b28-0aa2477ae9fe", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633510179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41817771-0275-428d-9553-ab16b5a4ba15", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633510566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7910cd0a-95c1-4784-a0ef-f4888a41cd9a", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633518152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e38c621-2d80-499b-8bc2-47f141a4aaf3", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633519193200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8efa66df-767d-4f32-ab7d-5e94d9f88b24", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633519549300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7d734e-0152-46c8-8352-93e0e9e49276", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633519673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671f98be-fbdf-4ea3-8cbe-daa4573ce302", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633520750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afafbf24-9418-4437-8fe4-fb1dad3a6f20", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633521352100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a579a3b0-9ae4-40a2-bf4a-a1d71d9c4a0f", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633521771000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7450850b-5f78-4050-89ce-24936aa8bd4e", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633522212400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a906eca9-bedb-43f5-90b6-74a16e2e4e9b", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633525108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363a3ade-7273-43d3-8e5b-5f0023c70d1d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633525831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b1d972-4f4b-4751-be3c-74ed7695d47b", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633525921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e3ea721-0543-4e85-87ce-ec3a1ccc2836", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633526160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537a4277-aff2-4671-8fd0-ac4e88e98a09", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633526831000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83dd5442-3345-4537-8527-f90055b6d7d0", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633530307600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7affad-db09-465f-a439-88413e51ba39", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633530597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417577f8-da2c-41d5-a2c0-a4c309d85047", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633530836000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb690c2-ce45-4444-9826-491a6590f4d4", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633531201300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a157f4-a098-4333-886b-a71a443a0284", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633531441000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}