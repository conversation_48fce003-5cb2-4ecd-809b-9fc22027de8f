{"version": "2.0", "ppid": 12876, "events": [{"head": {"id": "6bab8064-cdee-406a-850d-d0fb215d2389", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633547858200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365a00c4-e033-4c20-9d68-98597bf103b7", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633559478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e9332bf-a285-4a8d-be9b-d220aa484ca1", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21633559802200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eeb5fb9-1f44-4f0b-92e3-81b35ae36210", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645118444400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f54e377-2045-4a40-94b4-985b40cd1788", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645125445500, "endTime": 21645265138000}, "additional": {"children": ["2d1089f1-1f7a-4e85-a7ee-60de739c48d5", "f3d4c337-6269-4570-8979-3a47c13a8c08", "7f850a56-bd83-4d5e-a741-40e62186b4e7", "68cc0f23-4365-4e2d-9cbe-41e07af95eaf", "b20bac77-d791-4a19-8080-6e996b9db901", "cc6644e7-c2b6-4fb8-95e8-c8e0c2f950c3", "345d9138-a28a-4c91-b19d-37affc0a9251"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d1089f1-1f7a-4e85-a7ee-60de739c48d5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645125447600, "endTime": 21645137109500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "5f1aaf60-aa51-4a15-98fb-d54aa49ad76e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3d4c337-6269-4570-8979-3a47c13a8c08", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645137127000, "endTime": 21645264064700}, "additional": {"children": ["f7f9d053-96ca-4bd3-82f0-4e2abc7f986e", "dda662a8-0713-4d63-8561-327f24a87c60", "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "f5c945eb-5613-403a-b725-d59d3b3764f0", "47646431-9ff8-40dd-abc9-71886c199ce1", "3ffb4f49-4a0e-4224-b17e-0725cad4cad6", "cd2e646f-3170-45aa-ae93-cffe825301af", "11a74f6b-9d4d-499d-ab80-29a2e528f32c", "7d4b3c39-da23-4f2b-be12-5b9cf5cf34c2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "54f4c421-abfe-42a3-90fc-dd00ace49754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f850a56-bd83-4d5e-a741-40e62186b4e7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645264084700, "endTime": 21645265122800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "45ac15c5-b743-47c2-8ea3-f6a76f941a11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68cc0f23-4365-4e2d-9cbe-41e07af95eaf", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645265127700, "endTime": 21645265134600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "df6f5396-d623-4928-a3b5-b76dcb3f3ae0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b20bac77-d791-4a19-8080-6e996b9db901", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645128392200, "endTime": 21645128422400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "59eb8431-a167-4fb9-9e85-ec6bd719f786"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59eb8431-a167-4fb9-9e85-ec6bd719f786", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645128392200, "endTime": 21645128422400}, "additional": {"logType": "info", "children": [], "durationId": "b20bac77-d791-4a19-8080-6e996b9db901", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "cc6644e7-c2b6-4fb8-95e8-c8e0c2f950c3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645133319400, "endTime": 21645133339200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "5ae674bd-c38c-4e66-bfcf-470efc2594de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ae674bd-c38c-4e66-bfcf-470efc2594de", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645133319400, "endTime": 21645133339200}, "additional": {"logType": "info", "children": [], "durationId": "cc6644e7-c2b6-4fb8-95e8-c8e0c2f950c3", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "d956b62a-285b-472e-ac18-3ea1a49bc012", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645133389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd8b952-fc96-4d7c-bbd6-8882f6f97894", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645136986700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f1aaf60-aa51-4a15-98fb-d54aa49ad76e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645125447600, "endTime": 21645137109500}, "additional": {"logType": "info", "children": [], "durationId": "2d1089f1-1f7a-4e85-a7ee-60de739c48d5", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "f7f9d053-96ca-4bd3-82f0-4e2abc7f986e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645142005100, "endTime": 21645142015000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "ae71b241-24da-4ae4-828a-c85c63645c64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dda662a8-0713-4d63-8561-327f24a87c60", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645142029000, "endTime": 21645146056700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "be921bcf-ae9f-40d4-8df0-e23512a7bf78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645146071200, "endTime": 21645213703900}, "additional": {"children": ["8889d02b-e114-49da-b8f4-b73012525aa0", "35fd8b1d-7c27-4929-af08-e73ea3f727c2", "41578855-4944-4457-9960-fd0082d1f80f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "13f06ca6-7146-4f5e-a803-41b6e624dd3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5c945eb-5613-403a-b725-d59d3b3764f0", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213715800, "endTime": 21645234655200}, "additional": {"children": ["1f53e736-fe4f-43e3-8e4d-f7fcba40ea18"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "de8e30ee-c55e-4a25-9a08-e9b0b6c3fd51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47646431-9ff8-40dd-abc9-71886c199ce1", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645234661700, "endTime": 21645246290400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "ce4ba79a-34e3-4191-8b8f-2173c5aae8ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ffb4f49-4a0e-4224-b17e-0725cad4cad6", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645247259800, "endTime": 21645254966400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "cadb6007-90a4-4f6d-93fa-fe56ce667fdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd2e646f-3170-45aa-ae93-cffe825301af", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645254991400, "endTime": 21645263911200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "8db83521-bc7f-433d-b73f-db031977c012"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11a74f6b-9d4d-499d-ab80-29a2e528f32c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645263937900, "endTime": 21645264053700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "71afcf6a-c1ec-467b-b5de-5becc82b16b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae71b241-24da-4ae4-828a-c85c63645c64", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645142005100, "endTime": 21645142015000}, "additional": {"logType": "info", "children": [], "durationId": "f7f9d053-96ca-4bd3-82f0-4e2abc7f986e", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "be921bcf-ae9f-40d4-8df0-e23512a7bf78", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645142029000, "endTime": 21645146056700}, "additional": {"logType": "info", "children": [], "durationId": "dda662a8-0713-4d63-8561-327f24a87c60", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "8889d02b-e114-49da-b8f4-b73012525aa0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645146693600, "endTime": 21645146710900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "logId": "3ab04c90-bacf-471e-8fff-c4c514d6df87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ab04c90-bacf-471e-8fff-c4c514d6df87", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645146693600, "endTime": 21645146710900}, "additional": {"logType": "info", "children": [], "durationId": "8889d02b-e114-49da-b8f4-b73012525aa0", "parent": "13f06ca6-7146-4f5e-a803-41b6e624dd3b"}}, {"head": {"id": "35fd8b1d-7c27-4929-af08-e73ea3f727c2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645148431400, "endTime": 21645213093300}, "additional": {"children": ["3c3acdd8-97f8-4389-b49b-7b91e46d5946", "2ad204e1-a67c-4e5c-961d-17e98d2de73b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "logId": "75c444e6-dbb3-4d59-85aa-51a8a8740094"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c3acdd8-97f8-4389-b49b-7b91e46d5946", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645148432300, "endTime": 21645152321700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fd8b1d-7c27-4929-af08-e73ea3f727c2", "logId": "2315d275-a0ab-4120-82d0-b39be1f6b586"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad204e1-a67c-4e5c-961d-17e98d2de73b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645152337900, "endTime": 21645213082700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fd8b1d-7c27-4929-af08-e73ea3f727c2", "logId": "baf90662-524d-4483-a914-2c8aaabc5cb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d12f51d-007f-495a-b9d1-7ddd58af53ba", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645148439500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d76cc6e-6280-4ff1-869e-e1605ee9fb2a", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645152185100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2315d275-a0ab-4120-82d0-b39be1f6b586", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645148432300, "endTime": 21645152321700}, "additional": {"logType": "info", "children": [], "durationId": "3c3acdd8-97f8-4389-b49b-7b91e46d5946", "parent": "75c444e6-dbb3-4d59-85aa-51a8a8740094"}}, {"head": {"id": "ba24e1d7-e22b-436b-9e07-3fb9339ea6f4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645152356600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bf8533-0ff6-452e-abf7-1d468fa33fa9", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645159296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c49009-7600-4b70-bb40-d9fc28612853", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645159406800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0aba7b2-d876-4ac4-8f2f-f41e0eb6ed3d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645159519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e165657-d8e7-4395-88dc-6e531783b891", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645159634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb256793-48d5-40f7-87e5-bbdbd4031936", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645160966400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8489615-59e8-4204-b9c8-bab054be02f8", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645164450900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2af34a4-2983-491f-98cb-5fbac10b18bf", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645174383700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f833dd7f-bdc0-463b-947d-dac625dc9a1c", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645193767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "743b0177-bcf5-4795-9ca9-622b708c4e92", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645193909500}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "markType": "other"}}, {"head": {"id": "9f1b21ed-072e-4a97-9ba4-197b1f445204", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645193932900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "markType": "other"}}, {"head": {"id": "178f0448-69e8-44d4-83ab-010ce2c2dd55", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645212862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf75668c-d284-4402-af9d-71ebb5de8f58", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645212975100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d838180-ffab-45df-a20f-c42b7578584d", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213019000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91528f91-2f19-47cd-a4f7-f7857e8948ee", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213056400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf90662-524d-4483-a914-2c8aaabc5cb2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645152337900, "endTime": 21645213082700}, "additional": {"logType": "info", "children": [], "durationId": "2ad204e1-a67c-4e5c-961d-17e98d2de73b", "parent": "75c444e6-dbb3-4d59-85aa-51a8a8740094"}}, {"head": {"id": "75c444e6-dbb3-4d59-85aa-51a8a8740094", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645148431400, "endTime": 21645213093300}, "additional": {"logType": "info", "children": ["2315d275-a0ab-4120-82d0-b39be1f6b586", "baf90662-524d-4483-a914-2c8aaabc5cb2"], "durationId": "35fd8b1d-7c27-4929-af08-e73ea3f727c2", "parent": "13f06ca6-7146-4f5e-a803-41b6e624dd3b"}}, {"head": {"id": "41578855-4944-4457-9960-fd0082d1f80f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213676500, "endTime": 21645213690200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "logId": "d88026df-0a0f-4482-8ead-6a5d9b2dc683"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d88026df-0a0f-4482-8ead-6a5d9b2dc683", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213676500, "endTime": 21645213690200}, "additional": {"logType": "info", "children": [], "durationId": "41578855-4944-4457-9960-fd0082d1f80f", "parent": "13f06ca6-7146-4f5e-a803-41b6e624dd3b"}}, {"head": {"id": "13f06ca6-7146-4f5e-a803-41b6e624dd3b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645146071200, "endTime": 21645213703900}, "additional": {"logType": "info", "children": ["3ab04c90-bacf-471e-8fff-c4c514d6df87", "75c444e6-dbb3-4d59-85aa-51a8a8740094", "d88026df-0a0f-4482-8ead-6a5d9b2dc683"], "durationId": "9a9ae456-c74f-492b-84ef-e1b853ebd9bc", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "1f53e736-fe4f-43e3-8e4d-f7fcba40ea18", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645214979500, "endTime": 21645234642300}, "additional": {"children": ["7631b084-87a6-49e9-96c4-1dcc68f0f506", "7a43392d-886e-4c0d-b1bc-0262ff4c6b88", "ffb6bbec-a7c6-48d3-b8e7-6dacc8d41f53"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f5c945eb-5613-403a-b725-d59d3b3764f0", "logId": "9bc4022b-ff67-49d1-aad2-6f94122d451b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7631b084-87a6-49e9-96c4-1dcc68f0f506", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645217797400, "endTime": 21645217815200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53e736-fe4f-43e3-8e4d-f7fcba40ea18", "logId": "50597f44-f5a7-4daf-830b-4028aaf92e67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50597f44-f5a7-4daf-830b-4028aaf92e67", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645217797400, "endTime": 21645217815200}, "additional": {"logType": "info", "children": [], "durationId": "7631b084-87a6-49e9-96c4-1dcc68f0f506", "parent": "9bc4022b-ff67-49d1-aad2-6f94122d451b"}}, {"head": {"id": "7a43392d-886e-4c0d-b1bc-0262ff4c6b88", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645219410700, "endTime": 21645233367400}, "additional": {"children": ["c2b53e6f-532b-4722-b9b2-11a2892e594d", "d420ea40-9ec2-433d-92ec-4601e1e89b42"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53e736-fe4f-43e3-8e4d-f7fcba40ea18", "logId": "9178e18c-31a2-4c22-b8a8-c97e791060ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2b53e6f-532b-4722-b9b2-11a2892e594d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645219411700, "endTime": 21645223121400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a43392d-886e-4c0d-b1bc-0262ff4c6b88", "logId": "4372e846-101c-4362-a05f-749bffabf451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d420ea40-9ec2-433d-92ec-4601e1e89b42", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645223136300, "endTime": 21645233356100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a43392d-886e-4c0d-b1bc-0262ff4c6b88", "logId": "f5f8e1ff-d9e0-4a08-8a69-bb17acf392ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932bda26-32cb-4175-86c5-d5c64a332d5a", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645219419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7420234b-cd09-40a9-9cbe-d203d0cedf80", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645223016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4372e846-101c-4362-a05f-749bffabf451", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645219411700, "endTime": 21645223121400}, "additional": {"logType": "info", "children": [], "durationId": "c2b53e6f-532b-4722-b9b2-11a2892e594d", "parent": "9178e18c-31a2-4c22-b8a8-c97e791060ed"}}, {"head": {"id": "2cb1ace2-9c4b-454f-a898-f018dd351cbd", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645223147500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c30e304-9f9f-449c-b257-db49433952f3", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645228993900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da4c364-c159-4418-a7d4-4141e25e8f08", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17514e6a-e923-457f-9d8b-1cac1f35d217", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229266000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0701bf45-2b1f-41fe-966e-b13bb7ff456a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229357300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8779c1b5-3b1d-4e1a-965f-ec72e277fb00", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229429700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d987bb-bbad-4a80-940b-b82ed0154d06", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229943900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6863d2a7-f715-4124-8ed3-a3589cab6088", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645229988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cebce01-ef87-4873-ad10-426c16677618", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645233076700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0980ae-7954-4411-804e-bb6c6ebc2c34", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645233238700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c71fb37-caae-4b07-ac51-62edd3dae3fd", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645233296200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462a7831-cdf9-4827-b716-1a9ad1eb97f1", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645233326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f8e1ff-d9e0-4a08-8a69-bb17acf392ea", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645223136300, "endTime": 21645233356100}, "additional": {"logType": "info", "children": [], "durationId": "d420ea40-9ec2-433d-92ec-4601e1e89b42", "parent": "9178e18c-31a2-4c22-b8a8-c97e791060ed"}}, {"head": {"id": "9178e18c-31a2-4c22-b8a8-c97e791060ed", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645219410700, "endTime": 21645233367400}, "additional": {"logType": "info", "children": ["4372e846-101c-4362-a05f-749bffabf451", "f5f8e1ff-d9e0-4a08-8a69-bb17acf392ea"], "durationId": "7a43392d-886e-4c0d-b1bc-0262ff4c6b88", "parent": "9bc4022b-ff67-49d1-aad2-6f94122d451b"}}, {"head": {"id": "ffb6bbec-a7c6-48d3-b8e7-6dacc8d41f53", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645234612800, "endTime": 21645234626400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53e736-fe4f-43e3-8e4d-f7fcba40ea18", "logId": "4415bded-57cd-4e5c-8781-d4b4bec4cd47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4415bded-57cd-4e5c-8781-d4b4bec4cd47", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645234612800, "endTime": 21645234626400}, "additional": {"logType": "info", "children": [], "durationId": "ffb6bbec-a7c6-48d3-b8e7-6dacc8d41f53", "parent": "9bc4022b-ff67-49d1-aad2-6f94122d451b"}}, {"head": {"id": "9bc4022b-ff67-49d1-aad2-6f94122d451b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645214979500, "endTime": 21645234642300}, "additional": {"logType": "info", "children": ["50597f44-f5a7-4daf-830b-4028aaf92e67", "9178e18c-31a2-4c22-b8a8-c97e791060ed", "4415bded-57cd-4e5c-8781-d4b4bec4cd47"], "durationId": "1f53e736-fe4f-43e3-8e4d-f7fcba40ea18", "parent": "de8e30ee-c55e-4a25-9a08-e9b0b6c3fd51"}}, {"head": {"id": "de8e30ee-c55e-4a25-9a08-e9b0b6c3fd51", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645213715800, "endTime": 21645234655200}, "additional": {"logType": "info", "children": ["9bc4022b-ff67-49d1-aad2-6f94122d451b"], "durationId": "f5c945eb-5613-403a-b725-d59d3b3764f0", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "5b6a4749-6396-4e06-811c-d76e7a523a7e", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645245868900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50e9a63-d0f1-4280-830c-43c9274f96dc", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645246165800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4ba79a-34e3-4191-8b8f-2173c5aae8ec", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645234661700, "endTime": 21645246290400}, "additional": {"logType": "info", "children": [], "durationId": "47646431-9ff8-40dd-abc9-71886c199ce1", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "7d4b3c39-da23-4f2b-be12-5b9cf5cf34c2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645247072800, "endTime": 21645247246100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d4c337-6269-4570-8979-3a47c13a8c08", "logId": "26f7ad02-ea95-49c1-a377-9cae540381ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f1c5b7c-332f-48de-ad3f-2e1e80a887a6", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645247101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f7ad02-ea95-49c1-a377-9cae540381ee", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645247072800, "endTime": 21645247246100}, "additional": {"logType": "info", "children": [], "durationId": "7d4b3c39-da23-4f2b-be12-5b9cf5cf34c2", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "50d36a4a-d492-4a7c-8bb0-8a12f39ce595", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645248569700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea2e01c-fb95-428e-8edf-2677942e5f34", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645253995800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cadb6007-90a4-4f6d-93fa-fe56ce667fdd", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645247259800, "endTime": 21645254966400}, "additional": {"logType": "info", "children": [], "durationId": "3ffb4f49-4a0e-4224-b17e-0725cad4cad6", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "9c5e61b7-5135-433f-ba83-e6215748832a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645255011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c92c32-4219-4562-9d05-069204cb4472", "name": "<PERSON><PERSON>le wallet Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645259499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85496da5-7817-439c-9237-e11444bb9750", "name": "Module wallet's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645259613500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b52d08-638d-4fa8-b5d1-426c3286da86", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645259835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b71fad-77f6-401d-bde2-d98b8682c679", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645261498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47b8af0-0772-4d98-9074-b4b3610175e0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645261581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8db83521-bc7f-433d-b73f-db031977c012", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645254991400, "endTime": 21645263911200}, "additional": {"logType": "info", "children": [], "durationId": "cd2e646f-3170-45aa-ae93-cffe825301af", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "4665ada5-ffbf-48e4-a274-20f2fc61e44b", "name": "Configuration phase cost:122 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645263966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71afcf6a-c1ec-467b-b5de-5becc82b16b5", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645263937900, "endTime": 21645264053700}, "additional": {"logType": "info", "children": [], "durationId": "11a74f6b-9d4d-499d-ab80-29a2e528f32c", "parent": "54f4c421-abfe-42a3-90fc-dd00ace49754"}}, {"head": {"id": "54f4c421-abfe-42a3-90fc-dd00ace49754", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645137127000, "endTime": 21645264064700}, "additional": {"logType": "info", "children": ["ae71b241-24da-4ae4-828a-c85c63645c64", "be921bcf-ae9f-40d4-8df0-e23512a7bf78", "13f06ca6-7146-4f5e-a803-41b6e624dd3b", "de8e30ee-c55e-4a25-9a08-e9b0b6c3fd51", "ce4ba79a-34e3-4191-8b8f-2173c5aae8ec", "cadb6007-90a4-4f6d-93fa-fe56ce667fdd", "8db83521-bc7f-433d-b73f-db031977c012", "71afcf6a-c1ec-467b-b5de-5becc82b16b5", "26f7ad02-ea95-49c1-a377-9cae540381ee"], "durationId": "f3d4c337-6269-4570-8979-3a47c13a8c08", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "345d9138-a28a-4c91-b19d-37affc0a9251", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645265094100, "endTime": 21645265109700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f54e377-2045-4a40-94b4-985b40cd1788", "logId": "cb02c3e5-c08b-4cf8-b88d-4c22e248c8b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb02c3e5-c08b-4cf8-b88d-4c22e248c8b9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645265094100, "endTime": 21645265109700}, "additional": {"logType": "info", "children": [], "durationId": "345d9138-a28a-4c91-b19d-37affc0a9251", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "45ac15c5-b743-47c2-8ea3-f6a76f941a11", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645264084700, "endTime": 21645265122800}, "additional": {"logType": "info", "children": [], "durationId": "7f850a56-bd83-4d5e-a741-40e62186b4e7", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "df6f5396-d623-4928-a3b5-b76dcb3f3ae0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645265127700, "endTime": 21645265134600}, "additional": {"logType": "info", "children": [], "durationId": "68cc0f23-4365-4e2d-9cbe-41e07af95eaf", "parent": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf"}}, {"head": {"id": "f70ae49e-6b6c-4b1b-95cb-24822b05d0cf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645125445500, "endTime": 21645265138000}, "additional": {"logType": "info", "children": ["5f1aaf60-aa51-4a15-98fb-d54aa49ad76e", "54f4c421-abfe-42a3-90fc-dd00ace49754", "45ac15c5-b743-47c2-8ea3-f6a76f941a11", "df6f5396-d623-4928-a3b5-b76dcb3f3ae0", "59eb8431-a167-4fb9-9e85-ec6bd719f786", "5ae674bd-c38c-4e66-bfcf-470efc2594de", "cb02c3e5-c08b-4cf8-b88d-4c22e248c8b9"], "durationId": "0f54e377-2045-4a40-94b4-985b40cd1788"}}, {"head": {"id": "b99341c1-abe3-486c-87ad-a0086730577b", "name": "Configuration task cost before running: 144 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645265360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8b7c13-35e3-4df3-8c46-af95adccc54f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645269682400, "endTime": 21645277038000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "082ca97d-8e74-4328-8b93-99d50e70ab49", "logId": "50e6b934-191f-42ea-862b-43ac07786b3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "082ca97d-8e74-4328-8b93-99d50e70ab49", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645266536300}, "additional": {"logType": "detail", "children": [], "durationId": "0d8b7c13-35e3-4df3-8c46-af95adccc54f"}}, {"head": {"id": "a124f4b0-5854-4e15-9ab3-58c07a9949f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645267331800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994fec0f-75b4-4bf5-b30b-4c6d0d820d87", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645267413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b309c31f-3e4b-4e04-ad60-b5ee4b242cc3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645269696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8355f2-0fc5-4c7b-a893-df5c1e2d76ef", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645276809800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1429ec8c-f919-4807-ab5c-e7572c66a212", "name": "entry : default@PreBuild cost memory 0.28369140625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645276964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e6b934-191f-42ea-862b-43ac07786b3b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645269682400, "endTime": 21645277038000}, "additional": {"logType": "info", "children": [], "durationId": "0d8b7c13-35e3-4df3-8c46-af95adccc54f"}}, {"head": {"id": "165a9f36-430c-4a02-b682-145fd976666c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645281402000, "endTime": 21645283574900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9d745d1f-0721-4b7d-a998-c68f5bbc3053", "logId": "19fb03a3-8c69-4d16-8f02-ea034b6fb1d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d745d1f-0721-4b7d-a998-c68f5bbc3053", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645280238200}, "additional": {"logType": "detail", "children": [], "durationId": "165a9f36-430c-4a02-b682-145fd976666c"}}, {"head": {"id": "06be6a96-68b1-4912-b5ba-1a64256c8707", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645280686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d68cff2-4077-4476-8c55-11219ec80fcb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645280776200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb92bde-b107-44bd-9346-5c20d835dcb1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645281413400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb333794-a4de-4af7-b7fa-917e235a3589", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645283398200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad6fcd11-204b-4736-a153-f5dfbb13c1b4", "name": "entry : default@MergeProfile cost memory 0.10677337646484375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645283514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fb03a3-8c69-4d16-8f02-ea034b6fb1d0", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645281402000, "endTime": 21645283574900}, "additional": {"logType": "info", "children": [], "durationId": "165a9f36-430c-4a02-b682-145fd976666c"}}, {"head": {"id": "fa84da2e-96ff-481c-96cc-9c8bd276a9bc", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645286683400, "endTime": 21645288734600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8f344f1c-25cf-464c-8d83-7933ad95b6a6", "logId": "e37bcc02-86b9-4365-990f-720b81735d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f344f1c-25cf-464c-8d83-7933ad95b6a6", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645285460600}, "additional": {"logType": "detail", "children": [], "durationId": "fa84da2e-96ff-481c-96cc-9c8bd276a9bc"}}, {"head": {"id": "4bcf090a-98ac-4f12-b2a0-7c780e41d65a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645285921400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6388b709-8dbd-41a4-a9a9-c7163d1a438e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645286022500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ca48cb-4e66-4e85-ad7e-66d7f57795ff", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645286693700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b1ecf89-b830-4519-9cbe-0f0fa3956e3e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645287453200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7aaac7e-e48c-483c-9e96-199ddb478bcb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645288586200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee20e36a-da1f-4050-96cd-ce4122ea134a", "name": "entry : default@CreateBuildProfile cost memory 0.100372314453125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645288684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37bcc02-86b9-4365-990f-720b81735d72", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645286683400, "endTime": 21645288734600}, "additional": {"logType": "info", "children": [], "durationId": "fa84da2e-96ff-481c-96cc-9c8bd276a9bc"}}, {"head": {"id": "2b608c6b-4346-496a-a6ad-99cecf1aaa34", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291626000, "endTime": 21645291922600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6e2f771e-d2b8-45f6-ab74-5c7593763e30", "logId": "8497e3c0-64b0-46df-b1f6-ceb0779853e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e2f771e-d2b8-45f6-ab74-5c7593763e30", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645290349500}, "additional": {"logType": "detail", "children": [], "durationId": "2b608c6b-4346-496a-a6ad-99cecf1aaa34"}}, {"head": {"id": "92e2cd72-91a7-4aa3-93b5-c070362523ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645290818000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0caf3c4-cc41-4e93-a77f-be56ca76e26b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645290935700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95642d4-5175-4350-9236-131484192786", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291638300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "120a57f5-2f1c-4a82-b9fe-d49fac8bd596", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a0d591-d321-4ba3-8fd5-eb7387b60986", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291783100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3101f2-4963-48fa-acc0-402b9cc4bcd0", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291833900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fe97c1-d5fb-4536-85f9-4c51ab38ee61", "name": "runTaskFromQueue task cost before running: 170 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291894100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8497e3c0-64b0-46df-b1f6-ceb0779853e9", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645291626000, "endTime": 21645291922600, "totalTime": 249500}, "additional": {"logType": "info", "children": [], "durationId": "2b608c6b-4346-496a-a6ad-99cecf1aaa34"}}, {"head": {"id": "2c32a1a6-aa04-4aff-a744-27c3095879d2", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645300359200, "endTime": 21645301371100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b5aed028-8ed4-43ae-8770-caf5d2652408", "logId": "7c006cc2-2cf2-4f77-a3b8-cc9db030cd31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5aed028-8ed4-43ae-8770-caf5d2652408", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645293178100}, "additional": {"logType": "detail", "children": [], "durationId": "2c32a1a6-aa04-4aff-a744-27c3095879d2"}}, {"head": {"id": "eac5c7b1-982a-4773-a237-a128e4af35b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645293599800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4524cea-92c2-42fd-a96b-dc6238490e3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645293691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc1e0a2-8da6-4d78-817e-a56418e5fc5f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645300374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f57626e-aea5-4c52-849f-7f7d7bf0a691", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645300592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76b6ce0-eb7f-4f9a-9432-1234b15d2b44", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645301183300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417545b6-db20-4b52-bc8f-64e4ccf38242", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06725311279296875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645301311400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c006cc2-2cf2-4f77-a3b8-cc9db030cd31", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645300359200, "endTime": 21645301371100}, "additional": {"logType": "info", "children": [], "durationId": "2c32a1a6-aa04-4aff-a744-27c3095879d2"}}, {"head": {"id": "3a4314c9-7e8c-42d5-90f6-28e67ee6a1ae", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645304268100, "endTime": 21645305254600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a1874d6b-ff3e-48d5-ab53-dda270e44d19", "logId": "7bdd74ee-e953-4872-bf10-ff3991fdf9ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1874d6b-ff3e-48d5-ab53-dda270e44d19", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645302792000}, "additional": {"logType": "detail", "children": [], "durationId": "3a4314c9-7e8c-42d5-90f6-28e67ee6a1ae"}}, {"head": {"id": "60004d59-9854-477d-9154-30b7ed8f79da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645303236300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c274ab-1b58-4bc7-962b-43415ba08674", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645303320200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e2ae62-e84a-430c-af7b-70e3f65c5ee0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645304278500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d050d501-517f-4093-8a6a-e94296d73dba", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645305101600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e7356d-55e7-4db2-8396-65ab060f2dd0", "name": "entry : default@ProcessProfile cost memory 0.0588531494140625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645305196800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bdd74ee-e953-4872-bf10-ff3991fdf9ef", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645304268100, "endTime": 21645305254600}, "additional": {"logType": "info", "children": [], "durationId": "3a4314c9-7e8c-42d5-90f6-28e67ee6a1ae"}}, {"head": {"id": "6e8419c0-32a8-4a96-a76e-d22eac36315e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645308562800, "endTime": 21645313752500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8f43747-9df2-4e6b-a97a-0db3b3aba5ad", "logId": "eca8d078-2739-4763-8413-9b833639338d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8f43747-9df2-4e6b-a97a-0db3b3aba5ad", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645306614900}, "additional": {"logType": "detail", "children": [], "durationId": "6e8419c0-32a8-4a96-a76e-d22eac36315e"}}, {"head": {"id": "23b3f489-ddf7-4712-ba92-c26c27001404", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645307068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435f00f0-b1e1-4440-83a7-0a11e012c454", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645307153800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07b73b3-9e0a-4e6d-8083-238a494cdb5b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645308572700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f46eb20-084e-4c2f-b2cb-1bbb78d82c91", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645313557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e7a348-c25c-41c4-9607-b289d9efd16f", "name": "entry : default@ProcessRouterMap cost memory 0.23589324951171875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645313691300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca8d078-2739-4763-8413-9b833639338d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645308562800, "endTime": 21645313752500}, "additional": {"logType": "info", "children": [], "durationId": "6e8419c0-32a8-4a96-a76e-d22eac36315e"}}, {"head": {"id": "7a92c60d-aef8-478e-a4f7-fbd36eb5c16b", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645318931800, "endTime": 21645320982700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "20f7bbc1-7d9a-4bd4-a744-c4a198ca1f0e", "logId": "9e5afcf1-e9a4-4e95-afef-4d6630a815c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20f7bbc1-7d9a-4bd4-a744-c4a198ca1f0e", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645316113600}, "additional": {"logType": "detail", "children": [], "durationId": "7a92c60d-aef8-478e-a4f7-fbd36eb5c16b"}}, {"head": {"id": "bf8eddf6-1d74-4967-b1b8-9cd2ecf927f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645316520000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cc0adfc-7289-4611-9453-5cb02e814323", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645316616100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd25e8ce-9070-47b3-8891-30f4f10d3400", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645317348900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2352954-8f16-415d-8c5f-32afdb0a92ce", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645319784700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a701c895-fae4-4c02-b8a1-9abdb64cd832", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645319892600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a725b503-d0eb-4442-9d36-8838c04da09d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645319928900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7dc253b-4399-4171-b62b-93c0522622ae", "name": "entry : default@PreviewProcessResource cost memory 0.0696258544921875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645319975800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f77c116-70f8-4c41-a758-5912fcee88b5", "name": "runTaskFromQueue task cost before running: 199 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645320909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e5afcf1-e9a4-4e95-afef-4d6630a815c8", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645318931800, "endTime": 21645320982700, "totalTime": 1084300}, "additional": {"logType": "info", "children": [], "durationId": "7a92c60d-aef8-478e-a4f7-fbd36eb5c16b"}}, {"head": {"id": "77c4b2d2-3520-4b69-8dee-bd1bce8c15ac", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645326588700, "endTime": 21645345368200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "68a7d440-f8b0-4772-8f68-bd7750713a77", "logId": "212c4e0c-77a1-47a9-afc9-acbff4e092d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68a7d440-f8b0-4772-8f68-bd7750713a77", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645323421000}, "additional": {"logType": "detail", "children": [], "durationId": "77c4b2d2-3520-4b69-8dee-bd1bce8c15ac"}}, {"head": {"id": "d2f70652-b099-4dd0-a84e-8ea69c126ba4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645323868300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb7dfeb-0262-48b6-ac4f-095b661be81a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645323956600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfbaf8d0-27f8-472b-a762-a7a730cf9a6d", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645326606800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "236e5f0d-9238-487e-ad40-14a8dd16b9b1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645345096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d197c1-0531-424c-a078-0265a502cd7a", "name": "entry : default@GenerateLoaderJson cost memory -0.9459991455078125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645345291700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212c4e0c-77a1-47a9-afc9-acbff4e092d7", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645326588700, "endTime": 21645345368200}, "additional": {"logType": "info", "children": [], "durationId": "77c4b2d2-3520-4b69-8dee-bd1bce8c15ac"}}, {"head": {"id": "9f67763f-a89f-4cf6-94b0-ba4a0253e1b9", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645359506400, "endTime": 21645373210500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b9403dd7-7889-4d6a-a1b6-6c6da4c1f418", "logId": "d6543d8d-8904-4de5-bc00-ed37a084e597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9403dd7-7889-4d6a-a1b6-6c6da4c1f418", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645353295200}, "additional": {"logType": "detail", "children": [], "durationId": "9f67763f-a89f-4cf6-94b0-ba4a0253e1b9"}}, {"head": {"id": "5d05317f-8f70-4861-9535-364462113fe4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645353999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3491b04f-fcbb-447c-9ca0-adc3c431bc1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645354315300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4305cf-e9cd-404c-973b-6c9d7706294c", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645356729700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ebe55e1-dbe2-46c2-9c55-1c7e9eb42114", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645359542500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424c5381-29c1-4012-b8ba-0983a2f6fdfe", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645372508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1c72ef-ff79-4940-a013-e58cf67da6fe", "name": "entry : default@PreviewCompileResource cost memory 0.5409698486328125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645372924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6543d8d-8904-4de5-bc00-ed37a084e597", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645359506400, "endTime": 21645373210500}, "additional": {"logType": "info", "children": [], "durationId": "9f67763f-a89f-4cf6-94b0-ba4a0253e1b9"}}, {"head": {"id": "8dc18a39-3dfa-45a9-9739-3e38dc251353", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378064300, "endTime": 21645378344300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "423ad4cc-9b62-4de4-bffb-650dd439608f", "logId": "5a8668e4-a1cb-478a-9104-98c28f45910e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "423ad4cc-9b62-4de4-bffb-650dd439608f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645377331800}, "additional": {"logType": "detail", "children": [], "durationId": "8dc18a39-3dfa-45a9-9739-3e38dc251353"}}, {"head": {"id": "b41e3861-6be5-4546-866d-aa7e6b85cfde", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645377844500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83db4a5b-d8a1-4be4-ab96-4a6a988a76d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645377978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5190a99e-94f9-4c11-9ce8-c86cf3b181f6", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "455cde5d-d34a-4172-9479-6cc95d39ba05", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378158200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe825d8-5b1d-424b-81a5-ee36dbfa999d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378191400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b025b9c-d3e8-4d76-a1f3-4b04eece2bbf", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384979248046875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378245000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ba7b45-499f-480a-af6a-733fe28ef83e", "name": "runTaskFromQueue task cost before running: 257 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378310500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8668e4-a1cb-478a-9104-98c28f45910e", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645378064300, "endTime": 21645378344300, "totalTime": 224900}, "additional": {"logType": "info", "children": [], "durationId": "8dc18a39-3dfa-45a9-9739-3e38dc251353"}}, {"head": {"id": "b6482b18-3555-423c-8adc-ef34cd387b43", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645381533700, "endTime": 21645383888700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0e1ac56d-9694-46f3-8b39-22a5f546f2d1", "logId": "5df33c5a-c99f-41df-b890-601444ac432a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e1ac56d-9694-46f3-8b39-22a5f546f2d1", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645380229200}, "additional": {"logType": "detail", "children": [], "durationId": "b6482b18-3555-423c-8adc-ef34cd387b43"}}, {"head": {"id": "5fc405a2-2ed8-4868-ba05-935506983ed3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645380793700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab24ad27-4d90-4178-8bb3-e2cc6caae38a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645380952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7161ecb-b1db-45ab-9cf5-c5ff249d74f4", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645381545500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6fbf56-ec8d-4365-be54-06dab8614e9f", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645383711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b9d9bb-8c39-4c01-9617-bed57c3f5e4c", "name": "entry : default@CopyPreviewProfile cost memory 0.10504150390625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645383820500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df33c5a-c99f-41df-b890-601444ac432a", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645381533700, "endTime": 21645383888700}, "additional": {"logType": "info", "children": [], "durationId": "b6482b18-3555-423c-8adc-ef34cd387b43"}}, {"head": {"id": "9f91a7b1-e425-405b-bbb5-fa9fbd132367", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386551200, "endTime": 21645386837500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0da61475-0253-4f1c-a9ef-985964411494", "logId": "05de2497-c5ea-4657-8701-34fa0a453868"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0da61475-0253-4f1c-a9ef-985964411494", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645385278200}, "additional": {"logType": "detail", "children": [], "durationId": "9f91a7b1-e425-405b-bbb5-fa9fbd132367"}}, {"head": {"id": "95dcc967-fc3b-4772-a72e-8ae698021ff6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645385721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789d5728-f8f0-4505-9db1-7e3a78ce8b49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645385797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0936a14-e859-4d11-997b-caa774fc9572", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f55890-8577-44ca-91b1-1ca4aac4b876", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386660700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a77a197-3152-48a5-9bca-e4a1f332dd03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386697900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad1d41a-de21-43a7-925f-53d475f6674e", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386755100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd7fef6-2903-4b2a-8f94-56e7f1cb7e41", "name": "runTaskFromQueue task cost before running: 265 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386809700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05de2497-c5ea-4657-8701-34fa0a453868", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645386551200, "endTime": 21645386837500, "totalTime": 240500}, "additional": {"logType": "info", "children": [], "durationId": "9f91a7b1-e425-405b-bbb5-fa9fbd132367"}}, {"head": {"id": "bf7a07d5-0480-4f22-b74d-d62c998c975e", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388553900, "endTime": 21645388772600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0340d0b3-7f14-465a-93c7-db1cf7e3d9bd", "logId": "efdbdd8c-eb83-40c9-98ef-4636af5f1e37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0340d0b3-7f14-465a-93c7-db1cf7e3d9bd", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388506300}, "additional": {"logType": "detail", "children": [], "durationId": "bf7a07d5-0480-4f22-b74d-d62c998c975e"}}, {"head": {"id": "71a75cd7-cc80-46b7-a830-88de1b423565", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b603533b-a117-44f4-9d76-3ab831c87c29", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388672800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f001343-08cb-437f-b87f-27171e8acd46", "name": "runTaskFromQueue task cost before running: 267 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efdbdd8c-eb83-40c9-98ef-4636af5f1e37", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645388553900, "endTime": 21645388772600, "totalTime": 171700}, "additional": {"logType": "info", "children": [], "durationId": "bf7a07d5-0480-4f22-b74d-d62c998c975e"}}, {"head": {"id": "d1cfe36c-fb66-4723-83cd-2c06ac1590e2", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645391397800, "endTime": 21645393713700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5e4f4693-5db0-4797-83bb-c77c3dece23c", "logId": "b193ece5-ab80-48c3-acc4-26e4d4a11861"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e4f4693-5db0-4797-83bb-c77c3dece23c", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645390193700}, "additional": {"logType": "detail", "children": [], "durationId": "d1cfe36c-fb66-4723-83cd-2c06ac1590e2"}}, {"head": {"id": "70ac0111-1ec5-48f7-be54-7209663e3593", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645390649300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "839002de-d823-4441-90d0-5aef2069d110", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645390742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03b1c35-bad8-4dbd-acf3-26634ee82ca8", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645391410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f0ca519-fbc1-44ac-844b-ba8486a10d80", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645393551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acbc080-27e8-476a-9cfa-508271a239c8", "name": "entry : default@PreviewUpdateAssets cost memory 0.1125335693359375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645393661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b193ece5-ab80-48c3-acc4-26e4d4a11861", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645391397800, "endTime": 21645393713700}, "additional": {"logType": "info", "children": [], "durationId": "d1cfe36c-fb66-4723-83cd-2c06ac1590e2"}}, {"head": {"id": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645400195200, "endTime": 21648998937600}, "additional": {"children": ["82432edf-9354-40c6-b15d-ab1458cbb8ac"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6fac43cb-641d-4803-bbda-d67a1d4b3376", "logId": "aa29089b-62dc-40fc-a24f-5b19bb731afd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fac43cb-641d-4803-bbda-d67a1d4b3376", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645395654800}, "additional": {"logType": "detail", "children": [], "durationId": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633"}}, {"head": {"id": "441fa730-fb0f-4026-bd0b-ee2f1e4d7fc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645396094400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdad66d1-12e5-43d6-9295-1278d72a2cd0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645396176900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9908b2b2-a850-42ce-8035-7aa45d9ca37e", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645400209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82432edf-9354-40c6-b15d-ab1458cbb8ac", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker3", "startTime": 21645420173700, "endTime": 21648998555100}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633", "logId": "7ed67977-bf17-492c-947f-e3280bebc11b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c87b0a0d-f0e6-48e1-b305-e99a14a3ca6f", "name": "entry : default@PreviewArkTS cost memory -0.8572616577148438", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645422048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed67977-bf17-492c-947f-e3280bebc11b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Worker3", "startTime": 21645420173700, "endTime": 21648998555100}, "additional": {"logType": "error", "children": [], "durationId": "82432edf-9354-40c6-b15d-ab1458cbb8ac", "parent": "aa29089b-62dc-40fc-a24f-5b19bb731afd"}}, {"head": {"id": "baf8bf8c-773e-4887-bc2b-ff71ea39c5b2", "name": "default@PreviewArkTS watch work[3] failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21648998625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa29089b-62dc-40fc-a24f-5b19bb731afd", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645400195200, "endTime": 21648998937600}, "additional": {"logType": "error", "children": ["7ed67977-bf17-492c-947f-e3280bebc11b"], "durationId": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633"}}, {"head": {"id": "6ec0fcf6-8121-41ef-86da-9e1cb5b19efe", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21648999107400}, "additional": {"logType": "debug", "children": [], "durationId": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633"}}, {"head": {"id": "ecb80026-f240-4aff-bf89-69d984c80da0", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/MainPage.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649000797300}, "additional": {"logType": "debug", "children": [], "durationId": "d2c3d25f-8aaf-42fc-8f9a-66f65fa3a633"}}, {"head": {"id": "ac8e773c-0657-49e3-aca1-79c48d3d518f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649018585500, "endTime": 21649018641900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0b7d1a5-db26-4abb-b3f9-98fa9a20a779", "logId": "038d7efc-7a3b-4ea0-922a-3bb7fb1bd2cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "038d7efc-7a3b-4ea0-922a-3bb7fb1bd2cd", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649018585500, "endTime": 21649018641900}, "additional": {"logType": "info", "children": [], "durationId": "ac8e773c-0657-49e3-aca1-79c48d3d518f"}}, {"head": {"id": "1dd752c3-0fea-4e55-bedb-64a622cb897a", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21645121930500, "endTime": 21649018766300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 6}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "663ee342-996f-45dc-a4d1-f348c8889873", "name": "BUILD FAILED in 3 s 897 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649018797000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "640170ac-f417-409e-a352-ec4c965f3c0b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649018939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42fa83b8-fe03-4ced-baf5-566de9362613", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649018977400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e05a621-0a13-4455-b41d-aac4de410860", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019015900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b04cf54-7d6e-4312-94f7-de9fc51e959a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142506e0-811f-4233-91fd-126ed6d961cd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205efa93-5da7-46e7-aea1-4763c54fe45b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019091400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22669d55-36c9-49ed-a8af-e5f6d080dd8a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5bd1a5-1838-4e8f-b1c3-a8840a3670ed", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019131800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01c641a-768f-4e2d-b06a-0ebd135f442b", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e58f5b90-2aa5-4ef6-bebb-3e16914863aa", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649019185500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6047d92-fa62-46ce-a5d7-657a7415fa6d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649022004000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15b42fb-9df6-4d02-ac61-9a1799631435", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649022818700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31de9368-6fd4-454d-b307-72832f9ac9e2", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649023171700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c8aac1-c4f1-4948-a5a0-476df3fa0705", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649023434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fee32ed-196b-4706-8461-85dd429c916d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649024438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e30bac0-fb98-49a4-9eee-816850175787", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649029527700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5929c486-b1cf-47c4-988e-857aa4a163c8", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649029889900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1fd977a-88b8-42b8-a6f3-49e916e66c48", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649030190900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc5e29cb-b6ab-4435-9f01-e7ad42db0f3b", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649030515300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297a48ac-887e-4232-a2c3-b1a48233be82", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21649030758000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}