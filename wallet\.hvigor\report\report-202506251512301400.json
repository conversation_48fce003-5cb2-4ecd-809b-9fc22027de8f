{"version": "2.0", "ppid": 12876, "events": [{"head": {"id": "5e088449-05ea-42b1-8bce-48a7e2e15d1f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21988929425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636c23ab-5de7-4663-8390-129a7d5f0e51", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21988936021900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "465a8f59-41cc-4fa3-a533-c3773815ff66", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21988936616500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23bf5b21-3b7a-407e-8d22-dcf6364e5599", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992884927700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d96ed14f-c085-43d6-aee5-d6d69beda946", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992892814600, "endTime": 21993035471100}, "additional": {"children": ["bdea5faf-b59b-400f-95f4-16d6cc07079f", "008744bf-0ee8-4b75-aaa1-7973752a7c41", "7fad96b2-6cf6-4d7c-81b9-17b1b15c95a2", "03c9862a-a8f5-4d95-a827-15792f888ec6", "9f3efd94-3c5a-4a8b-b796-ac28958725a8", "1b432b75-ff8a-4685-baea-b11efe2e9e78", "057d878e-2947-4228-8fa9-d1a7f1a285cd"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdea5faf-b59b-400f-95f4-16d6cc07079f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992892815900, "endTime": 21992903721000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "edd17fb9-f8b0-4eaf-ba8d-1b1cd368a32d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992903740500, "endTime": 21993034440600}, "additional": {"children": ["a4008a0e-b70a-4513-855a-5fef7a820e8e", "f575d10d-aac3-4c95-8319-47d8a4968ea9", "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "a936575d-e893-4288-9274-6db92ec66ac5", "6513fe19-ac83-4f2f-a138-d0e5a5544177", "2bc36f13-9917-4e65-8067-327cb46d71fc", "0a59edeb-368d-4aa3-a5e0-8b7588ad24b1", "6d5c1cfb-30f2-45bb-a1e4-543dfd21fa6e", "98fec4ad-d5b5-40a6-a916-0b369e61f193"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fad96b2-6cf6-4d7c-81b9-17b1b15c95a2", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993034459800, "endTime": 21993035458600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "34c55615-ce97-421d-9ca5-a89917d1ebb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03c9862a-a8f5-4d95-a827-15792f888ec6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993035462800, "endTime": 21993035468300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "9240ae30-4098-4c19-9b37-1dd4735d1720"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f3efd94-3c5a-4a8b-b796-ac28958725a8", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992895767900, "endTime": 21992895803900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "6e5828a9-3dcc-4b49-900b-20750e0d1118"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e5828a9-3dcc-4b49-900b-20750e0d1118", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992895767900, "endTime": 21992895803900}, "additional": {"logType": "info", "children": [], "durationId": "9f3efd94-3c5a-4a8b-b796-ac28958725a8", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "1b432b75-ff8a-4685-baea-b11efe2e9e78", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992900540800, "endTime": 21992900559700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "4b044808-8ecf-4818-a93e-35560f3c3ed8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b044808-8ecf-4818-a93e-35560f3c3ed8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992900540800, "endTime": 21992900559700}, "additional": {"logType": "info", "children": [], "durationId": "1b432b75-ff8a-4685-baea-b11efe2e9e78", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "1cf34c27-9e6d-4234-a98e-fb9868e741d1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992900607000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1483b934-e816-478e-b67d-5523bf9cc9dc", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992903587800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd17fb9-f8b0-4eaf-ba8d-1b1cd368a32d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992892815900, "endTime": 21992903721000}, "additional": {"logType": "info", "children": [], "durationId": "bdea5faf-b59b-400f-95f4-16d6cc07079f", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "a4008a0e-b70a-4513-855a-5fef7a820e8e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992908325100, "endTime": 21992908347200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "5dc25f76-6668-4a64-a26b-db8758e4abf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f575d10d-aac3-4c95-8319-47d8a4968ea9", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992908408000, "endTime": 21992911776700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "e8483c60-e433-43e9-b322-a367a12a6b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992911787700, "endTime": 21992977148200}, "additional": {"children": ["d0796082-804c-4774-a894-6261650708e5", "9f537a8e-269d-4bbd-a4f4-51a2af467dd3", "de05457c-0a4d-4184-a754-3a0b2b6f06c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "88340d22-49f3-4ba3-a195-1e59e6249cbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a936575d-e893-4288-9274-6db92ec66ac5", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977159000, "endTime": 21992999677100}, "additional": {"children": ["ab9a620b-cbe4-4a25-9223-f1d8b23e2568"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "1abb4320-861d-4dac-811e-ff4e4c295c9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6513fe19-ac83-4f2f-a138-d0e5a5544177", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992999687900, "endTime": 21993011003300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "d409d511-dc48-4714-8c5b-5dad87fd4352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bc36f13-9917-4e65-8067-327cb46d71fc", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993012006100, "endTime": 21993020221600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "8bae4996-9605-454b-b1e4-84b927c05455"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a59edeb-368d-4aa3-a5e0-8b7588ad24b1", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993020254800, "endTime": 21993034261700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "fb145314-6dd0-4666-b0cf-459d072166c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d5c1cfb-30f2-45bb-a1e4-543dfd21fa6e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993034297800, "endTime": 21993034429000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "83847718-e707-4cec-8923-d73b36243d96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dc25f76-6668-4a64-a26b-db8758e4abf9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992908325100, "endTime": 21992908347200}, "additional": {"logType": "info", "children": [], "durationId": "a4008a0e-b70a-4513-855a-5fef7a820e8e", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "e8483c60-e433-43e9-b322-a367a12a6b91", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992908408000, "endTime": 21992911776700}, "additional": {"logType": "info", "children": [], "durationId": "f575d10d-aac3-4c95-8319-47d8a4968ea9", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "d0796082-804c-4774-a894-6261650708e5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992912324500, "endTime": 21992912345900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "logId": "bd430bff-2157-4254-bf82-49a02c879b45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd430bff-2157-4254-bf82-49a02c879b45", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992912324500, "endTime": 21992912345900}, "additional": {"logType": "info", "children": [], "durationId": "d0796082-804c-4774-a894-6261650708e5", "parent": "88340d22-49f3-4ba3-a195-1e59e6249cbd"}}, {"head": {"id": "9f537a8e-269d-4bbd-a4f4-51a2af467dd3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992913933300, "endTime": 21992976482600}, "additional": {"children": ["e9deb4d9-0f9b-449d-986f-2d098a39cb6e", "d14eb4c0-14e8-4ef9-82fd-0446896baada"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "logId": "e8920876-1c3a-40c8-a0e1-82573a1bc700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9deb4d9-0f9b-449d-986f-2d098a39cb6e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992913934200, "endTime": 21992919888500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f537a8e-269d-4bbd-a4f4-51a2af467dd3", "logId": "5d11a585-e2cc-43fd-a758-2f04c71279c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d14eb4c0-14e8-4ef9-82fd-0446896baada", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992919905800, "endTime": 21992976472600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f537a8e-269d-4bbd-a4f4-51a2af467dd3", "logId": "80aa38c9-5957-46c3-875f-400aa800d7e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc4c0d6a-de65-4df7-80fd-4140631f1042", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992913940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2cfdaa0-bd32-4234-affd-08fbaa2c085b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992919761900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d11a585-e2cc-43fd-a758-2f04c71279c6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992913934200, "endTime": 21992919888500}, "additional": {"logType": "info", "children": [], "durationId": "e9deb4d9-0f9b-449d-986f-2d098a39cb6e", "parent": "e8920876-1c3a-40c8-a0e1-82573a1bc700"}}, {"head": {"id": "b373c0cb-dbbf-42c1-b8bd-2e89b0f48729", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992919915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc38e85b-bbc4-42c8-9d52-622f4e76eb9a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992926159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403c2138-668f-4ec3-a813-49432ee979e4", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992926287400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb5ac2c-d3e4-4f2c-ada2-62c8a7597027", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992926400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb6b94c2-d62a-46e0-80cd-733fbcb42e99", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992926466700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd8b55d-4124-45d1-8d1e-62403d5dd970", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992927793900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79c4b091-9445-4528-a13c-dd8477adeae4", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992931812200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "664f6a18-bac9-4e5c-9657-94273ad5c242", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992940526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a139cd-52cb-4233-90b6-ec277ff2b87e", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992958119700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9711cb-670d-43dd-8b08-5fe0b6a17d12", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992958275500}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 12}, "markType": "other"}}, {"head": {"id": "fb69ffe8-cb6b-46e0-8be8-fb3dd7c7937c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992958288000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 12}, "markType": "other"}}, {"head": {"id": "b594069a-c71c-4ec1-bc7d-b21becc59c4c", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992976256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece7634b-956c-410d-978e-3c9788423330", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992976372900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabe4a73-5e06-43a2-a9af-afc7efed2767", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992976416600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97331e29-6867-40f2-b866-4711e8fa4e92", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992976445200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80aa38c9-5957-46c3-875f-400aa800d7e2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992919905800, "endTime": 21992976472600}, "additional": {"logType": "info", "children": [], "durationId": "d14eb4c0-14e8-4ef9-82fd-0446896baada", "parent": "e8920876-1c3a-40c8-a0e1-82573a1bc700"}}, {"head": {"id": "e8920876-1c3a-40c8-a0e1-82573a1bc700", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992913933300, "endTime": 21992976482600}, "additional": {"logType": "info", "children": ["5d11a585-e2cc-43fd-a758-2f04c71279c6", "80aa38c9-5957-46c3-875f-400aa800d7e2"], "durationId": "9f537a8e-269d-4bbd-a4f4-51a2af467dd3", "parent": "88340d22-49f3-4ba3-a195-1e59e6249cbd"}}, {"head": {"id": "de05457c-0a4d-4184-a754-3a0b2b6f06c4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977108700, "endTime": 21992977127500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "logId": "64b827db-a7c6-4bb6-aa57-ab273b22fc5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64b827db-a7c6-4bb6-aa57-ab273b22fc5e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977108700, "endTime": 21992977127500}, "additional": {"logType": "info", "children": [], "durationId": "de05457c-0a4d-4184-a754-3a0b2b6f06c4", "parent": "88340d22-49f3-4ba3-a195-1e59e6249cbd"}}, {"head": {"id": "88340d22-49f3-4ba3-a195-1e59e6249cbd", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992911787700, "endTime": 21992977148200}, "additional": {"logType": "info", "children": ["bd430bff-2157-4254-bf82-49a02c879b45", "e8920876-1c3a-40c8-a0e1-82573a1bc700", "64b827db-a7c6-4bb6-aa57-ab273b22fc5e"], "durationId": "e21ea6ae-d1c5-4efe-b7f0-020ac0262378", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "ab9a620b-cbe4-4a25-9223-f1d8b23e2568", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977677300, "endTime": 21992999657800}, "additional": {"children": ["657f96c3-48b8-411a-bcd4-1900d81a754a", "28ef90d7-8675-47cd-b6bc-d6df22a6247a", "5bd304f8-da1e-46a5-980b-528f7a459cc6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a936575d-e893-4288-9274-6db92ec66ac5", "logId": "8f353035-cd00-445c-b747-7e1e3209d075"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "657f96c3-48b8-411a-bcd4-1900d81a754a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992980255700, "endTime": 21992980271800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab9a620b-cbe4-4a25-9223-f1d8b23e2568", "logId": "7f489145-6a0b-4899-8e5a-b9091af88563"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f489145-6a0b-4899-8e5a-b9091af88563", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992980255700, "endTime": 21992980271800}, "additional": {"logType": "info", "children": [], "durationId": "657f96c3-48b8-411a-bcd4-1900d81a754a", "parent": "8f353035-cd00-445c-b747-7e1e3209d075"}}, {"head": {"id": "28ef90d7-8675-47cd-b6bc-d6df22a6247a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992982489300, "endTime": 21992996770900}, "additional": {"children": ["fd984c95-ee40-44a9-937f-1eeaf43c8870", "c0b36009-25c1-4a34-8070-108964543695"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab9a620b-cbe4-4a25-9223-f1d8b23e2568", "logId": "8a2feb0b-3e4c-4a27-a92e-4bbf2c4b44ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd984c95-ee40-44a9-937f-1eeaf43c8870", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992982490500, "endTime": 21992985648500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28ef90d7-8675-47cd-b6bc-d6df22a6247a", "logId": "f9936838-050b-4e8a-a80f-9505ef79b01b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b36009-25c1-4a34-8070-108964543695", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992985663600, "endTime": 21992996760700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28ef90d7-8675-47cd-b6bc-d6df22a6247a", "logId": "9d5f1413-2f7e-4635-8b3e-ecac0412b0f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "535745ec-5a6b-4948-a1c3-4391ee8e682b", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992982498300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df04b165-3b19-4d82-bb01-bc4767ea9e25", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992985533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9936838-050b-4e8a-a80f-9505ef79b01b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992982490500, "endTime": 21992985648500}, "additional": {"logType": "info", "children": [], "durationId": "fd984c95-ee40-44a9-937f-1eeaf43c8870", "parent": "8a2feb0b-3e4c-4a27-a92e-4bbf2c4b44ec"}}, {"head": {"id": "705c9a9b-a366-4f7d-829d-60b1db5424ed", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992985673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c13838b9-05f7-4f2f-9b09-32b7cc7976f5", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993292700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff50b600-b5f6-49f9-b426-3631008f8220", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3c3226-b695-4735-a436-303080e8142e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993618800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd31767-1455-4856-9275-54fab356deb2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993708600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ff5eeb-11a8-4041-b552-19d786532420", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993751100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174604c5-02ec-4f4e-980a-e3830ac15d36", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddfad73-eab7-414c-85ab-c0479602a9df", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992993838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9252957d-c870-4aed-ac5a-2e5b7056286d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992996388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f464a030-149d-4cfc-a926-0fb8f087ca6a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992996634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8098b764-0282-4cc3-b88f-4d2a71c9ba57", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992996697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb036b5-8264-42e3-9089-f901518c4c65", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992996731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5f1413-2f7e-4635-8b3e-ecac0412b0f7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992985663600, "endTime": 21992996760700}, "additional": {"logType": "info", "children": [], "durationId": "c0b36009-25c1-4a34-8070-108964543695", "parent": "8a2feb0b-3e4c-4a27-a92e-4bbf2c4b44ec"}}, {"head": {"id": "8a2feb0b-3e4c-4a27-a92e-4bbf2c4b44ec", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992982489300, "endTime": 21992996770900}, "additional": {"logType": "info", "children": ["f9936838-050b-4e8a-a80f-9505ef79b01b", "9d5f1413-2f7e-4635-8b3e-ecac0412b0f7"], "durationId": "28ef90d7-8675-47cd-b6bc-d6df22a6247a", "parent": "8f353035-cd00-445c-b747-7e1e3209d075"}}, {"head": {"id": "5bd304f8-da1e-46a5-980b-528f7a459cc6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992999585800, "endTime": 21992999622300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab9a620b-cbe4-4a25-9223-f1d8b23e2568", "logId": "51b233eb-0dfc-47ec-b291-248f4fc85802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51b233eb-0dfc-47ec-b291-248f4fc85802", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992999585800, "endTime": 21992999622300}, "additional": {"logType": "info", "children": [], "durationId": "5bd304f8-da1e-46a5-980b-528f7a459cc6", "parent": "8f353035-cd00-445c-b747-7e1e3209d075"}}, {"head": {"id": "8f353035-cd00-445c-b747-7e1e3209d075", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977677300, "endTime": 21992999657800}, "additional": {"logType": "info", "children": ["7f489145-6a0b-4899-8e5a-b9091af88563", "8a2feb0b-3e4c-4a27-a92e-4bbf2c4b44ec", "51b233eb-0dfc-47ec-b291-248f4fc85802"], "durationId": "ab9a620b-cbe4-4a25-9223-f1d8b23e2568", "parent": "1abb4320-861d-4dac-811e-ff4e4c295c9c"}}, {"head": {"id": "1abb4320-861d-4dac-811e-ff4e4c295c9c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992977159000, "endTime": 21992999677100}, "additional": {"logType": "info", "children": ["8f353035-cd00-445c-b747-7e1e3209d075"], "durationId": "a936575d-e893-4288-9274-6db92ec66ac5", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "cc137090-8557-495f-9308-ba12bba66768", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993010598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d346f0-b231-4943-9d5f-afd7c5ef0d1d", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993010929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d409d511-dc48-4714-8c5b-5dad87fd4352", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992999687900, "endTime": 21993011003300}, "additional": {"logType": "info", "children": [], "durationId": "6513fe19-ac83-4f2f-a138-d0e5a5544177", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "98fec4ad-d5b5-40a6-a916-0b369e61f193", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993011805100, "endTime": 21993011990500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "logId": "1e57b78e-be5b-4909-b8d1-da9249349dfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67a7dbb3-a0aa-480b-bc89-932bac78fa35", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993011836500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e57b78e-be5b-4909-b8d1-da9249349dfc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993011805100, "endTime": 21993011990500}, "additional": {"logType": "info", "children": [], "durationId": "98fec4ad-d5b5-40a6-a916-0b369e61f193", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "2ecacfa5-2fde-4ff9-801f-e824daf85841", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993013391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0bd55c-d277-4c03-af66-d4f45d8d359e", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993018946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bae4996-9605-454b-b1e4-84b927c05455", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993012006100, "endTime": 21993020221600}, "additional": {"logType": "info", "children": [], "durationId": "2bc36f13-9917-4e65-8067-327cb46d71fc", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "adbeb7fd-12fe-4f5f-95d9-36ea9a2187e1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993020281800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a5348a-af4e-45df-bce0-589043a3ecdc", "name": "<PERSON><PERSON>le wallet Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993027842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f26e1af-0d27-4a0d-a55d-0c6ba70230c6", "name": "Module wallet's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993028048800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "073905fd-f1bd-45bf-8f45-7780c15d8bfd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993028389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b9b48cf-7593-4533-bad0-50b7fd12142f", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993031298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f0f40e9-121b-4130-9fe7-19e2860a1016", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993031457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb145314-6dd0-4666-b0cf-459d072166c7", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993020254800, "endTime": 21993034261700}, "additional": {"logType": "info", "children": [], "durationId": "0a59edeb-368d-4aa3-a5e0-8b7588ad24b1", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "09ea1a1d-9e87-462a-823a-9f8c83fb140a", "name": "Configuration phase cost:126 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993034325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83847718-e707-4cec-8923-d73b36243d96", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993034297800, "endTime": 21993034429000}, "additional": {"logType": "info", "children": [], "durationId": "6d5c1cfb-30f2-45bb-a1e4-543dfd21fa6e", "parent": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd"}}, {"head": {"id": "c0076b1a-da8a-4e58-91de-6a4170a7b9bd", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992903740500, "endTime": 21993034440600}, "additional": {"logType": "info", "children": ["5dc25f76-6668-4a64-a26b-db8758e4abf9", "e8483c60-e433-43e9-b322-a367a12a6b91", "88340d22-49f3-4ba3-a195-1e59e6249cbd", "1abb4320-861d-4dac-811e-ff4e4c295c9c", "d409d511-dc48-4714-8c5b-5dad87fd4352", "8bae4996-9605-454b-b1e4-84b927c05455", "fb145314-6dd0-4666-b0cf-459d072166c7", "83847718-e707-4cec-8923-d73b36243d96", "1e57b78e-be5b-4909-b8d1-da9249349dfc"], "durationId": "008744bf-0ee8-4b75-aaa1-7973752a7c41", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "057d878e-2947-4228-8fa9-d1a7f1a285cd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993035431200, "endTime": 21993035445900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d96ed14f-c085-43d6-aee5-d6d69beda946", "logId": "836f09d2-6dc8-4aa7-9712-b01b9e7d59eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "836f09d2-6dc8-4aa7-9712-b01b9e7d59eb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993035431200, "endTime": 21993035445900}, "additional": {"logType": "info", "children": [], "durationId": "057d878e-2947-4228-8fa9-d1a7f1a285cd", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "34c55615-ce97-421d-9ca5-a89917d1ebb4", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993034459800, "endTime": 21993035458600}, "additional": {"logType": "info", "children": [], "durationId": "7fad96b2-6cf6-4d7c-81b9-17b1b15c95a2", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "9240ae30-4098-4c19-9b37-1dd4735d1720", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993035462800, "endTime": 21993035468300}, "additional": {"logType": "info", "children": [], "durationId": "03c9862a-a8f5-4d95-a827-15792f888ec6", "parent": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573"}}, {"head": {"id": "4fe5f2f6-4a5c-4a9a-ab17-3784a5f40573", "name": "init", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992892814600, "endTime": 21993035471100}, "additional": {"logType": "info", "children": ["edd17fb9-f8b0-4eaf-ba8d-1b1cd368a32d", "c0076b1a-da8a-4e58-91de-6a4170a7b9bd", "34c55615-ce97-421d-9ca5-a89917d1ebb4", "9240ae30-4098-4c19-9b37-1dd4735d1720", "6e5828a9-3dcc-4b49-900b-20750e0d1118", "4b044808-8ecf-4818-a93e-35560f3c3ed8", "836f09d2-6dc8-4aa7-9712-b01b9e7d59eb"], "durationId": "d96ed14f-c085-43d6-aee5-d6d69beda946"}}, {"head": {"id": "05eb2e02-0155-4e6a-b050-6bc183f8bb71", "name": "Configuration task cost before running: 147 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993035607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918cf9c4-6b2d-4760-a4f2-c9a1096558a9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993040339700, "endTime": 21993052885200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed."], "detailId": "29c41bd3-a00d-410f-a45f-6a2fa7139e7a", "logId": "592820fa-3a7c-476d-8937-484537aaf047"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29c41bd3-a00d-410f-a45f-6a2fa7139e7a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993036905900}, "additional": {"logType": "detail", "children": [], "durationId": "918cf9c4-6b2d-4760-a4f2-c9a1096558a9"}}, {"head": {"id": "bc5d3e68-0288-47c5-b2ff-9f3aa20bfaf9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993037387900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c127751a-7215-43b2-a0ef-f7dbba259093", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993037472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c848510-999d-4057-99e5-e6888d74c5f8", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993040361500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ecb7119-7c48-4baa-97ce-4d82106cf9e6", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993046025600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53764b6-b93e-4791-99ec-5a33247945a9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993046269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0815a1d5-330a-4dd9-98d4-91e1395e7dec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993046402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277eeb2d-c68b-49f3-abe5-637bee436a64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993046441900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e1ad7a-7605-4c1d-bab5-04045d1c3dab", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993051943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f50bb8-44c2-42ff-b5c7-c79cc8115286", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993052493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d83bd97f-7476-4d8e-a573-7bc2756d14b3", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993052613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eecf318-1a2b-4a80-8c24-9d350dc79f30", "name": "entry : default@PreBuild cost memory 0.591094970703125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993052759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3a0f5d-e38c-41be-9623-0aad8b80051e", "name": "runTaskFromQueue task cost before running: 165 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993052834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592820fa-3a7c-476d-8937-484537aaf047", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993040339700, "endTime": 21993052885200, "totalTime": 12475600}, "additional": {"logType": "info", "children": [], "durationId": "918cf9c4-6b2d-4760-a4f2-c9a1096558a9"}}, {"head": {"id": "0e96c1d9-6dd1-4a12-a0c7-cece625f8895", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993059181500, "endTime": 21993065781000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed."], "detailId": "2238d1cc-c738-403f-af4f-367305a4196b", "logId": "f6dc7ce0-1a14-4e3c-920d-a51108e04638"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2238d1cc-c738-403f-af4f-367305a4196b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993057406900}, "additional": {"logType": "detail", "children": [], "durationId": "0e96c1d9-6dd1-4a12-a0c7-cece625f8895"}}, {"head": {"id": "a0ca7128-32ef-4e0e-b341-7ff60af4b80a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993058365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974526a0-8dcc-406f-9828-21b9a5d90609", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993058482600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d238e2e-9548-46d2-8f6b-65b89fa9810a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993059195000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b410912-2af1-4f0e-9fc3-404318013b94", "name": "entry:default@MergeProfile is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993060442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cea97e30-1ea7-4ec2-8725-3d797f246535", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993060562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1987173-0fe0-40ce-9ff1-c40efe0a15de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993060631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1a387c-39e7-443f-8944-65d4464d72be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993060662100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111e058a-9ff2-44e9-8519-e5f9ff57ffc9", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993060911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd263ce-e69f-4668-a154-1d1f3fa94b30", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993061166900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1969c292-4bfb-41ce-a836-6bdd54e45db3", "name": "Change app target API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993061235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a80bae-af1d-4413-bf75-1373efd68bd7", "name": "Change app minimum API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993061267700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150d9f20-f805-4fb7-8045-836057955364", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993061370500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44cbbdc5-9520-4340-9597-de15495aedab", "name": "entry : default@MergeProfile cost memory -3.2611083984375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993065515700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a112460-06e8-41da-88b0-469039fe950e", "name": "runTaskFromQueue task cost before running: 178 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993065709100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6dc7ce0-1a14-4e3c-920d-a51108e04638", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993059181500, "endTime": 21993065781000, "totalTime": 6494700}, "additional": {"logType": "info", "children": [], "durationId": "0e96c1d9-6dd1-4a12-a0c7-cece625f8895"}}, {"head": {"id": "d410f700-6a5c-4b50-ad34-dbb6542e9ee5", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993069328100, "endTime": 21993071463100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9debe2c1-d989-4229-b50b-dd6e5cdfd4c4", "logId": "6523f32e-bb25-4327-bd11-7862c74bb30a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9debe2c1-d989-4229-b50b-dd6e5cdfd4c4", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993067874500}, "additional": {"logType": "detail", "children": [], "durationId": "d410f700-6a5c-4b50-ad34-dbb6542e9ee5"}}, {"head": {"id": "bfed17df-b449-497b-b8c9-9f624e09ce56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993068427100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ab93ff-06b4-40bc-85f2-7a9455064938", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993068539400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f65786fa-d97c-4c78-b0a6-8c082c5135ad", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993069341000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f03d23-8bcd-48d2-91de-85d87525e6aa", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993070219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25d950a-1451-4b09-b2a8-e0743520074f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993071315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d9a28e-8231-43eb-8e2f-3f644abffcb5", "name": "entry : default@CreateBuildProfile cost memory 0.1044921875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993071409500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6523f32e-bb25-4327-bd11-7862c74bb30a", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993069328100, "endTime": 21993071463100}, "additional": {"logType": "info", "children": [], "durationId": "d410f700-6a5c-4b50-ad34-dbb6542e9ee5"}}, {"head": {"id": "f575218b-371f-4909-af83-74ae1a46a53c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075090100, "endTime": 21993075548900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9c8d679e-255b-4414-8ec2-a29b3fe2589a", "logId": "3d8f73f7-e8ae-44e3-9f31-a8efb0aa5bd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c8d679e-255b-4414-8ec2-a29b3fe2589a", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993073098200}, "additional": {"logType": "detail", "children": [], "durationId": "f575218b-371f-4909-af83-74ae1a46a53c"}}, {"head": {"id": "08fcc237-3294-405c-baa2-1afb6425b4bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993073797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16d0da9-90d9-4894-8f54-8b5f1013de69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993073947500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c9aebc-107b-4f6a-9b00-31993c7944ca", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075122300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b897c3c-7519-48cc-a24b-b7978c9bb176", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075304500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfdfb2d8-30e3-45b8-b162-550913e0652b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075366000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7206389-078e-4ead-aa29-b9d1e162921f", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075444500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b6ba019-a403-4aa6-839f-f332d3bf3817", "name": "runTaskFromQueue task cost before running: 187 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d8f73f7-e8ae-44e3-9f31-a8efb0aa5bd4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993075090100, "endTime": 21993075548900, "totalTime": 405700}, "additional": {"logType": "info", "children": [], "durationId": "f575218b-371f-4909-af83-74ae1a46a53c"}}, {"head": {"id": "5f8621f0-04f3-437d-b2f4-4e618aee7bc4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993086249200, "endTime": 21993087326700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "65bb3cde-94ef-4dc3-ab04-c4628d0d0bd1", "logId": "4c8b2fdd-2549-491a-a318-5cb91784e5d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65bb3cde-94ef-4dc3-ab04-c4628d0d0bd1", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993077750000}, "additional": {"logType": "detail", "children": [], "durationId": "5f8621f0-04f3-437d-b2f4-4e618aee7bc4"}}, {"head": {"id": "c7cbac84-b5b0-4852-9ec0-16fbbfe02bd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993078504300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c28be15-dbd7-4f83-bdc7-f2033d5283e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993078664600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e388606-62f8-450a-aa5e-114437cf15f1", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993086268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c83b77-d7d5-4e77-b860-cf56450e5a91", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993086491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2348302c-58a4-4a1a-a1d7-8b55705bc2cd", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993087157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73497fb4-969b-46c1-a2c0-fa55b123994d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06748199462890625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993087263700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c8b2fdd-2549-491a-a318-5cb91784e5d3", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993086249200, "endTime": 21993087326700}, "additional": {"logType": "info", "children": [], "durationId": "5f8621f0-04f3-437d-b2f4-4e618aee7bc4"}}, {"head": {"id": "e38bc6de-dc9c-41de-9b08-3650302a3289", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993090583600, "endTime": 21993200100400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json' has been changed."], "detailId": "b13fa263-1685-4177-9c1a-083b27e419d9", "logId": "57cb1c3a-0eec-4f1a-9176-276d51af7763"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b13fa263-1685-4177-9c1a-083b27e419d9", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993088818100}, "additional": {"logType": "detail", "children": [], "durationId": "e38bc6de-dc9c-41de-9b08-3650302a3289"}}, {"head": {"id": "bc999e80-af94-4753-8a86-2db3aa086ba2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993089298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78752b2-1375-401c-9d56-2774a6a25621", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993089399600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "470db4e8-1834-47ef-891e-7039d69b9c51", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993090599000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d18e1f-c292-4fb5-a4f5-d733c53d61a5", "name": "entry:default@ProcessProfile is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993091170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "047ba8bb-10c5-4859-9d11-a70f7a88442a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993091314400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824143d5-60e9-4583-b020-5bd5b3cd77ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993091391700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a265aac0-165a-4672-93b2-d2a75765019e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993091423500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47de897-65b3-46e9-be02-5e149c881f00", "name": "********", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993196597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4d5a02-b45b-4185-8c32-7ca879021ada", "name": "entry : default@ProcessProfile cost memory 0.33666229248046875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993199806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e63ac0a-36f5-426b-960d-e9d9799369b8", "name": "runTaskFromQueue task cost before running: 312 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993200026100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57cb1c3a-0eec-4f1a-9176-276d51af7763", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993090583600, "endTime": 21993200100400, "totalTime": 109403500}, "additional": {"logType": "info", "children": [], "durationId": "e38bc6de-dc9c-41de-9b08-3650302a3289"}}, {"head": {"id": "439032a1-6ea0-4a6c-a7b0-06eb0cced056", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993205626800, "endTime": 21993212743600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed."], "detailId": "a93a4cdd-d8c8-43f4-8967-7b7a484d4d66", "logId": "6149e3dc-2cc3-4500-9429-adbb234cd3cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a93a4cdd-d8c8-43f4-8967-7b7a484d4d66", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993202241000}, "additional": {"logType": "detail", "children": [], "durationId": "439032a1-6ea0-4a6c-a7b0-06eb0cced056"}}, {"head": {"id": "323fd050-2e4c-4a36-93d3-1dffb75b1dee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993202929900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3042f093-d5da-46c8-a558-0b47c934e2e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993203077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6cdc59-41cf-4699-be6d-c6a7db3046c2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993205647800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd99c5d-6327-47c9-8c00-482638d598ea", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993210897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b960ca-cac2-4f39-97b8-5e4ca4d715b8", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993211061700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431d8332-164a-4623-9fcb-081c145b11c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993211155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce77c8d-eec9-4837-bee6-479d85331b24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993211190600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "185ba5a6-fa22-4d86-a8ef-5d6b8c69adac", "name": "entry : default@ProcessRouterMap cost memory 0.21598052978515625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993212495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37bbb4b2-bfd9-45b7-8d4f-92ea0768d50a", "name": "runTaskFromQueue task cost before running: 324 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993212678300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6149e3dc-2cc3-4500-9429-adbb234cd3cb", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993205626800, "endTime": 21993212743600, "totalTime": 7022900}, "additional": {"logType": "info", "children": [], "durationId": "439032a1-6ea0-4a6c-a7b0-06eb0cced056"}}, {"head": {"id": "0047f7fd-9857-449f-9fab-22fadc9a63d5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993219301300, "endTime": 21993221895600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "cca3bc4f-2997-4576-9b66-fb1945bd264b", "logId": "f8b23fab-9d4c-48e0-9486-4d375d3299cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cca3bc4f-2997-4576-9b66-fb1945bd264b", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993215831900}, "additional": {"logType": "detail", "children": [], "durationId": "0047f7fd-9857-449f-9fab-22fadc9a63d5"}}, {"head": {"id": "86f9044c-f758-48c4-9dac-bc574ae5535f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993216392800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb47c07-95f7-4cec-a00b-a06980212f5b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993216514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a442f88-abe9-4fc9-8603-49f42def944a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993217433400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18b09b7-565e-46d0-959b-fbe6f9b4ad13", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993220419300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4da718-ca09-4a97-8784-a4baf5fac795", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993220577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f36f17-3014-4927-a02e-d77ab8c5c240", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993220621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa80b1f0-1e18-49b9-a7c0-6842386abaaf", "name": "entry : default@PreviewProcessResource cost memory 0.0774688720703125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993220682400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b54e876-597f-4010-bfd2-1582a771cc9b", "name": "runTaskFromQueue task cost before running: 334 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993221789400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b23fab-9d4c-48e0-9486-4d375d3299cc", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993219301300, "endTime": 21993221895600, "totalTime": 1423000}, "additional": {"logType": "info", "children": [], "durationId": "0047f7fd-9857-449f-9fab-22fadc9a63d5"}}, {"head": {"id": "96548682-e351-407b-a47a-67907b91a773", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993227829900, "endTime": 21993251517600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json' has been changed."], "detailId": "d952f73b-288f-4d99-b275-76eb8d4ffc47", "logId": "e8861cd9-77f3-46ca-b889-52700488ce27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d952f73b-288f-4d99-b275-76eb8d4ffc47", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993224354000}, "additional": {"logType": "detail", "children": [], "durationId": "96548682-e351-407b-a47a-67907b91a773"}}, {"head": {"id": "9a4dc4b0-c86a-46b1-9327-26c7476cf1d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993224853800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd77a77b-8bdc-46ea-9ca2-24ff61dbf5b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993224966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72fca3aa-91a3-4229-913b-120003982480", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993227846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d153a85d-2038-4b36-9c49-1888aa75c857", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993245384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ace4ef-200b-4684-9c4b-5e3f5caa14a5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993245559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ee2fdeb-b77e-417d-9441-6832fbb651df", "name": "entry : default@GenerateLoaderJson cost memory -2.4556961059570312", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993251216400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097600d4-3775-4183-a666-95eca10b2b46", "name": "runTaskFromQueue task cost before running: 363 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993251449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8861cd9-77f3-46ca-b889-52700488ce27", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993227829900, "endTime": 21993251517600, "totalTime": 23585200}, "additional": {"logType": "info", "children": [], "durationId": "96548682-e351-407b-a47a-67907b91a773"}}, {"head": {"id": "d5f93594-78ee-4b8a-98dd-9c45510d699c", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993262730100, "endTime": 21993448544500}, "additional": {"children": ["4d7a7e43-94fc-4f7e-b6c7-e8535519817a", "0c5b40a2-2c62-4942-bc91-cdcb694afcb0", "e7f432f5-f697-4ea5-b837-5b8911c004e1", "0a60ffd7-bcc2-4f05-a359-0239dd88c889"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources' has been changed."], "detailId": "3d879a8a-67c7-483e-996c-14a75f8c0188", "logId": "13159ace-e943-4fae-9ba8-abbba5d613cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d879a8a-67c7-483e-996c-14a75f8c0188", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993258934900}, "additional": {"logType": "detail", "children": [], "durationId": "d5f93594-78ee-4b8a-98dd-9c45510d699c"}}, {"head": {"id": "8a79b3bb-6b7a-468b-a820-75b5df70da9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993259491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fe2e1a-3e78-4b61-8c9c-ed8432ebf6fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993259607200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9205fb3f-c11b-493e-97a2-af2f4487ddcd", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993260464900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f5f0ca4-e1dd-4f21-9692-20082fae1c12", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993262775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "351f16f0-0528-4b56-be94-bf369b0e008c", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993269848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427d5879-f119-4c64-aa8e-0b323bd5729a", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993270013700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7a7e43-94fc-4f7e-b6c7-e8535519817a", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993270998700, "endTime": 21993286382700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5f93594-78ee-4b8a-98dd-9c45510d699c", "logId": "5866187b-2a22-4436-9775-c108b5dd853f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5866187b-2a22-4436-9775-c108b5dd853f", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993270998700, "endTime": 21993286382700}, "additional": {"logType": "info", "children": [], "durationId": "4d7a7e43-94fc-4f7e-b6c7-e8535519817a", "parent": "13159ace-e943-4fae-9ba8-abbba5d613cf"}}, {"head": {"id": "d3845630-114b-44b4-ae51-5e842d3e50a6", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993286703300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c5b40a2-2c62-4942-bc91-cdcb694afcb0", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993287580100, "endTime": 21993336122100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5f93594-78ee-4b8a-98dd-9c45510d699c", "logId": "dac2c664-e207-41cb-b305-6c741581aca5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49226062-1d78-4f71-b7e6-1e38e5a16624", "name": "current process  memoryUsage: {\n  rss: 123564032,\n  heapTotal: 131686400,\n  heapUsed: 109718472,\n  external: 3141071,\n  arrayBuffers: 134972\n} os memoryUsage :12.895980834960938", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993288393200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d731759e-f0d8-4546-bf66-e3a9fb42700e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993333636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac2c664-e207-41cb-b305-6c741581aca5", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993287580100, "endTime": 21993336122100}, "additional": {"logType": "info", "children": [], "durationId": "0c5b40a2-2c62-4942-bc91-cdcb694afcb0", "parent": "13159ace-e943-4fae-9ba8-abbba5d613cf"}}, {"head": {"id": "b8cc22c3-7ff9-4d3b-b375-94af473085b4", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993336313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7f432f5-f697-4ea5-b837-5b8911c004e1", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993337328800, "endTime": 21993375925900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5f93594-78ee-4b8a-98dd-9c45510d699c", "logId": "4e660b57-410f-40c1-a0cd-fac8a46959d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "259a166d-cd9b-45a3-afa0-ece0f68d45b1", "name": "current process  memoryUsage: {\n  rss: 123564032,\n  heapTotal: 131686400,\n  heapUsed: 109999432,\n  external: 3149389,\n  arrayBuffers: 143305\n} os memoryUsage :12.905906677246094", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993338272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9922e3-155c-4e3b-afbd-082536e791eb", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993372761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e660b57-410f-40c1-a0cd-fac8a46959d2", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993337328800, "endTime": 21993375925900}, "additional": {"logType": "info", "children": [], "durationId": "e7f432f5-f697-4ea5-b837-5b8911c004e1", "parent": "13159ace-e943-4fae-9ba8-abbba5d613cf"}}, {"head": {"id": "fabb24af-e0f1-4227-8d8f-50c0ebd54e75", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***n',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993376168200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a60ffd7-bcc2-4f05-a359-0239dd88c889", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993377283900, "endTime": 21993446736500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5f93594-78ee-4b8a-98dd-9c45510d699c", "logId": "207a32dd-17ea-4732-93a8-4f8293cb562d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d84f0bbb-3538-4f9a-8738-17ec2e0a1ed3", "name": "current process  memoryUsage: {\n  rss: 123564032,\n  heapTotal: 131686400,\n  heapUsed: 110330944,\n  external: 3149515,\n  arrayBuffers: 144310\n} os memoryUsage :12.907928466796875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993378209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f71243e-3e8d-4968-8bb0-bc949943fd79", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993442733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207a32dd-17ea-4732-93a8-4f8293cb562d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993377283900, "endTime": 21993446736500}, "additional": {"logType": "info", "children": [], "durationId": "0a60ffd7-bcc2-4f05-a359-0239dd88c889", "parent": "13159ace-e943-4fae-9ba8-abbba5d613cf"}}, {"head": {"id": "8cdc312a-1681-4103-b9e0-9fff522ae005", "name": "entry : default@PreviewCompileResource cost memory 1.3556900024414062", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993448122100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d982f09e-dd8b-48a9-8d61-dfc79dd94f8e", "name": "runTaskFromQueue task cost before running: 560 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993448428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13159ace-e943-4fae-9ba8-abbba5d613cf", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993262730100, "endTime": 21993448544500, "totalTime": 185615500}, "additional": {"logType": "info", "children": ["5866187b-2a22-4436-9775-c108b5dd853f", "dac2c664-e207-41cb-b305-6c741581aca5", "4e660b57-410f-40c1-a0cd-fac8a46959d2", "207a32dd-17ea-4732-93a8-4f8293cb562d"], "durationId": "d5f93594-78ee-4b8a-98dd-9c45510d699c"}}, {"head": {"id": "e641ec94-1692-4051-ab6c-b53b23da5b15", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453630200, "endTime": 21993454087400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c6392c77-e98b-4cc1-9e2e-d3698f0af52f", "logId": "7f08b14d-261b-46d8-818e-e25a37d8663c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6392c77-e98b-4cc1-9e2e-d3698f0af52f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993452460500}, "additional": {"logType": "detail", "children": [], "durationId": "e641ec94-1692-4051-ab6c-b53b23da5b15"}}, {"head": {"id": "72719611-a184-4173-8e65-012ab113e581", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18726df9-c381-4f31-9367-15046e108351", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa88f5fe-b116-4f8d-914f-a1f5070b5345", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0766a16-befe-4712-b6d7-f65a3374d87c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453782400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5ba8a50-b5f5-4cc5-9c41-cb36ce63917a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453834800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22df4407-4733-45c8-97e1-1cae96b8020b", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453911000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "771d62cf-d5cb-4ec9-8d36-bb7521f5a0ab", "name": "runTaskFromQueue task cost before running: 566 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993454025500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f08b14d-261b-46d8-818e-e25a37d8663c", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993453630200, "endTime": 21993454087400, "totalTime": 335600}, "additional": {"logType": "info", "children": [], "durationId": "e641ec94-1692-4051-ab6c-b53b23da5b15"}}, {"head": {"id": "75b100da-e629-4430-b5bb-80b2f85b4458", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993458562100, "endTime": 21993467312600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "74baee12-c2f2-4147-85e2-83f3e27af106", "logId": "c2c3ce18-18a7-4f2b-a558-88a92b771e59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74baee12-c2f2-4147-85e2-83f3e27af106", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993456598300}, "additional": {"logType": "detail", "children": [], "durationId": "75b100da-e629-4430-b5bb-80b2f85b4458"}}, {"head": {"id": "d5d927cb-b077-4653-a297-f88f9170f476", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993457695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7767035f-5cdc-464d-b166-41e8c8a50063", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993457863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d9e12b-b985-4a92-a753-7a97cea5a878", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993458579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac72dd3-cef6-4c41-9724-bd382e555042", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993459839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb6b8ef-460a-4de7-9cb6-6a7b7603e64e", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993459968000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252905f5-c828-4cbd-8a48-eadbe35307df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993460037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ce429d-66b9-4e19-b254-62a1efded9d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993460072600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98c8033-1b48-412d-b708-ffdc3f2d5aff", "name": "entry : default@CopyPreviewProfile cost memory 0.23236083984375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993467058600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8695e91-5b31-4f03-a62e-57c29e552b65", "name": "runTaskFromQueue task cost before running: 579 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993467246800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c3ce18-18a7-4f2b-a558-88a92b771e59", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993458562100, "endTime": 21993467312600, "totalTime": 8653000}, "additional": {"logType": "info", "children": [], "durationId": "75b100da-e629-4430-b5bb-80b2f85b4458"}}, {"head": {"id": "483de656-c7f0-48f3-8c84-ad2500027b9f", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993470910200, "endTime": 21993471239000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c264cb81-9e48-4b88-aa11-dd4e2bf20f63", "logId": "015bf1ab-33a9-4ea0-a728-eace59594f31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c264cb81-9e48-4b88-aa11-dd4e2bf20f63", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993469443400}, "additional": {"logType": "detail", "children": [], "durationId": "483de656-c7f0-48f3-8c84-ad2500027b9f"}}, {"head": {"id": "015f9d97-c086-4ee3-bbe0-65ec9856cf6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993470035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d92f674b-9e18-4f38-8abd-13ef458f67a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993470154300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6659ec4d-66aa-400d-8d48-3bbfe4a5e79a", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993470922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7f9b82-20ed-4464-bef5-916f6295020e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993471038900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adaab937-3ec8-4832-81b4-dbc77e0bcca5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993471074800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5fede4a-e1fd-4001-8551-543b69aa87fd", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993471147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d03c48-a4a4-4700-84c5-720fe1ef9449", "name": "runTaskFromQueue task cost before running: 583 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993471207000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "015bf1ab-33a9-4ea0-a728-eace59594f31", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993470910200, "endTime": 21993471239000, "totalTime": 278200}, "additional": {"logType": "info", "children": [], "durationId": "483de656-c7f0-48f3-8c84-ad2500027b9f"}}, {"head": {"id": "ed332542-14ce-48a9-9b8a-4eb6ef38d329", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473463500, "endTime": 21993473777600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a28508fa-b460-47bb-8974-b4c0781e64a1", "logId": "fde1e6b9-2e0c-4b0c-a735-9475120c37ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a28508fa-b460-47bb-8974-b4c0781e64a1", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473368500}, "additional": {"logType": "detail", "children": [], "durationId": "ed332542-14ce-48a9-9b8a-4eb6ef38d329"}}, {"head": {"id": "e4cdb82f-6351-46bf-9cfd-6be502980390", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473488300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b002b1-08f4-4dc0-aed4-54097c2dccec", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473657600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c8ba1b4-d374-4905-80d8-312b87637bb2", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473742200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fde1e6b9-2e0c-4b0c-a735-9475120c37ae", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993473463500, "endTime": 21993473777600, "totalTime": 254600}, "additional": {"logType": "info", "children": [], "durationId": "ed332542-14ce-48a9-9b8a-4eb6ef38d329"}}, {"head": {"id": "06b39cd6-a7f9-4705-acee-c53ac12aae62", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993478739100, "endTime": 21993484668700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "f92e3af3-a0d6-439f-9012-478ca6eab4d0", "logId": "e28a62c2-b949-4c9d-8968-15365ccca3eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f92e3af3-a0d6-439f-9012-478ca6eab4d0", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993477157900}, "additional": {"logType": "detail", "children": [], "durationId": "06b39cd6-a7f9-4705-acee-c53ac12aae62"}}, {"head": {"id": "1c3a743c-72d1-4075-ae81-3fbffc8911fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993477754000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1e4f37-b219-4dfa-9906-896088499533", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993477884100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc66021-9994-4b54-ad78-2c1c09060597", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993478751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6fea9f-10a9-4b2a-ac3e-865b06867c0b", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993481016100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08491b35-277c-4253-b112-e9e5d5f107b3", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993481222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa0d8e6-6649-4f76-a7f8-68235d239cdd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993481297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dca9e57-72bc-4ba6-87c2-7f33940a74ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993481332800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "818f9170-ed8a-430d-8102-b9339e202f3b", "name": "entry : default@PreviewUpdateAssets cost memory 0.1370391845703125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993484412100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6926a2f9-4082-4fbe-87b7-9140471470a3", "name": "runTaskFromQueue task cost before running: 596 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993484603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e28a62c2-b949-4c9d-8968-15365ccca3eb", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993478739100, "endTime": 21993484668700, "totalTime": 5830300}, "additional": {"logType": "info", "children": [], "durationId": "06b39cd6-a7f9-4705-acee-c53ac12aae62"}}, {"head": {"id": "d0246d90-93eb-40a4-87c9-14429ea00333", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993492908000, "endTime": 21997416243900}, "additional": {"children": ["abb9e1f1-3365-43c0-b7e9-d6daf8793842"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dabe369c-f8b7-40d2-a182-054fa27b5fef", "logId": "0c2dda34-b279-4539-9137-bf73bb5e3b4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dabe369c-f8b7-40d2-a182-054fa27b5fef", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993487494700}, "additional": {"logType": "detail", "children": [], "durationId": "d0246d90-93eb-40a4-87c9-14429ea00333"}}, {"head": {"id": "c95c9174-b851-4608-a2f5-17b3306c383c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993487967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d44e8a9-90bd-4cf0-ba95-115b1b718f47", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993488064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23735a9-f496-4210-a000-80a482eff785", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993492923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb9e1f1-3365-43c0-b7e9-d6daf8793842", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker4", "startTime": 21993512829900, "endTime": 21997415109900}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d0246d90-93eb-40a4-87c9-14429ea00333", "logId": "514f4ab8-3108-4454-a29d-9d00b4b94c6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe54627-790f-4c91-a44f-98bf415a97a1", "name": "entry : default@PreviewArkTS cost memory 0.9585113525390625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993516431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514f4ab8-3108-4454-a29d-9d00b4b94c6b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Worker4", "startTime": 21993512829900, "endTime": 21997415109900}, "additional": {"logType": "error", "children": [], "durationId": "abb9e1f1-3365-43c0-b7e9-d6daf8793842", "parent": "0c2dda34-b279-4539-9137-bf73bb5e3b4c"}}, {"head": {"id": "36fca342-61f2-4444-ac04-8689d5e8fbaa", "name": "default@PreviewArkTS watch work[4] failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997415232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c2dda34-b279-4539-9137-bf73bb5e3b4c", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21993492908000, "endTime": 21997416243900}, "additional": {"logType": "error", "children": ["514f4ab8-3108-4454-a29d-9d00b4b94c6b"], "durationId": "d0246d90-93eb-40a4-87c9-14429ea00333"}}, {"head": {"id": "fc1f57aa-b509-41fe-96e6-d7254cb592db", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997418116800}, "additional": {"logType": "debug", "children": [], "durationId": "d0246d90-93eb-40a4-87c9-14429ea00333"}}, {"head": {"id": "d012abb7-fb01-4054-8792-53052c65508b", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997421774200}, "additional": {"logType": "debug", "children": [], "durationId": "d0246d90-93eb-40a4-87c9-14429ea00333"}}, {"head": {"id": "f7f6a7fb-46b3-4923-b9bf-38117ab1c4c7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997450126700, "endTime": 21997450248100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b30d1cd8-ad42-4e6a-b4eb-045643ee6335", "logId": "33162ebf-fa6a-424d-8595-06e1d7ca0cc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33162ebf-fa6a-424d-8595-06e1d7ca0cc8", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997450126700, "endTime": 21997450248100}, "additional": {"logType": "info", "children": [], "durationId": "f7f6a7fb-46b3-4923-b9bf-38117ab1c4c7"}}, {"head": {"id": "d6d501b3-8d1d-4af7-8f84-22b25023bb39", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21992888654400, "endTime": 21997450568900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 12}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "4c47effe-1f08-484a-adae-06ed4b8dec61", "name": "BUILD FAILED in 4 s 562 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997450626700}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "413c6292-9366-4a62-9207-02098351465e", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997452569700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d505c780-8341-42c2-ae0e-1cc1d765c46a", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997453177600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8432725-f1a7-43cc-bd61-6e4caf3702ce", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997453290900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c13a39a-7a78-483f-90b9-17ca6ebc9cff", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997453700100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653a96e9-df0d-44d6-a8df-7d70d135dbed", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997454503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042181be-b7ba-434a-a78c-3a3f9391c351", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997454930500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9127c27c-5db8-45c9-a8d6-b0000c810a76", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997455254100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab43308d-d9b1-4990-8f4a-f61a9b88ee75", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997455524000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa05463f-d4bd-4b79-9882-45de2a24f09e", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997455780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440e1f4d-9574-4e51-a61f-4fceddb1ff49", "name": "Incremental task entry:default@PreBuild post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997456180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b2015f-c1b7-4757-a8c6-17e0ac313a76", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997456266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140efabb-c17e-4b46-a735-14c274a68408", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997456539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940165bf-9408-47cd-9839-d8615c785f1e", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997456818900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b46276-f9fe-4c68-b173-c471880bc50f", "name": "Update task entry:default@MergeProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997456903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff1bc59a-fea7-40b8-b860-ffa9395b0d4b", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997457255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64250b81-551e-4602-ab7f-6e8a6bb0641d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997457325800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b6fd93-d8f6-4ee4-b7dd-80905aa81968", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997457490400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9df50409-eaca-40dc-8b24-a43b6cf923ee", "name": "Update task entry:default@ProcessProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997457747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b92982-9482-42cf-bea8-72a9928144b3", "name": "Update task entry:default@ProcessProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997457943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316d072c-8e32-4092-a2d6-c09588d27c93", "name": "Incremental task entry:default@ProcessProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997458989300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c399c6a0-ceb2-4bc6-8c96-45d7f7d9d72d", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997460668600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8d14494-c17c-4b06-9e18-8ec835265975", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997461109500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ecdd41-b90c-4edd-8368-5e332e8705e7", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997461466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7698600-357a-457d-976e-5f3ef7e8d538", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997461673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43bf4a92-0075-43ca-a999-8104da734c7b", "name": "Update task entry:default@ProcessRouterMap output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997462367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2ca60e-e74f-4535-91a7-e09a93e86517", "name": "Update task entry:default@ProcessRouterMap output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997462667800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c8e2b3-2957-4e4a-b62c-df2edd69dbd3", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997462963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c838b6a-f034-4cb5-bee7-c26bc8db5cb4", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997468096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3e185dd-edfe-401b-8915-45ca9cc712b0", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997468559700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863dd150-4750-4bc2-bdd4-3f6d0bbe49ee", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997468937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd81b64-a4e3-499f-ae33-4d0450ca25df", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997469018000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5002da-2c36-418c-b71b-913803167f10", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997469331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150f545a-afe9-4aa9-8cfe-7939686783b2", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997476909100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b325a292-bef8-4101-9026-0fe7d23ec1d9", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997477325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9374e3d-a5ce-4344-9022-ccfdfae97cdc", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997479207000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4805dd1f-9a5f-423b-b72d-4ce2f5cb770f", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997486189100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49205796-8c80-4ae7-b62f-d803929908f4", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997486866900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e01ab357-6207-4ef3-88b1-a5ec178cebad", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997487104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82fc7a45-208e-43ff-b981-4f5bd2c35ef2", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997487786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45144256-e5a0-4851-8023-2aaf5be9d836", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997488526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec779ad-73a1-4337-be98-1fbbbaa3ff77", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997488945000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2cc7afa-70dc-4bee-a58c-46e442ae3461", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997489309900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bcc87e5-0479-48f9-8fae-4a599f939850", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997489738400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44099641-f755-4a5a-baf0-1380c8aa478c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997493371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa777a8-bbcf-4fc3-8d57-e0fa8a7e7b8f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997494279800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8706c3-896c-4b86-930b-8ed8b4d78ad6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997494575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd414b6-c943-4e51-9693-83c82c587e43", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997494827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b082a0-8061-4d59-b2fb-192d2b85c80e", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997495479800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e85960-f224-48be-b1f8-43a05ee33b32", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997500017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cd8a921-1ed7-4b6d-a1fa-f166c11eb8c4", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997500453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f0bca5-c347-447c-ae78-26460209f3ba", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997500809300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f62223f-9655-4ebe-b77f-bf16509d9e7d", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997501160500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d11ef9b-d483-4bd8-9c93-ccd798d3c336", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997501498600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}