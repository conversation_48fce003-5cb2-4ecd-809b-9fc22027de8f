{"version": "2.0", "ppid": 12876, "events": [{"head": {"id": "dce267cd-18e1-4ad9-a642-9e24a3f6d22b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997519700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31486e04-5559-41e2-9026-b30a946c29cf", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997535271200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3639a0-33bb-4e0c-9149-6673b1b3fc0d", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 21997535605600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80902fac-2046-4abb-9d7a-a1c988164ff7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046839882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046859070200, "endTime": 22047387036700}, "additional": {"children": ["faa1fc26-ff42-4817-8e86-406244fc9790", "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "2c2a0854-6ca5-4f64-8f49-45f55f9b8bd3", "351fe112-f51c-4791-a850-fcaa493cc1ed", "8795c854-65b8-40be-a1c8-495a3add3aeb", "4830531a-5b78-41e2-98a5-10390d3f6e4a", "3235b011-889c-4d51-bb87-edf509779f1a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4978a377-9078-4b34-94cc-0ce35bae3d03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "faa1fc26-ff42-4817-8e86-406244fc9790", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046859073700, "endTime": 22046892238500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "b64f44ae-5a2d-45f8-aee6-af458ad7e198"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046892313000, "endTime": 22047379148000}, "additional": {"children": ["0ae9357a-36cb-4ddd-a270-4d062bbae7d3", "3fbd2cd0-76a5-4c86-a0a7-3bf7eb296d13", "6f5586e2-4a1c-4542-88e4-e0696cf73767", "e57b067f-caa0-471f-a251-a07b988777f3", "95a71619-ee81-4980-9c85-e36950efa241", "aa99fc2e-07bf-4854-8c30-e18f5b8aa1cf", "76d8c06e-47b4-4e3d-a584-082af9f32209", "afc91c59-097b-4435-aa68-01d320c90ebc", "07720041-16b1-42a3-8219-7ac96f04c091"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c2a0854-6ca5-4f64-8f49-45f55f9b8bd3", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047379229200, "endTime": 22047386921600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "5ae7212e-0959-40cf-b21c-c62383d16cb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "351fe112-f51c-4791-a850-fcaa493cc1ed", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047386943100, "endTime": 22047387015700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "3bbd4bb6-6f42-4d45-ace9-79338727fdda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8795c854-65b8-40be-a1c8-495a3add3aeb", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046867068400, "endTime": 22046867131000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "2f1c577a-4634-4123-9361-b8c7dc3f4e72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f1c577a-4634-4123-9361-b8c7dc3f4e72", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046867068400, "endTime": 22046867131000}, "additional": {"logType": "info", "children": [], "durationId": "8795c854-65b8-40be-a1c8-495a3add3aeb", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "4830531a-5b78-41e2-98a5-10390d3f6e4a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046882089900, "endTime": 22046882117300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "a9cf9eb7-64b0-4fc5-93ac-7883a4c7087d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9cf9eb7-64b0-4fc5-93ac-7883a4c7087d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046882089900, "endTime": 22046882117300}, "additional": {"logType": "info", "children": [], "durationId": "4830531a-5b78-41e2-98a5-10390d3f6e4a", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "91c5c11f-ae56-4b02-98c5-ea4b821f264d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046882245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af476edb-458e-4369-a438-b5d7fa02ca13", "name": "Cache service initialization finished in 10 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046891992600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64f44ae-5a2d-45f8-aee6-af458ad7e198", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046859073700, "endTime": 22046892238500}, "additional": {"logType": "info", "children": [], "durationId": "faa1fc26-ff42-4817-8e86-406244fc9790", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "0ae9357a-36cb-4ddd-a270-4d062bbae7d3", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046913965100, "endTime": 22046913982400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "920d32da-237f-480b-bd38-8a921b34b544"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fbd2cd0-76a5-4c86-a0a7-3bf7eb296d13", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046914039500, "endTime": 22046926378300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "aff3adb9-62db-4e8c-a7ae-d93b8bf871b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f5586e2-4a1c-4542-88e4-e0696cf73767", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046926397200, "endTime": 22047167202400}, "additional": {"children": ["74b11729-b300-4ea5-b7d9-41188c28e9ec", "136e298f-f5b6-4575-9f0e-55c9dfe2f64d", "26ded7c0-ca46-4489-b834-bd296da04e80"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "6074a879-a0ac-4f5a-9433-9eec9e0da9d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e57b067f-caa0-471f-a251-a07b988777f3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047167253000, "endTime": 22047233760400}, "additional": {"children": ["87a2729c-524a-4b9c-a06e-15788372d78c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "ff53f33f-7e9b-451a-b0d8-e25c84037055"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95a71619-ee81-4980-9c85-e36950efa241", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047233773400, "endTime": 22047293826400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "fc61247d-a362-4585-a3b1-840e849f5ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa99fc2e-07bf-4854-8c30-e18f5b8aa1cf", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047298183100, "endTime": 22047340427000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "81176115-abd9-4004-b939-59d4f65ec700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76d8c06e-47b4-4e3d-a584-082af9f32209", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047340470900, "endTime": 22047378532500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "b421c7ca-2557-4a05-a9db-3459e7347e8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afc91c59-097b-4435-aa68-01d320c90ebc", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047378632000, "endTime": 22047379098800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "9fea4707-8907-4d80-bd5b-2528d8c6c45f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "920d32da-237f-480b-bd38-8a921b34b544", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046913965100, "endTime": 22046913982400}, "additional": {"logType": "info", "children": [], "durationId": "0ae9357a-36cb-4ddd-a270-4d062bbae7d3", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "aff3adb9-62db-4e8c-a7ae-d93b8bf871b2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046914039500, "endTime": 22046926378300}, "additional": {"logType": "info", "children": [], "durationId": "3fbd2cd0-76a5-4c86-a0a7-3bf7eb296d13", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "74b11729-b300-4ea5-b7d9-41188c28e9ec", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046928370900, "endTime": 22046928401700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f5586e2-4a1c-4542-88e4-e0696cf73767", "logId": "7d29e184-a2ae-4353-b070-e0dce7f1086f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d29e184-a2ae-4353-b070-e0dce7f1086f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046928370900, "endTime": 22046928401700}, "additional": {"logType": "info", "children": [], "durationId": "74b11729-b300-4ea5-b7d9-41188c28e9ec", "parent": "6074a879-a0ac-4f5a-9433-9eec9e0da9d9"}}, {"head": {"id": "136e298f-f5b6-4575-9f0e-55c9dfe2f64d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046935804600, "endTime": 22047164691100}, "additional": {"children": ["c35c0c8e-193a-4d12-9558-cf800f03b9ee", "3458f346-1d7a-4604-a39b-24ef1c8a76be"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f5586e2-4a1c-4542-88e4-e0696cf73767", "logId": "84bd10a0-e0e1-4ce2-b547-fb39bea9c94b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c35c0c8e-193a-4d12-9558-cf800f03b9ee", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046935814300, "endTime": 22046951740200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "136e298f-f5b6-4575-9f0e-55c9dfe2f64d", "logId": "e5f2fc00-bfbb-4374-88ee-7ec580b894df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3458f346-1d7a-4604-a39b-24ef1c8a76be", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046951835900, "endTime": 22047164652400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "136e298f-f5b6-4575-9f0e-55c9dfe2f64d", "logId": "2bdd2809-ebd7-4c22-8073-9271a7348c0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "425bdec4-2c6e-4ff7-a2ff-a786d051afcd", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046935839300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f107018-7b1c-44e6-a846-8dd8f1f04507", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046951236500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f2fc00-bfbb-4374-88ee-7ec580b894df", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046935814300, "endTime": 22046951740200}, "additional": {"logType": "info", "children": [], "durationId": "c35c0c8e-193a-4d12-9558-cf800f03b9ee", "parent": "84bd10a0-e0e1-4ce2-b547-fb39bea9c94b"}}, {"head": {"id": "99b625db-065b-49a4-8457-f0582932062f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046951885600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25cc5f68-7adf-49f0-bdd8-0015e645fea5", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046971834600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ab78c8-691e-49e4-b83d-a86120dd3dac", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046972107200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1438164d-ae20-4822-8688-2d89869ee3aa", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046972310700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbdb25f-2f53-4efb-9d64-fe013797d8e7", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046972452700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b5d5f8-0ea5-4515-95b8-1e3695a88c51", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046981317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca21e0f6-7933-4d24-ac2d-a438cb5dd289", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046995953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00840d8e-1d37-40e3-b2d3-4413be93e56c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047025103500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0780df74-0bc1-4073-a016-34edc180d5f4", "name": "Sdk init in 96 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047093520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "486bc0b3-f97a-4d59-a239-fa303392acf6", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047093932000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 13}, "markType": "other"}}, {"head": {"id": "0948deba-7d7c-41f6-8bd4-e926bbe18e84", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047094071000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 13}, "markType": "other"}}, {"head": {"id": "d6b16bad-2300-4ebd-acd5-883b446d651c", "name": "Project task initialization takes 66 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047163669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "646070d2-b879-4069-9862-3ad1efb800fe", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047164038800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5b72428-e9b5-401f-a0af-af620dac0a30", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047164271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e2fb62a-9610-45d3-b964-9a884cf33650", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047164461600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bdd2809-ebd7-4c22-8073-9271a7348c0a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046951835900, "endTime": 22047164652400}, "additional": {"logType": "info", "children": [], "durationId": "3458f346-1d7a-4604-a39b-24ef1c8a76be", "parent": "84bd10a0-e0e1-4ce2-b547-fb39bea9c94b"}}, {"head": {"id": "84bd10a0-e0e1-4ce2-b547-fb39bea9c94b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046935804600, "endTime": 22047164691100}, "additional": {"logType": "info", "children": ["e5f2fc00-bfbb-4374-88ee-7ec580b894df", "2bdd2809-ebd7-4c22-8073-9271a7348c0a"], "durationId": "136e298f-f5b6-4575-9f0e-55c9dfe2f64d", "parent": "6074a879-a0ac-4f5a-9433-9eec9e0da9d9"}}, {"head": {"id": "26ded7c0-ca46-4489-b834-bd296da04e80", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047167115100, "endTime": 22047167153800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f5586e2-4a1c-4542-88e4-e0696cf73767", "logId": "16a7ca82-a3ff-4338-9b87-f988b6543b61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16a7ca82-a3ff-4338-9b87-f988b6543b61", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047167115100, "endTime": 22047167153800}, "additional": {"logType": "info", "children": [], "durationId": "26ded7c0-ca46-4489-b834-bd296da04e80", "parent": "6074a879-a0ac-4f5a-9433-9eec9e0da9d9"}}, {"head": {"id": "6074a879-a0ac-4f5a-9433-9eec9e0da9d9", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046926397200, "endTime": 22047167202400}, "additional": {"logType": "info", "children": ["7d29e184-a2ae-4353-b070-e0dce7f1086f", "84bd10a0-e0e1-4ce2-b547-fb39bea9c94b", "16a7ca82-a3ff-4338-9b87-f988b6543b61"], "durationId": "6f5586e2-4a1c-4542-88e4-e0696cf73767", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "87a2729c-524a-4b9c-a06e-15788372d78c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047170345000, "endTime": 22047233740100}, "additional": {"children": ["a6ec21da-5a90-40a7-b41c-3eb73cb42764", "c65781c5-57fd-4726-b801-b1b95f2a46a7", "ef5dfc7e-dc5e-454a-b514-a7d56092bf48"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e57b067f-caa0-471f-a251-a07b988777f3", "logId": "9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ec21da-5a90-40a7-b41c-3eb73cb42764", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047181579000, "endTime": 22047181623700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87a2729c-524a-4b9c-a06e-15788372d78c", "logId": "88dfdeab-c1e8-47aa-8af2-f513420cb06d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88dfdeab-c1e8-47aa-8af2-f513420cb06d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047181579000, "endTime": 22047181623700}, "additional": {"logType": "info", "children": [], "durationId": "a6ec21da-5a90-40a7-b41c-3eb73cb42764", "parent": "9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1"}}, {"head": {"id": "c65781c5-57fd-4726-b801-b1b95f2a46a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047186952300, "endTime": 22047230012300}, "additional": {"children": ["d43ebb60-bcb7-422b-a440-747630097743", "df8ec5a3-3b39-48ee-b5cf-7eb6bb18bb24"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87a2729c-524a-4b9c-a06e-15788372d78c", "logId": "64b350b4-4860-4f24-8f67-e30de0175c5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d43ebb60-bcb7-422b-a440-747630097743", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047186954000, "endTime": 22047194253000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c65781c5-57fd-4726-b801-b1b95f2a46a7", "logId": "0973198c-0ee6-4d7a-85ec-0f6aec551abd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df8ec5a3-3b39-48ee-b5cf-7eb6bb18bb24", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047194281200, "endTime": 22047229981700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c65781c5-57fd-4726-b801-b1b95f2a46a7", "logId": "e3246903-a9d3-4d70-8805-358790c1fccc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af7d601c-cbc9-43bb-986d-51fa2ae04798", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047186963500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd84818c-b01a-4546-ad82-90e49b7d0fce", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047193861600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0973198c-0ee6-4d7a-85ec-0f6aec551abd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047186954000, "endTime": 22047194253000}, "additional": {"logType": "info", "children": [], "durationId": "d43ebb60-bcb7-422b-a440-747630097743", "parent": "64b350b4-4860-4f24-8f67-e30de0175c5b"}}, {"head": {"id": "b83caf69-9c8f-4d5a-9076-89d209547d51", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047194300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee1d6e53-46d6-4a26-b475-05d9d806fe06", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047217382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689d7308-ae21-481c-a812-77a46c9bd051", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047217578500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1c66b9-7a8f-4e5d-937f-bf31f4adebd6", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047217868900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfc73de-a86b-457f-9f5f-66a50bd30bc4", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047218140500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c24b1c-dd81-40a8-ace4-d07521bc186d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047218402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968c7d7a-a760-48d7-bea5-e7105e3e5faa", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047218482400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abeb99d7-30c6-496c-8bd7-cfa0a70b4bd5", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047218643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756dc5f8-b66c-46e2-bac9-b6b84f33eff2", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047229197400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994b767e-0c22-41b4-b7fa-533179328691", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047229609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62182fe9-e777-4967-9db0-fca94c2d159e", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047229799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6647a7-c1a4-465f-9aa7-6b77bf3609f6", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047229910700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3246903-a9d3-4d70-8805-358790c1fccc", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047194281200, "endTime": 22047229981700}, "additional": {"logType": "info", "children": [], "durationId": "df8ec5a3-3b39-48ee-b5cf-7eb6bb18bb24", "parent": "64b350b4-4860-4f24-8f67-e30de0175c5b"}}, {"head": {"id": "64b350b4-4860-4f24-8f67-e30de0175c5b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047186952300, "endTime": 22047230012300}, "additional": {"logType": "info", "children": ["0973198c-0ee6-4d7a-85ec-0f6aec551abd", "e3246903-a9d3-4d70-8805-358790c1fccc"], "durationId": "c65781c5-57fd-4726-b801-b1b95f2a46a7", "parent": "9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1"}}, {"head": {"id": "ef5dfc7e-dc5e-454a-b514-a7d56092bf48", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047233684100, "endTime": 22047233704900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87a2729c-524a-4b9c-a06e-15788372d78c", "logId": "aab68db6-bdbc-43c1-b911-30d7e5bf5609"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aab68db6-bdbc-43c1-b911-30d7e5bf5609", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047233684100, "endTime": 22047233704900}, "additional": {"logType": "info", "children": [], "durationId": "ef5dfc7e-dc5e-454a-b514-a7d56092bf48", "parent": "9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1"}}, {"head": {"id": "9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047170345000, "endTime": 22047233740100}, "additional": {"logType": "info", "children": ["88dfdeab-c1e8-47aa-8af2-f513420cb06d", "64b350b4-4860-4f24-8f67-e30de0175c5b", "aab68db6-bdbc-43c1-b911-30d7e5bf5609"], "durationId": "87a2729c-524a-4b9c-a06e-15788372d78c", "parent": "ff53f33f-7e9b-451a-b0d8-e25c84037055"}}, {"head": {"id": "ff53f33f-7e9b-451a-b0d8-e25c84037055", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047167253000, "endTime": 22047233760400}, "additional": {"logType": "info", "children": ["9812f5ad-dc4d-4c2f-a62a-0d36c0db53a1"], "durationId": "e57b067f-caa0-471f-a251-a07b988777f3", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "398ae999-9413-449c-b758-28fc6fb4f57f", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047291930800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fba27c4-8467-4e97-8383-81a916db615d", "name": "hvigorfile, resolve hvigorfile dependencies in 60 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047293347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc61247d-a362-4585-a3b1-840e849f5ccf", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047233773400, "endTime": 22047293826400}, "additional": {"logType": "info", "children": [], "durationId": "95a71619-ee81-4980-9c85-e36950efa241", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "07720041-16b1-42a3-8219-7ac96f04c091", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047297411400, "endTime": 22047298125000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "logId": "321d9936-ccf8-4cdd-979e-d9d28373bea2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfee1e77-450a-499f-8030-11e747eae339", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047297517200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321d9936-ccf8-4cdd-979e-d9d28373bea2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047297411400, "endTime": 22047298125000}, "additional": {"logType": "info", "children": [], "durationId": "07720041-16b1-42a3-8219-7ac96f04c091", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "b7171b0f-ee79-4d1e-9cda-4c1df0983526", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047307523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a659003d-6098-4c7d-93f7-a520021d5070", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047336728700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81176115-abd9-4004-b939-59d4f65ec700", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047298183100, "endTime": 22047340427000}, "additional": {"logType": "info", "children": [], "durationId": "aa99fc2e-07bf-4854-8c30-e18f5b8aa1cf", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "d8d87fcc-8220-4e67-b85d-21daf4217702", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047340498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21486d8b-3bbe-4bf9-8d30-9f76961425d5", "name": "<PERSON><PERSON>le wallet Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047356931800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f5b065-26db-4e76-8132-4adbc9787762", "name": "Module wallet's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047357129300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d726a6-4a01-4139-b969-6c0b87c458d2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047357479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8648dcd-abfd-4b62-86b5-5d3b788bef09", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047364776100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9c20e8-8622-4c02-9704-e08871b63ad7", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047365056100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b421c7ca-2557-4a05-a9db-3459e7347e8f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047340470900, "endTime": 22047378532500}, "additional": {"logType": "info", "children": [], "durationId": "76d8c06e-47b4-4e3d-a584-082af9f32209", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "b86eee21-1847-45bc-b2e8-d7986c58f812", "name": "Configuration phase cost:465 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047378723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fea4707-8907-4d80-bd5b-2528d8c6c45f", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047378632000, "endTime": 22047379098800}, "additional": {"logType": "info", "children": [], "durationId": "afc91c59-097b-4435-aa68-01d320c90ebc", "parent": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6"}}, {"head": {"id": "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046892313000, "endTime": 22047379148000}, "additional": {"logType": "info", "children": ["920d32da-237f-480b-bd38-8a921b34b544", "aff3adb9-62db-4e8c-a7ae-d93b8bf871b2", "6074a879-a0ac-4f5a-9433-9eec9e0da9d9", "ff53f33f-7e9b-451a-b0d8-e25c84037055", "fc61247d-a362-4585-a3b1-840e849f5ccf", "81176115-abd9-4004-b939-59d4f65ec700", "b421c7ca-2557-4a05-a9db-3459e7347e8f", "9fea4707-8907-4d80-bd5b-2528d8c6c45f", "321d9936-ccf8-4cdd-979e-d9d28373bea2"], "durationId": "0744f6a0-e02b-4be8-a3fc-e56b2b456d39", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "3235b011-889c-4d51-bb87-edf509779f1a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047386816900, "endTime": 22047386877900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f7993e4-d0ba-48b9-acb3-03b6276975b8", "logId": "262a86f5-fd55-46bf-9add-7e9ee40847fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "262a86f5-fd55-46bf-9add-7e9ee40847fd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047386816900, "endTime": 22047386877900}, "additional": {"logType": "info", "children": [], "durationId": "3235b011-889c-4d51-bb87-edf509779f1a", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "5ae7212e-0959-40cf-b21c-c62383d16cb4", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047379229200, "endTime": 22047386921600}, "additional": {"logType": "info", "children": [], "durationId": "2c2a0854-6ca5-4f64-8f49-45f55f9b8bd3", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "3bbd4bb6-6f42-4d45-ace9-79338727fdda", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047386943100, "endTime": 22047387015700}, "additional": {"logType": "info", "children": [], "durationId": "351fe112-f51c-4791-a850-fcaa493cc1ed", "parent": "4978a377-9078-4b34-94cc-0ce35bae3d03"}}, {"head": {"id": "4978a377-9078-4b34-94cc-0ce35bae3d03", "name": "init", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046859070200, "endTime": 22047387036700}, "additional": {"logType": "info", "children": ["b64f44ae-5a2d-45f8-aee6-af458ad7e198", "b5d68ec4-01ee-4458-b9c3-1ebbaa0fafb6", "5ae7212e-0959-40cf-b21c-c62383d16cb4", "3bbd4bb6-6f42-4d45-ace9-79338727fdda", "2f1c577a-4634-4123-9361-b8c7dc3f4e72", "a9cf9eb7-64b0-4fc5-93ac-7883a4c7087d", "262a86f5-fd55-46bf-9add-7e9ee40847fd"], "durationId": "7f7993e4-d0ba-48b9-acb3-03b6276975b8"}}, {"head": {"id": "ea4d9cc0-d86a-4996-9e44-803d319db7a8", "name": "Configuration task cost before running: 540 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047387808500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2371c8fc-f324-4645-a644-b8a7a77a6eff", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047407950400, "endTime": 22047436332500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1d387ca3-4750-4e23-a17e-a56c55a292e2", "logId": "1a72074e-eb2e-40f2-874c-42aa492ccb7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d387ca3-4750-4e23-a17e-a56c55a292e2", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047394679200}, "additional": {"logType": "detail", "children": [], "durationId": "2371c8fc-f324-4645-a644-b8a7a77a6eff"}}, {"head": {"id": "abbb0ad8-756e-4f48-a94d-9f764355f3f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047396284700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6632734-1ded-46e1-920c-7d2eee9d11dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047396576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6feda25-0597-4b9d-ba00-2f4067a5fc18", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047407984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00fb76f2-063b-4290-9cdf-ffe4e05208cc", "name": "Incremental task entry:default@PreBuild pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047435808200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e9d872-1fee-4562-abd9-2e37268f8bc1", "name": "entry : default@PreBuild cost memory 0.29473876953125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047436207600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a72074e-eb2e-40f2-874c-42aa492ccb7f", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047407950400, "endTime": 22047436332500}, "additional": {"logType": "info", "children": [], "durationId": "2371c8fc-f324-4645-a644-b8a7a77a6eff"}}, {"head": {"id": "5317b381-feeb-48f9-a97a-1aab8129b711", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047453298700, "endTime": 22047458679800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d1fce0f7-3667-4f03-b559-5f56fb324879", "logId": "d4ee6788-453d-484a-a79f-48fe72660dc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1fce0f7-3667-4f03-b559-5f56fb324879", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047448835500}, "additional": {"logType": "detail", "children": [], "durationId": "5317b381-feeb-48f9-a97a-1aab8129b711"}}, {"head": {"id": "d57c810c-041d-4ca1-b76b-85232a190306", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047450490100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd50a1c-66b0-4ab4-a8a3-7efb9f1a9067", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047450904500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94820e0-7a8b-4196-85f9-c4e8ad228ac8", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047453349900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cafde490-e0cb-444d-a8b7-6c81b2e6e357", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047458273800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f679c1-65ea-4f38-98e3-c9200508f8cd", "name": "entry : default@MergeProfile cost memory 0.1115875244140625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047458503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ee6788-453d-484a-a79f-48fe72660dc9", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047453298700, "endTime": 22047458679800}, "additional": {"logType": "info", "children": [], "durationId": "5317b381-feeb-48f9-a97a-1aab8129b711"}}, {"head": {"id": "8bfb2b0c-a86b-4ff1-a166-ca99127183ed", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047466434800, "endTime": 22047472724900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7ba59377-d420-404b-8c22-81b03890b809", "logId": "352f6b45-db65-4f47-b84c-48ce324a155c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ba59377-d420-404b-8c22-81b03890b809", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047462341000}, "additional": {"logType": "detail", "children": [], "durationId": "8bfb2b0c-a86b-4ff1-a166-ca99127183ed"}}, {"head": {"id": "ab4b8ab6-018e-4957-8e15-d8254d890ac3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047463771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c023801f-ce2f-43ff-a569-7b37594c8bdf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047464085800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222477c6-2f6d-4162-8744-e662ecfa9b8d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047466460300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78cd7da6-d204-4e95-bf02-911487836826", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047469303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0b2fd9-55b1-4747-a628-b6da49a64e0d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047472362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e10059b-4a6e-4e78-9f6d-0356e106bcfe", "name": "entry : default@CreateBuildProfile cost memory 0.0991058349609375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047472593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352f6b45-db65-4f47-b84c-48ce324a155c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047466434800, "endTime": 22047472724900}, "additional": {"logType": "info", "children": [], "durationId": "8bfb2b0c-a86b-4ff1-a166-ca99127183ed"}}, {"head": {"id": "c64b0a6d-037c-481c-8fc7-fb94a26605d7", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481087500, "endTime": 22047481780500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eaa4d54c-76cf-4961-8c9d-40684edb11f3", "logId": "108f4453-31fc-41b4-a0d0-2cd5e7e5c6c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaa4d54c-76cf-4961-8c9d-40684edb11f3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047478112100}, "additional": {"logType": "detail", "children": [], "durationId": "c64b0a6d-037c-481c-8fc7-fb94a26605d7"}}, {"head": {"id": "ecef5c4e-3b73-4d26-b326-03cbf7fe3854", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047479303100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8591ca9a-dde8-4846-970d-7c542c725b16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047479558700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "754e0647-8cc0-4257-a36d-5c493ae1f1de", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560deb7d-a674-4669-bc5d-14eef6021a21", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7668b144-b3e1-47ce-a25b-88c99c66938d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481387000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f37dc4f1-85b5-47ef-8948-f857bf8b5feb", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a8ad28-f3e0-48ae-af34-916e22ef44a2", "name": "runTaskFromQueue task cost before running: 633 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "108f4453-31fc-41b4-a0d0-2cd5e7e5c6c5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047481087500, "endTime": 22047481780500, "totalTime": 539100}, "additional": {"logType": "info", "children": [], "durationId": "c64b0a6d-037c-481c-8fc7-fb94a26605d7"}}, {"head": {"id": "15db21be-6084-49d1-8db2-2a73e1fd474c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047505566000, "endTime": 22047507976800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "87769f55-c916-4a49-acd2-1b8709ea6e43", "logId": "0ea019b3-122b-4b4c-b1cb-ea728907f728"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87769f55-c916-4a49-acd2-1b8709ea6e43", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047485563600}, "additional": {"logType": "detail", "children": [], "durationId": "15db21be-6084-49d1-8db2-2a73e1fd474c"}}, {"head": {"id": "87f336b8-824b-4564-8072-b3aa2600ae3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047487226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e577022-799e-42b6-ac91-e1c8675a34b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047487470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4479f995-cb12-49b8-bd3a-2b5cf2d9aa06", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047505604300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649f0890-1a5e-4f62-a274-bddb26a830ef", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047506040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bed247b-7dff-4698-b6ec-ffe60d5e6cf1", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047507619100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811fd06e-b85b-4638-9684-16d0704860d3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06710052490234375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047507838300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ea019b3-122b-4b4c-b1cb-ea728907f728", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047505566000, "endTime": 22047507976800}, "additional": {"logType": "info", "children": [], "durationId": "15db21be-6084-49d1-8db2-2a73e1fd474c"}}, {"head": {"id": "12772552-43de-4deb-af1e-c630ff5faabd", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047516494100, "endTime": 22047520079100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2755dce1-7cb0-4484-8cf1-e2b762a8d3df", "logId": "ff96f7b6-d183-455e-9065-7e7b28f774ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2755dce1-7cb0-4484-8cf1-e2b762a8d3df", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047512140800}, "additional": {"logType": "detail", "children": [], "durationId": "12772552-43de-4deb-af1e-c630ff5faabd"}}, {"head": {"id": "a5419b08-3ab0-4ab9-8deb-9242c496a385", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047513254800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d638e141-15d2-4f8a-9ed8-8f8a1ee982b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047513561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b5b841-734b-4675-9295-5cce5eb22116", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047516522000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce1b5b4-5283-4f13-9f1e-069bc43b3c99", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047519507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe0b0f0-fb64-4d21-b60b-8d0759bafb7d", "name": "entry : default@ProcessProfile cost memory 0.05767822265625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047519877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff96f7b6-d183-455e-9065-7e7b28f774ae", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047516494100, "endTime": 22047520079100}, "additional": {"logType": "info", "children": [], "durationId": "12772552-43de-4deb-af1e-c630ff5faabd"}}, {"head": {"id": "6a413a81-9cc2-4621-af33-41807bfafde8", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047528411700, "endTime": 22047542126000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b5f418a7-f852-451e-a767-9021e8ac3056", "logId": "3273b884-4c5d-4d96-99f2-9c6235e6f99e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5f418a7-f852-451e-a767-9021e8ac3056", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047523527700}, "additional": {"logType": "detail", "children": [], "durationId": "6a413a81-9cc2-4621-af33-41807bfafde8"}}, {"head": {"id": "ff4bb6a6-1636-4c98-86b8-0ceec289ecff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047524555900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8978fba3-2258-4afa-9c04-481581cc6486", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047524733900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3440a96b-2f2d-4521-b072-c5ef9b57e2d7", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047528438000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ee8556-308d-495c-a32a-7fc7e48b81d1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047541772100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd807348-0036-4ae9-9cbc-85dfeb44e40b", "name": "entry : default@ProcessRouterMap cost memory 0.1990966796875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047542005800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3273b884-4c5d-4d96-99f2-9c6235e6f99e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047528411700, "endTime": 22047542126000}, "additional": {"logType": "info", "children": [], "durationId": "6a413a81-9cc2-4621-af33-41807bfafde8"}}, {"head": {"id": "4f1cd4ac-213d-4b43-a32e-6817b4af5cc2", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047559326200, "endTime": 22047567449600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6ca21331-caa7-457f-8eca-48774daa5a14", "logId": "b61d3efd-ca77-4442-962b-57e21e851fc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ca21331-caa7-457f-8eca-48774daa5a14", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047549457800}, "additional": {"logType": "detail", "children": [], "durationId": "4f1cd4ac-213d-4b43-a32e-6817b4af5cc2"}}, {"head": {"id": "3432c955-ed93-4288-a837-6e68a7b6dbb3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047551701100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7309c186-10e0-4f75-82a0-1eb67ed2d046", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047551898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d336cdc-f327-41c4-868e-a5c60f4d7297", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047554411700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d1dadb-d820-4b0f-ae6f-96124a261c35", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047562060700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5544e6-c349-4e09-8ddd-f62007e8ad3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047562533100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "146911f5-ec4d-4978-b48d-babb455156ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047562871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91201c8-a854-4a5e-bab7-be41922b34b0", "name": "entry : default@PreviewProcessResource cost memory 0.06943511962890625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047563151000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678c0d7a-9624-4d90-86b0-582f5c7660a1", "name": "runTaskFromQueue task cost before running: 719 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047567230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61d3efd-ca77-4442-962b-57e21e851fc3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047559326200, "endTime": 22047567449600, "totalTime": 4058100}, "additional": {"logType": "info", "children": [], "durationId": "4f1cd4ac-213d-4b43-a32e-6817b4af5cc2"}}, {"head": {"id": "8a91230e-f9d3-47dd-bcb2-eea7d7997b4d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047588195100, "endTime": 22047640825200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8dede111-7399-49c4-ae1b-2ae788b2d891", "logId": "754e58bc-1c8b-4438-a53f-ae9376c95a03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dede111-7399-49c4-ae1b-2ae788b2d891", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047575719800}, "additional": {"logType": "detail", "children": [], "durationId": "8a91230e-f9d3-47dd-bcb2-eea7d7997b4d"}}, {"head": {"id": "a50bd49f-ae53-4480-9c11-264882af029c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047577234400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f903ee-bb7b-4d69-8d39-a32a5fb0b025", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047577417500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172aa118-58ce-4303-a71e-db9faa6f976c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047588246200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c20953-221d-4a59-946a-4ee4a4ca1312", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047640453700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c907df6e-c1b1-44cc-b9eb-c5b2699f3bbb", "name": "entry : default@GenerateLoaderJson cost memory 0.7484512329101562", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047640686200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "754e58bc-1c8b-4438-a53f-ae9376c95a03", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047588195100, "endTime": 22047640825200}, "additional": {"logType": "info", "children": [], "durationId": "8a91230e-f9d3-47dd-bcb2-eea7d7997b4d"}}, {"head": {"id": "a8169d77-4666-48b0-9fd4-2f63acd5f616", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047680257300, "endTime": 22047735149100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3a273b7b-037f-49a6-b00d-210702e4cab0", "logId": "a7ee824e-53bd-40e3-ac97-27f375c39351"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a273b7b-037f-49a6-b00d-210702e4cab0", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047666030900}, "additional": {"logType": "detail", "children": [], "durationId": "a8169d77-4666-48b0-9fd4-2f63acd5f616"}}, {"head": {"id": "c0fed06e-b3b9-4aea-a7f9-e29bb75f31a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047668395800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b43e24-de66-4674-b372-01833a04b56b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047668696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a3724d6-46cb-4c19-9d27-359c9abcbd90", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047671584800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73b22a1-e941-4571-b0e8-77b0bbbb068f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047680372000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85db6ebd-3d6a-48d3-a3cf-9c8265a5547a", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 52 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047734585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0fdb20-dd93-439a-aa0c-7702cd208e2c", "name": "entry : default@PreviewCompileResource cost memory 0.49627685546875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047734916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ee824e-53bd-40e3-ac97-27f375c39351", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047680257300, "endTime": 22047735149100}, "additional": {"logType": "info", "children": [], "durationId": "a8169d77-4666-48b0-9fd4-2f63acd5f616"}}, {"head": {"id": "e81b7a76-46a2-4cde-abf7-0a064c1647ac", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047748823200, "endTime": 22047749959900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0cd0124c-961f-4ad3-b56f-9ed30f606f86", "logId": "017d952d-d331-4e93-aeab-40118f30d51b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cd0124c-961f-4ad3-b56f-9ed30f606f86", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047744615000}, "additional": {"logType": "detail", "children": [], "durationId": "e81b7a76-46a2-4cde-abf7-0a064c1647ac"}}, {"head": {"id": "cffae753-7176-4a72-9301-426026efb617", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047748052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c40f3f-9e6d-4474-94e6-91fe4ded<PERSON>dab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047748491100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25c088a-499d-4175-8a4e-68d40c5a2bfa", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047748842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0183306-550d-452d-9d5b-35dcc5a16e99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047749049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1cdf271-4686-4901-9ccc-aa2e47931da0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047749193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b443763-8226-4dcf-8d1e-bb166593e501", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047749393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e89d1c2-04df-42c2-9617-65b984cdaa2d", "name": "runTaskFromQueue task cost before running: 901 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047749714900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017d952d-d331-4e93-aeab-40118f30d51b", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047748823200, "endTime": 22047749959900, "totalTime": 808000}, "additional": {"logType": "info", "children": [], "durationId": "e81b7a76-46a2-4cde-abf7-0a064c1647ac"}}, {"head": {"id": "be45a677-499e-4132-a339-8556368a61b9", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047759239100, "endTime": 22047765506000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "b8383ad4-3ac7-460d-a458-9606cd984d2c", "logId": "d70417c7-2f16-4f4c-bc7e-7a3a38a4131d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8383ad4-3ac7-460d-a458-9606cd984d2c", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047756507100}, "additional": {"logType": "detail", "children": [], "durationId": "be45a677-499e-4132-a339-8556368a61b9"}}, {"head": {"id": "46573b21-605e-4572-9632-17c4848fca04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047757510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11096a42-81f3-401a-8bc3-6408d9c7919a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047757685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e46badd-ed63-47e5-85cb-027448b5c0e8", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047759284400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78020a7-0851-473f-9e2d-bd1be5812873", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047765158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9d7b5f-981e-4a2a-afab-510cef6b0b96", "name": "entry : default@CopyPreviewProfile cost memory 0.099456787109375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047765385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d70417c7-2f16-4f4c-bc7e-7a3a38a4131d", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047759239100, "endTime": 22047765506000}, "additional": {"logType": "info", "children": [], "durationId": "be45a677-499e-4132-a339-8556368a61b9"}}, {"head": {"id": "16fc7b9f-9bf7-4b57-959c-7074047ff1e6", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047770647900, "endTime": 22047771506300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "4e3d9cd1-e8f1-461d-b4b6-bc69d4ea40d1", "logId": "4b0e0bc6-0489-461f-ac91-bb7795c230c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e3d9cd1-e8f1-461d-b4b6-bc69d4ea40d1", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047768475800}, "additional": {"logType": "detail", "children": [], "durationId": "16fc7b9f-9bf7-4b57-959c-7074047ff1e6"}}, {"head": {"id": "7542c017-54b4-45ac-a27e-3905bb860f5c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047769374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7462f37-cf08-45c6-b2a4-026a76e99738", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047769514200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60fc0d27-710c-4b70-b4a7-c2f0f9fa6092", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047770663400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "850b73cf-cdee-4092-baef-cb469ebd3256", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047770831400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f91f8da9-17be-4c3a-b0ba-a27d3ab5d4fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047770909900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb8f46a0-597b-478d-8959-9ce9da10e3cd", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047771032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4758bb1-6068-4159-aa55-f2840df85db3", "name": "runTaskFromQueue task cost before running: 923 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047771151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0e0bc6-0489-461f-ac91-bb7795c230c6", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047770647900, "endTime": 22047771506300, "totalTime": 476100}, "additional": {"logType": "info", "children": [], "durationId": "16fc7b9f-9bf7-4b57-959c-7074047ff1e6"}}, {"head": {"id": "0ece53e1-b5d9-49e9-a222-2c9c09da42b8", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047776994400, "endTime": 22047777930800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "41a9e8d9-3fcf-4b08-8d77-b0147011dd22", "logId": "b2af2844-7916-41e1-8f40-bde4f12f4b24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41a9e8d9-3fcf-4b08-8d77-b0147011dd22", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047776779300}, "additional": {"logType": "detail", "children": [], "durationId": "0ece53e1-b5d9-49e9-a222-2c9c09da42b8"}}, {"head": {"id": "498e3cc0-f7e1-4f6f-8ea2-4253eb38cdb9", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047777025900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0dcff58-1f5b-4f9b-94d2-61fa6ef12b9f", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047777472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e22f25d2-2239-4dfd-ba3b-c6473044a84f", "name": "runTaskFromQueue task cost before running: 930 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047777754300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2af2844-7916-41e1-8f40-bde4f12f4b24", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047776994400, "endTime": 22047777930800, "totalTime": 696600}, "additional": {"logType": "info", "children": [], "durationId": "0ece53e1-b5d9-49e9-a222-2c9c09da42b8"}}, {"head": {"id": "ed0646d4-4b63-46fc-af5b-22ad843c9a4c", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047782816700, "endTime": 22047788146000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "468270ca-5244-48d7-a5bc-5d5fc4146cfb", "logId": "4277288d-8a5d-442e-9bf3-d993a6626c7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "468270ca-5244-48d7-a5bc-5d5fc4146cfb", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047780669800}, "additional": {"logType": "detail", "children": [], "durationId": "ed0646d4-4b63-46fc-af5b-22ad843c9a4c"}}, {"head": {"id": "63f14800-db6d-4d2f-8788-13034faebc0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047781491300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388bdcda-dd3d-463c-9d18-5652ca8e12c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047781617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0066de2-fbcd-451c-9273-cb9aff9acabd", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047782830800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d2a0fb-858c-43b4-b633-5c70838e884b", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047787836400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0baf4c04-912b-4f3b-8f09-88189908b57d", "name": "entry : default@PreviewUpdateAssets cost memory -1.65771484375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047788039400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4277288d-8a5d-442e-9bf3-d993a6626c7b", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047782816700, "endTime": 22047788146000}, "additional": {"logType": "info", "children": [], "durationId": "ed0646d4-4b63-46fc-af5b-22ad843c9a4c"}}, {"head": {"id": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047801263300, "endTime": 22051867616000}, "additional": {"children": ["69927a12-87c5-4971-b5b8-2498c020dd1c"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4a1b4ff2-fa7f-47d0-b586-13fa9f5a14af", "logId": "d28efb6c-f031-4028-8a71-eac820ad3f49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a1b4ff2-fa7f-47d0-b586-13fa9f5a14af", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047791626800}, "additional": {"logType": "detail", "children": [], "durationId": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62"}}, {"head": {"id": "d4507e94-579a-415e-afc2-9253aee0af36", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047792425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ec82c1-50ba-42ed-ac1a-c461c38d2c55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047792597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c40e48a-5607-410d-9418-f07cebc6dd02", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047801285000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69927a12-87c5-4971-b5b8-2498c020dd1c", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker5", "startTime": 22047855351200, "endTime": 22051866964000}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62", "logId": "8e4cd3a3-c95b-4e81-a9c1-6a2a41d08cb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cf776b6-5cb4-4888-b6c6-3fc62ab1352c", "name": "entry : default@PreviewArkTS cost memory 0.9474029541015625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047860980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e4cd3a3-c95b-4e81-a9c1-6a2a41d08cb5", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Worker5", "startTime": 22047855351200, "endTime": 22051866964000}, "additional": {"logType": "error", "children": [], "durationId": "69927a12-87c5-4971-b5b8-2498c020dd1c", "parent": "d28efb6c-f031-4028-8a71-eac820ad3f49"}}, {"head": {"id": "ba4b3dca-0aa7-46e0-b3bb-7ecf9b2e3323", "name": "default@PreviewArkTS watch work[5] failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051867070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d28efb6c-f031-4028-8a71-eac820ad3f49", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22047801263300, "endTime": 22051867616000}, "additional": {"logType": "error", "children": ["8e4cd3a3-c95b-4e81-a9c1-6a2a41d08cb5"], "durationId": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62"}}, {"head": {"id": "8400560f-6d08-4a26-a9f0-6753c03f0a9a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051869667900}, "additional": {"logType": "debug", "children": [], "durationId": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62"}}, {"head": {"id": "1652779f-9fe2-4482-9f6f-fa2e6385e1f2", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/vue/daxiangmuwallet/wallet/entry/src/main/ets/pages/TransactionRecordsPage.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051873060300}, "additional": {"logType": "debug", "children": [], "durationId": "a68b5ba7-b1cf-46b8-a73c-ac11c3a9ea62"}}, {"head": {"id": "faec5438-4098-4ea5-99c4-83dae67ab09e", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051889897300, "endTime": 22051889995500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ca254a8-2a2b-4a26-9b5c-72667be88294", "logId": "49c20477-0140-4991-abd4-dc69d123e3f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49c20477-0140-4991-abd4-dc69d123e3f1", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051889897300, "endTime": 22051889995500}, "additional": {"logType": "info", "children": [], "durationId": "faec5438-4098-4ea5-99c4-83dae67ab09e"}}, {"head": {"id": "ef3c7f49-511d-4ff7-9dd7-c21ef074a14b", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22046848655500, "endTime": 22051890201300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 15, "minute": 13}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "716bd27c-3b14-4452-9902-006b70f2c444", "name": "BUILD FAILED in 5 s 42 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890238200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "1f60bd71-d13c-4e54-a669-23f6e3ce44ee", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890401300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca4da90-41a5-42f0-a712-c73c1267677e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e81235-9483-4209-808b-c7c435e471ec", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890471600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d938eb1e-f684-43a1-92a4-60c118f56a22", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890515000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc7a59da-a4e4-44fd-bec3-fb31a522db3e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890541200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "099f71f9-fc34-41bd-bdd4-d0a98cff90fe", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890562000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55cd08c-1bbf-4144-8c89-0056307ff983", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15eea96-50c9-4c19-ab05-395f110fc918", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890607800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f13f246-1f64-476a-b6d8-c990324c251b", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890633100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4593ccfa-3f0d-4701-9d15-d958695f98ca", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051890656600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f814c6-f865-4962-9cff-33c3b98cdcf7", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051895255800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b02bb7-3c8e-4c61-add4-4da1cbc4f7c0", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051897616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4779a5f2-185a-4e8b-8013-333bad6aa420", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051898355800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc72126-6b57-488a-8403-04e5e5b63053", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051899023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8460a91d-73cb-4bd1-bff0-51ac5617c7e2", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051900587500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e90153-d1a3-4400-a774-4f26d7ac2550", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051905375600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee2836dd-376e-4abc-a228-da8049b6f126", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051905736400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f2c6ea-b69d-46d3-a8bc-b472540b51dc", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051906023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b71ece-274e-409e-9128-4577f45de083", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051906305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d4a482d-32a8-41dd-afe3-65d9a39324e7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 22051906582200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}