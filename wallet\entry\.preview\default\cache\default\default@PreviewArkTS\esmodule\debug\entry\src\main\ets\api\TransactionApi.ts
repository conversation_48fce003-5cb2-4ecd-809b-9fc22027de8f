import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { TransactionType, TransactionStatus, PaymentMethod, PaymentChannel } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction, TransactionQueryParams, PageResult } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
/**
 * 交易相关API服务
 */
export class TransactionApi {
    /**
     * 获取用户交易记录列表（分页）
     * @param params 查询参数
     */
    public static async getTransactionList(params: TransactionQueryParams): Promise<PageResult<Transaction>> {
        try {
            const queryParams = new URLSearchParams();
            queryParams.append('userId', params.userId.toString());
            if (params.page) {
                queryParams.append('page', params.page.toString());
            }
            if (params.size) {
                queryParams.append('size', params.size.toString());
            }
            if (params.type) {
                queryParams.append('type', params.type.toString());
            }
            if (params.status !== undefined) {
                queryParams.append('status', params.status.toString());
            }
            if (params.startDate) {
                queryParams.append('startDate', params.startDate);
            }
            if (params.endDate) {
                queryParams.append('endDate', params.endDate);
            }
            const response = await httpClient.get<any>(`/transaction/list?${queryParams.toString()}`);
            if (response.data) {
                return {
                    records: response.data.records ? response.data.records.map((item: any) => this.convertToTransaction(item)) : [],
                    total: response.data.total || 0,
                    size: response.data.size || 10,
                    current: response.data.current || 1,
                    pages: response.data.pages || 1
                };
            }
            else {
                return {
                    records: [],
                    total: 0,
                    size: 10,
                    current: 1,
                    pages: 1
                };
            }
        }
        catch (error) {
            console.error('获取交易记录列表失败:', error);
            throw error;
        }
    }
    /**
     * 获取用户所有交易记录（不分页）
     * @param userId 用户ID
     */
    public static async getAllTransactions(userId: number): Promise<Transaction[]> {
        try {
            const response = await httpClient.get<any[]>(`/transaction/user/${userId}`);
            if (response.data && Array.isArray(response.data)) {
                return response.data.map(item => this.convertToTransaction(item));
            }
            else {
                return [];
            }
        }
        catch (error) {
            console.error('获取所有交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 获取交易详情
     * @param txnId 交易ID
     */
    public static async getTransactionDetail(txnId: number): Promise<Transaction> {
        try {
            const response = await httpClient.get<any>(`/transaction/${txnId}`);
            if (response.data) {
                return this.convertToTransaction(response.data);
            }
            else {
                throw new Error('获取交易详情响应数据为空');
            }
        }
        catch (error) {
            console.error('获取交易详情失败:', error);
            throw error;
        }
    }
    /**
     * 获取最近交易记录
     * @param userId 用户ID
     * @param limit 限制数量，默认5条
     */
    public static async getRecentTransactions(userId: number, limit: number = 5): Promise<Transaction[]> {
        try {
            const params: TransactionQueryParams = {
                userId: userId,
                page: 1,
                size: limit
            };
            const result = await this.getTransactionList(params);
            return result.records;
        }
        catch (error) {
            console.error('获取最近交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 根据类型获取交易记录
     * @param userId 用户ID
     * @param type 交易类型
     * @param page 页码
     * @param size 每页数量
     */
    public static async getTransactionsByType(userId: number, type: TransactionType, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        try {
            const params: TransactionQueryParams = {
                userId: userId,
                type: type,
                page: page,
                size: size
            };
            return await this.getTransactionList(params);
        }
        catch (error) {
            console.error('根据类型获取交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 获取支付记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     */
    public static async getPaymentRecords(userId: number, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        return this.getTransactionsByType(userId, TransactionType.PAYMENT, page, size);
    }
    /**
     * 获取充值记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     */
    public static async getRechargeRecords(userId: number, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        return this.getTransactionsByType(userId, TransactionType.RECHARGE, page, size);
    }
    /**
     * 获取转账记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     */
    public static async getTransferRecords(userId: number, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        return this.getTransactionsByType(userId, TransactionType.TRANSFER, page, size);
    }
    /**
     * 获取收款记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     */
    public static async getReceiveRecords(userId: number, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        return this.getTransactionsByType(userId, TransactionType.RECEIVE, page, size);
    }
    /**
     * 搜索交易记录
     * @param userId 用户ID
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页数量
     */
    public static async searchTransactions(userId: number, keyword: string, page: number = 1, size: number = 10): Promise<PageResult<Transaction>> {
        try {
            const response = await httpClient.get<any>(`/transaction/search?userId=${userId}&keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
            if (response.data) {
                return {
                    records: response.data.records ? response.data.records.map((item: any) => this.convertToTransaction(item)) : [],
                    total: response.data.total || 0,
                    size: response.data.size || 10,
                    current: response.data.current || 1,
                    pages: response.data.pages || 1
                };
            }
            else {
                return {
                    records: [],
                    total: 0,
                    size: 10,
                    current: 1,
                    pages: 1
                };
            }
        }
        catch (error) {
            console.error('搜索交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 转换后端数据为本地Transaction格式
     * @param data 后端返回的交易数据
     */
    private static convertToTransaction(data: any): Transaction {
        return {
            txnId: data.txnId || 0,
            txnNo: data.txnNo || '',
            userId: data.userId || 0,
            accountId: data.accountId || 0,
            type: data.type || TransactionType.PAYMENT,
            amount: data.amount || 0,
            balance: data.balance || 0,
            counterparty: data.counterparty,
            counterpartyPhone: data.counterpartyPhone,
            merchantId: data.merchantId,
            cardId: data.cardId,
            paymentMethod: data.paymentMethod,
            paymentChannel: data.paymentChannel,
            status: data.status || TransactionStatus.PENDING,
            remark: data.remark,
            createdAt: data.createdAt || new Date().toISOString(),
            updatedAt: data.updatedAt
        };
    }
    /**
     * 获取交易类型显示文本
     * @param type 交易类型
     */
    public static getTransactionTypeText(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE:
                return '充值';
            case TransactionType.TRANSFER:
                return '转账';
            case TransactionType.RECEIVE:
                return '收款';
            case TransactionType.PAYMENT:
                return '支付';
            default:
                return '未知';
        }
    }
    /**
     * 获取交易状态显示文本
     * @param status 交易状态
     */
    public static getTransactionStatusText(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.PENDING:
                return '处理中';
            case TransactionStatus.SUCCESS:
                return '成功';
            case TransactionStatus.FAILED:
                return '失败';
            default:
                return '未知';
        }
    }
    /**
     * 获取支付方式显示文本
     * @param method 支付方式
     */
    public static getPaymentMethodText(method?: PaymentMethod): string {
        if (!method)
            return '';
        switch (method) {
            case PaymentMethod.WALLET:
                return '钱包支付';
            case PaymentMethod.BANK_CARD:
                return '银行卡支付';
            default:
                return '未知';
        }
    }
    /**
     * 获取支付渠道显示文本
     * @param channel 支付渠道
     */
    public static getPaymentChannelText(channel?: PaymentChannel): string {
        if (!channel)
            return '';
        switch (channel) {
            case PaymentChannel.MERCHANT:
                return '商户付款';
            case PaymentChannel.QR_CODE:
                return '扫码付款';
            case PaymentChannel.NFC:
                return 'NFC支付';
            default:
                return '未知';
        }
    }
    /**
     * 格式化金额显示
     * @param amount 金额
     * @param type 交易类型
     */
    public static formatAmount(amount: number, type: TransactionType): string {
        const absAmount = Math.abs(amount);
        const formattedAmount = absAmount.toFixed(2);
        switch (type) {
            case TransactionType.RECHARGE:
            case TransactionType.RECEIVE:
                return `+${formattedAmount}`;
            case TransactionType.TRANSFER:
            case TransactionType.PAYMENT:
                return `-${formattedAmount}`;
            default:
                return amount >= 0 ? `+${formattedAmount}` : `-${formattedAmount}`;
        }
    }
    /**
     * 获取交易金额颜色
     * @param type 交易类型
     */
    public static getAmountColor(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE:
            case TransactionType.RECEIVE:
                return '#00C851'; // 绿色
            case TransactionType.TRANSFER:
            case TransactionType.PAYMENT:
                return '#FF4444'; // 红色
            default:
                return '#333333'; // 默认颜色
        }
    }
}
