import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { LoginRequest, LoginResponse, UserInfo } from '../common/types/index';
/**
 * 用户相关API服务
 */
export class UserApi {
    /**
     * 发送验证码
     * @param phone 手机号
     * @param type 验证码类型 1-登录 2-操作
     */
    public static async sendVerificationCode(phone: string, type: number = 1): Promise<string> {
        try {
            const response = await httpClient.post<string>('/user/send-code', {
                phone: phone,
                type: type
            });
            // 返回验证码（实际项目中不应该返回验证码，这里仅用于测试）
            return response.data || '123456';
        }
        catch (error) {
            console.error('发送验证码失败:', error);
            throw error;
        }
    }
    /**
     * 验证验证码
     * @param phone 手机号
     * @param verificationCode 验证码
     * @param loginType 登录类型
     */
    public static async verifyCode(phone: string, verificationCode: string, loginType: number = 1): Promise<boolean> {
        try {
            const response = await httpClient.post<boolean>('/user/verify-code', {
                phone: phone,
                verificationCode: verificationCode,
                loginType: loginType
            });
            return response.data || false;
        }
        catch (error) {
            console.error('验证码验证失败:', error);
            throw error;
        }
    }
    /**
     * 用户登录
     * @param loginRequest 登录请求参数
     */
    public static async login(loginRequest: LoginRequest): Promise<LoginResponse> {
        try {
            const response = await httpClient.post<LoginResponse>('/user/login', loginRequest);
            if (response.data) {
                return response.data;
            }
            else {
                throw new Error('登录响应数据为空');
            }
        }
        catch (error) {
            console.error('用户登录失败:', error);
            throw error;
        }
    }
    /**
     * 密码登录
     * @param phone 手机号
     * @param password 密码
     */
    public static async loginWithPassword(phone: string, password: string): Promise<LoginResponse> {
        const loginRequest: LoginRequest = {
            phone: phone,
            password: password,
            loginType: 'password'
        };
        return this.login(loginRequest);
    }
    /**
     * 验证码登录
     * @param phone 手机号
     * @param verificationCode 验证码
     */
    public static async loginWithSms(phone: string, verificationCode: string): Promise<LoginResponse> {
        const loginRequest: LoginRequest = {
            phone: phone,
            verificationCode: verificationCode,
            loginType: 'sms'
        };
        return this.login(loginRequest);
    }
    /**
     * 用户注册
     * @param userInfo 用户信息
     */
    public static async register(userInfo: Partial<UserInfo>): Promise<UserInfo> {
        try {
            const response = await httpClient.post<UserInfo>('/user/register', userInfo);
            if (response.data) {
                return response.data;
            }
            else {
                throw new Error('注册响应数据为空');
            }
        }
        catch (error) {
            console.error('用户注册失败:', error);
            throw error;
        }
    }
    /**
     * 获取用户信息
     * @param userId 用户ID
     */
    public static async getUserInfo(userId: number): Promise<UserInfo> {
        try {
            const response = await httpClient.get<UserInfo>(`/user/${userId}`);
            if (response.data) {
                return response.data;
            }
            else {
                throw new Error('获取用户信息响应数据为空');
            }
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
        }
    }
    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param userInfo 用户信息
     */
    public static async updateUserInfo(userId: number, userInfo: Partial<UserInfo>): Promise<UserInfo> {
        try {
            const response = await httpClient.put<UserInfo>(`/user/${userId}`, userInfo);
            if (response.data) {
                return response.data;
            }
            else {
                throw new Error('更新用户信息响应数据为空');
            }
        }
        catch (error) {
            console.error('更新用户信息失败:', error);
            throw error;
        }
    }
    /**
     * 修改登录密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    public static async changeLoginPassword(userId: number, oldPassword: string, newPassword: string): Promise<void> {
        try {
            await httpClient.put<string>('/security/change-login-password', {
                userId: userId,
                oldPassword: oldPassword,
                newPassword: newPassword
            });
        }
        catch (error) {
            console.error('修改登录密码失败:', error);
            throw error;
        }
    }
    /**
     * 修改支付密码
     * @param userId 用户ID
     * @param oldPayPassword 旧支付密码
     * @param newPayPassword 新支付密码
     */
    public static async changePayPassword(userId: number, oldPayPassword: string, newPayPassword: string): Promise<void> {
        try {
            await httpClient.put<string>('/security/change-pay-password', {
                userId: userId,
                oldPayPassword: oldPayPassword,
                newPayPassword: newPayPassword
            });
        }
        catch (error) {
            console.error('修改支付密码失败:', error);
            throw error;
        }
    }
    /**
     * 设置支付限额
     * @param userId 用户ID
     * @param singleLimit 单笔限额
     * @param dailyLimit 日限额
     * @param monthlyLimit 月限额
     */
    public static async setPaymentLimits(userId: number, singleLimit: number, dailyLimit: number, monthlyLimit: number): Promise<void> {
        try {
            await httpClient.put<string>('/security/set-payment-limits', {
                userId: userId,
                singleLimit: singleLimit,
                dailyLimit: dailyLimit,
                monthlyLimit: monthlyLimit
            });
        }
        catch (error) {
            console.error('设置支付限额失败:', error);
            throw error;
        }
    }
    /**
     * 验证支付密码
     * @param userId 用户ID
     * @param payPassword 支付密码
     */
    public static async verifyPayPassword(userId: number, payPassword: string): Promise<boolean> {
        try {
            const response = await httpClient.post<boolean>('/security/verify-pay-password', {
                userId: userId,
                payPassword: payPassword
            });
            return response.data || false;
        }
        catch (error) {
            console.error('验证支付密码失败:', error);
            throw error;
        }
    }
    /**
     * 测试登录（简化版，用于开发测试）
     * @param phone 手机号
     */
    public static async testLogin(phone: string): Promise<LoginResponse> {
        try {
            const response = await httpClient.post<LoginResponse>('/test/login', {
                phone: phone
            });
            if (response.data) {
                return response.data;
            }
            else {
                throw new Error('测试登录响应数据为空');
            }
        }
        catch (error) {
            console.error('测试登录失败:', error);
            throw error;
        }
    }
    /**
     * 测试验证码验证（简化版，用于开发测试）
     * @param phone 手机号
     * @param verificationCode 验证码
     * @param loginType 登录类型
     */
    public static async testVerifyCode(phone: string, verificationCode: string, loginType: string = 'sms'): Promise<boolean> {
        try {
            const response = await httpClient.post<boolean>('/test/verify-code', {
                phone: phone,
                verificationCode: verificationCode,
                loginType: loginType
            });
            return response.data || false;
        }
        catch (error) {
            console.error('测试验证码验证失败:', error);
            throw error;
        }
    }
}
