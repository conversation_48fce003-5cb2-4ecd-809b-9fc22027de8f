import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { WalletInfo } from '../common/types/index';
/**
 * 钱包相关API服务
 */
export class WalletApi {
    /**
     * 获取钱包余额信息
     * @param userId 用户ID
     */
    public static async getWalletBalance(userId: number): Promise<WalletInfo> {
        try {
            const response = await httpClient.get<any>(`/wallet/balance/${userId}`);
            if (response.data) {
                // 转换SpringBoot返回的数据格式为本地格式
                return {
                    accountId: response.data.accountId || 0,
                    userId: userId,
                    balance: response.data.balance || 0,
                    dailyLimit: response.data.dailyLimit || 50000,
                    singleLimit: response.data.singleLimit || 5000,
                    monthlyLimit: response.data.monthlyLimit || 200000,
                    status: response.data.status || 1,
                    createdAt: response.data.createdAt,
                    updatedAt: response.data.updatedAt
                };
            }
            else {
                throw new Error('获取钱包余额响应数据为空');
            }
        }
        catch (error) {
            console.error('获取钱包余额失败:', error);
            throw error;
        }
    }
    /**
     * 获取账户信息（直接调用account接口）
     * @param userId 用户ID
     */
    public static async getAccountInfo(userId: number): Promise<WalletInfo> {
        try {
            const response = await httpClient.get<any>(`/account/${userId}`);
            if (response.data) {
                // 转换SpringBoot返回的Account数据格式为本地WalletInfo格式
                return {
                    accountId: response.data.accountId || 0,
                    userId: response.data.userId || userId,
                    balance: response.data.balance || 0,
                    dailyLimit: response.data.dailyLimit || 50000,
                    singleLimit: response.data.singleLimit || 5000,
                    monthlyLimit: response.data.monthlyLimit || 200000,
                    status: response.data.status || 1,
                    createdAt: response.data.createdAt,
                    updatedAt: response.data.updatedAt
                };
            }
            else {
                throw new Error('获取账户信息响应数据为空');
            }
        }
        catch (error) {
            console.error('获取账户信息失败:', error);
            throw error;
        }
    }
    /**
     * 钱包充值
     * @param userId 用户ID
     * @param amount 充值金额
     * @param cardId 银行卡ID
     * @param payPassword 支付密码
     * @param remark 备注
     */
    public static async recharge(userId: number, amount: number, cardId: number, payPassword: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/recharge', {
                userId: userId,
                amount: amount,
                cardId: cardId,
                payPassword: payPassword,
                remark: remark || '钱包充值'
            });
        }
        catch (error) {
            console.error('钱包充值失败:', error);
            throw error;
        }
    }
    /**
     * 钱包提现
     * @param userId 用户ID
     * @param amount 提现金额
     * @param cardId 银行卡ID
     * @param payPassword 支付密码
     * @param remark 备注
     */
    public static async withdraw(userId: number, amount: number, cardId: number, payPassword: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/withdraw', {
                userId: userId,
                amount: amount,
                cardId: cardId,
                payPassword: payPassword,
                remark: remark || '钱包提现'
            });
        }
        catch (error) {
            console.error('钱包提现失败:', error);
            throw error;
        }
    }
    /**
     * 钱包转账
     * @param fromUserId 转账用户ID
     * @param toAccount 收款账号（银行账号）
     * @param amount 转账金额
     * @param payPassword 支付密码
     * @param remark 备注
     */
    public static async transfer(fromUserId: number, toAccount: string, amount: number, payPassword: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/wallet-transfer', {
                fromUserId: fromUserId,
                toAccount: toAccount,
                amount: amount,
                payPassword: payPassword,
                remark: remark || '钱包转账'
            });
        }
        catch (error) {
            console.error('钱包转账失败:', error);
            throw error;
        }
    }
    /**
     * 钱包转账（通过手机号）
     * @param fromUserId 转账用户ID
     * @param toPhone 收款手机号
     * @param amount 转账金额
     * @param payPassword 支付密码
     * @param remark 备注
     */
    public static async transferByPhone(fromUserId: number, toPhone: string, amount: number, payPassword: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/transfer', {
                fromUserId: fromUserId,
                toPhone: toPhone,
                amount: amount,
                payPassword: payPassword,
                remark: remark || '钱包转账'
            });
        }
        catch (error) {
            console.error('钱包转账失败:', error);
            throw error;
        }
    }
    /**
     * 生成收款码
     * @param userId 用户ID
     * @param amount 收款金额（可选）
     * @param remark 备注
     */
    public static async generateReceiveQR(userId: number, amount?: number, remark?: string): Promise<any> {
        try {
            const response = await httpClient.post<any>('/wallet/generate-qr', {
                userId: userId,
                amount: amount,
                remark: remark || '收款'
            });
            return response.data;
        }
        catch (error) {
            console.error('生成收款码失败:', error);
            throw error;
        }
    }
    /**
     * 收款
     * @param userId 用户ID
     * @param amount 收款金额
     * @param fromPhone 付款方手机号
     * @param remark 备注
     */
    public static async receiveMoney(userId: number, amount: number, fromPhone: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/receive', {
                userId: userId,
                amount: amount,
                fromPhone: fromPhone,
                remark: remark || '收款'
            });
        }
        catch (error) {
            console.error('收款失败:', error);
            throw error;
        }
    }
    /**
     * 钱包支付
     * @param userId 用户ID
     * @param amount 支付金额
     * @param merchantId 商户ID
     * @param payPassword 支付密码
     * @param remark 备注
     */
    public static async walletPay(userId: number, amount: number, merchantId: number, payPassword: string, remark?: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/pay', {
                userId: userId,
                amount: amount,
                merchantId: merchantId,
                payPassword: payPassword,
                remark: remark || '钱包支付'
            });
        }
        catch (error) {
            console.error('钱包支付失败:', error);
            throw error;
        }
    }
    /**
     * 扫码支付
     * @param qrCode 二维码内容
     * @param payerUserId 付款用户ID
     * @param payPassword 支付密码
     */
    public static async scanPay(qrCode: string, payerUserId: number, payPassword: string): Promise<void> {
        try {
            await httpClient.post<string>('/wallet/scan-pay', {
                qrCode: qrCode,
                payerUserId: payerUserId,
                payPassword: payPassword
            });
        }
        catch (error) {
            console.error('扫码支付失败:', error);
            throw error;
        }
    }
    /**
     * 获取钱包信息（兼容方法）
     * @param userId 用户ID
     */
    public static async getWalletInfo(userId?: number): Promise<WalletInfo | null> {
        try {
            if (userId) {
                return await this.getAccountInfo(userId);
            }
            return null;
        }
        catch (error) {
            console.error('获取钱包信息失败:', error);
            return null;
        }
    }
}
