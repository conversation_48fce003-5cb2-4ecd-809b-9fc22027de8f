import http from "@ohos:net.http";
import type { BusinessError } from "@ohos:base";
import { ErrorType } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { ApiError } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { ApiResponse } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
/**
 * HTTP客户端工具类
 * 封装网络请求，提供统一的API调用接口
 */
export class HttpClient {
    private static instance: HttpClient;
    private baseUrl: string = 'http://127.0.0.1:8096/api'; // SpringBoot3运行在8096端口
    private timeout: number = 30000; // 30秒超时
    private maxRetries: number = 3;
    private retryDelay: number = 1000;
    private authToken: string = '';
    private constructor() { }
    public static getInstance(): HttpClient {
        if (!HttpClient.instance) {
            HttpClient.instance = new HttpClient();
        }
        return HttpClient.instance;
    }
    /**
     * 设置基础URL
     */
    public setBaseUrl(url: string): void {
        this.baseUrl = url;
    }
    /**
     * 设置超时时间
     */
    public setTimeout(timeout: number): void {
        this.timeout = timeout;
    }
    /**
     * 设置认证Token
     */
    public setAuthToken(token: string): void {
        this.authToken = token;
    }
    /**
     * 获取完整URL
     */
    private getFullUrl(endpoint: string): string {
        if (endpoint.startsWith('http')) {
            return endpoint;
        }
        return `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    }
    /**
     * 创建HTTP请求
     */
    private createHttpRequest(): http.HttpRequest {
        return http.createHttp();
    }
    /**
     * 构建请求头
     */
    private buildHeaders(customHeaders?: Record<string, string>): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...customHeaders
        };
        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }
        return headers;
    }
    /**
     * 处理HTTP响应
     */
    private async handleResponse<T>(httpRequest: http.HttpRequest): Promise<ApiResponse<T>> {
        return new Promise((resolve, reject) => {
            httpRequest.on('headersReceive', (header) => {
                console.log('Response headers:', JSON.stringify(header));
            });
            httpRequest.on('dataReceive', (data) => {
                try {
                    const responseText = data.toString();
                    console.log('Response data:', responseText);
                    const response: ApiResponse<T> = JSON.parse(responseText);
                    if (response.code === 0) {
                        resolve(response);
                    }
                    else {
                        const error: ApiError = {
                            type: ErrorType.BUSINESS_ERROR,
                            code: response.code,
                            message: response.msg || '业务处理失败'
                        };
                        reject(error);
                    }
                }
                catch (parseError) {
                    const error: ApiError = {
                        type: ErrorType.UNKNOWN_ERROR,
                        code: -1,
                        message: '响应数据解析失败',
                        details: parseError.toString()
                    };
                    reject(error);
                }
            });
            httpRequest.on('dataEnd', () => {
                console.log('HTTP request completed');
            });
            httpRequest.on('error', (err: BusinessError) => {
                console.error('HTTP request error:', err);
                const error: ApiError = {
                    type: ErrorType.NETWORK_ERROR,
                    code: err.code,
                    message: err.message || '网络请求失败'
                };
                reject(error);
            });
        });
    }
    /**
     * 执行HTTP请求（带重试机制）
     */
    private async executeRequest<T>(method: http.RequestMethod, url: string, data?: any, headers?: Record<string, string>, retryCount: number = 0): Promise<ApiResponse<T>> {
        const httpRequest = this.createHttpRequest();
        try {
            const fullUrl = this.getFullUrl(url);
            const requestHeaders = this.buildHeaders(headers);
            console.log(`HTTP ${method} request to: ${fullUrl}`);
            console.log('Request headers:', JSON.stringify(requestHeaders));
            if (data) {
                console.log('Request data:', JSON.stringify(data));
            }
            const requestOptions: http.HttpRequestOptions = {
                method: method,
                header: requestHeaders,
                readTimeout: this.timeout,
                connectTimeout: this.timeout
            };
            if (data && (method === http.RequestMethod.POST || method === http.RequestMethod.PUT)) {
                requestOptions.extraData = JSON.stringify(data);
            }
            httpRequest.request(fullUrl, requestOptions);
            const response = await this.handleResponse<T>(httpRequest);
            httpRequest.destroy();
            return response;
        }
        catch (error) {
            httpRequest.destroy();
            // 重试逻辑
            if (retryCount < this.maxRetries && this.shouldRetry(error as ApiError)) {
                console.log(`Request failed, retrying... (${retryCount + 1}/${this.maxRetries})`);
                await this.delay(this.retryDelay * (retryCount + 1));
                return this.executeRequest<T>(method, url, data, headers, retryCount + 1);
            }
            throw error;
        }
    }
    /**
     * 判断是否应该重试
     */
    private shouldRetry(error: ApiError): boolean {
        return error.type === ErrorType.NETWORK_ERROR ||
            (error.type === ErrorType.BUSINESS_ERROR && error.code >= 500);
    }
    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * GET请求
     */
    public async get<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
        return this.executeRequest<T>(http.RequestMethod.GET, url, undefined, headers);
    }
    /**
     * POST请求
     */
    public async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
        return this.executeRequest<T>(http.RequestMethod.POST, url, data, headers);
    }
    /**
     * PUT请求
     */
    public async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
        return this.executeRequest<T>(http.RequestMethod.PUT, url, data, headers);
    }
    /**
     * DELETE请求
     */
    public async delete<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
        return this.executeRequest<T>(http.RequestMethod.DELETE, url, undefined, headers);
    }
    /**
     * 测试网络连接
     */
    public async testConnection(): Promise<boolean> {
        try {
            const response = await this.get<any>('/test/ping');
            return response.code === 0;
        }
        catch (error) {
            console.error('Network connection test failed:', error);
            return false;
        }
    }
}
// 导出单例实例
export const httpClient = HttpClient.getInstance();
