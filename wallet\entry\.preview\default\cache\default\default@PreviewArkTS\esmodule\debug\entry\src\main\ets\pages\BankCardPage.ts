if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardPage_Params {
    userInfo?: LocalUserInfo | null;
    bankCards?: BankCard[];
    isLoading?: boolean;
    showAddCardDialog?: boolean;
    showCardDetailDialog?: boolean;
    selectedCard?: BankCard | null;
    refreshing?: boolean;
    newCard?: Partial<BankCard>;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard, LocalUserInfo, ApiError } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class BankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showAddCardDialog = new ObservedPropertySimplePU(false, this, "showAddCardDialog");
        this.__showCardDetailDialog = new ObservedPropertySimplePU(false, this, "showCardDetailDialog");
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.__newCard = new ObservedPropertyObjectPU({
            bankName: '',
            cardNumber: '',
            cardType: BankCardType.DEBIT,
            cardHolder: '',
            balance: 0,
            expiryDate: '',
            cvv: '',
            phone: '',
            isDefault: false
        }, this, "newCard");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showAddCardDialog !== undefined) {
            this.showAddCardDialog = params.showAddCardDialog;
        }
        if (params.showCardDetailDialog !== undefined) {
            this.showCardDetailDialog = params.showCardDetailDialog;
        }
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
        if (params.newCard !== undefined) {
            this.newCard = params.newCard;
        }
    }
    updateStateVars(params: BankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showAddCardDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardDetailDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__newCard.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showAddCardDialog.aboutToBeDeleted();
        this.__showCardDetailDialog.aboutToBeDeleted();
        this.__selectedCard.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        this.__newCard.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showAddCardDialog: ObservedPropertySimplePU<boolean>;
    get showAddCardDialog() {
        return this.__showAddCardDialog.get();
    }
    set showAddCardDialog(newValue: boolean) {
        this.__showAddCardDialog.set(newValue);
    }
    private __showCardDetailDialog: ObservedPropertySimplePU<boolean>;
    get showCardDetailDialog() {
        return this.__showCardDetailDialog.get();
    }
    set showCardDetailDialog(newValue: boolean) {
        this.__showCardDetailDialog.set(newValue);
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>;
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    // 添加银行卡表单数据
    private __newCard: ObservedPropertyObjectPU<Partial<BankCard>>;
    get newCard() {
        return this.__newCard.get();
    }
    set newCard(newValue: Partial<BankCard>) {
        this.__newCard.set(newValue);
    }
    async aboutToAppear() {
        console.log('BankCardPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params && params.userId) {
            this.userInfo = {
                userId: params.userId,
                phone: '',
                username: '',
                token: '',
                loginTime: Date.now(),
                rememberLogin: false
            };
        }
        else {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载银行卡列表
        await this.loadBankCards();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载银行卡列表
     */
    async loadBankCards() {
        if (!this.userInfo)
            return;
        this.isLoading = true;
        try {
            const bankCards = await BankCardApi.getCardList(this.userInfo.userId);
            this.bankCards = bankCards;
            console.log(`银行卡列表加载成功: ${bankCards.length}张`);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '加载银行卡列表失败'
            });
        }
        finally {
            this.isLoading = false;
            this.refreshing = false;
        }
    }
    /**
     * 刷新数据
     */
    async refreshData() {
        this.refreshing = true;
        await this.loadBankCards();
    }
    /**
     * 添加银行卡
     */
    async addBankCard() {
        if (!this.userInfo)
            return;
        // 表单验证
        if (!this.newCard.bankName || !this.newCard.cardNumber || !this.newCard.cardHolder) {
            promptAction.showToast({ message: '请填写完整的银行卡信息' });
            return;
        }
        if (this.newCard.cardNumber!.length < 16) {
            promptAction.showToast({ message: '银行卡号长度不正确' });
            return;
        }
        this.isLoading = true;
        try {
            const cardData: Partial<BankCard> = {
                ...this.newCard,
                userId: this.userInfo.userId,
                status: BankCardStatus.NORMAL
            };
            const addedCard = await BankCardApi.addBankCard(cardData);
            // 添加到本地列表
            this.bankCards = [...this.bankCards, addedCard];
            // 重置表单
            this.resetNewCardForm();
            this.showAddCardDialog = false;
            promptAction.showToast({ message: '银行卡添加成功' });
        }
        catch (error) {
            console.error('添加银行卡失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '添加银行卡失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 绑定银行卡
     */
    async bindCard(card: BankCard) {
        if (!this.userInfo)
            return;
        try {
            await BankCardApi.bindCard(this.userInfo.userId, card.cardId);
            // 更新本地状态
            const index = this.bankCards.findIndex(c => c.cardId === card.cardId);
            if (index !== -1) {
                this.bankCards[index] = { ...this.bankCards[index] };
            }
            promptAction.showToast({ message: '银行卡绑定成功' });
            await this.loadBankCards(); // 重新加载列表
        }
        catch (error) {
            console.error('绑定银行卡失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '绑定银行卡失败'
            });
        }
    }
    /**
     * 解绑银行卡
     */
    async unbindCard(card: BankCard) {
        if (!this.userInfo)
            return;
        try {
            await BankCardApi.unbindCard(this.userInfo.userId, card.cardId);
            promptAction.showToast({ message: '银行卡解绑成功' });
            await this.loadBankCards(); // 重新加载列表
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '解绑银行卡失败'
            });
        }
    }
    /**
     * 删除银行卡
     */
    async deleteCard(card: BankCard) {
        if (!this.userInfo)
            return;
        try {
            await BankCardApi.deleteCard(this.userInfo.userId, card.cardId);
            // 从本地列表中移除
            this.bankCards = this.bankCards.filter(c => c.cardId !== card.cardId);
            promptAction.showToast({ message: '银行卡删除成功' });
        }
        catch (error) {
            console.error('删除银行卡失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '删除银行卡失败'
            });
        }
    }
    /**
     * 设置默认银行卡
     */
    async setDefaultCard(card: BankCard) {
        if (!this.userInfo)
            return;
        try {
            await BankCardApi.setDefaultCard(this.userInfo.userId, card.cardId);
            promptAction.showToast({ message: '默认银行卡设置成功' });
            await this.loadBankCards(); // 重新加载列表
        }
        catch (error) {
            console.error('设置默认银行卡失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '设置默认银行卡失败'
            });
        }
    }
    /**
     * 激活/停用银行卡
     */
    async toggleCardStatus(card: BankCard) {
        if (!this.userInfo)
            return;
        try {
            if (card.status === BankCardStatus.NORMAL) {
                await BankCardApi.deactivateCard(this.userInfo.userId, card.cardId);
                promptAction.showToast({ message: '银行卡已停用' });
            }
            else {
                await BankCardApi.activateCard(this.userInfo.userId, card.cardId);
                promptAction.showToast({ message: '银行卡已激活' });
            }
            await this.loadBankCards(); // 重新加载列表
        }
        catch (error) {
            console.error('切换银行卡状态失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '操作失败'
            });
        }
    }
    /**
     * 显示银行卡详情
     */
    showCardDetail(card: BankCard) {
        this.selectedCard = card;
        this.showCardDetailDialog = true;
    }
    /**
     * 重置新卡表单
     */
    resetNewCardForm() {
        this.newCard = {
            bankName: '',
            cardNumber: '',
            cardType: BankCardType.DEBIT,
            cardHolder: '',
            balance: 0,
            expiryDate: '',
            cvv: '',
            phone: '',
            isDefault: false
        };
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(314:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 银行卡列表
        this.BankCardListView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 添加银行卡弹窗
            if (this.showAddCardDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.AddCardDialog.bind(this)();
                });
            }
            // 银行卡详情弹窗
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡详情弹窗
            if (this.showCardDetailDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.CardDetailDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(337:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(338:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡管理');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(346:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(352:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('添加');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(354:7)", "entry");
            Button.fontSize(14);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showAddCardDialog = true;
            });
        }, Button);
        Button.pop();
        Row.pop();
    }
    BankCardListView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading && this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 首次加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(372:7)", "entry");
                        // 首次加载状态
                        Column.width('100%');
                        // 首次加载状态
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(373:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                        Text.margin({ top: 100 });
                    }, Text);
                    Text.pop();
                    // 首次加载状态
                    Column.pop();
                });
            }
            else if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 空状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(382:7)", "entry");
                        // 空状态
                        Column.width('100%');
                        // 空状态
                        Column.justifyContent(FlexAlign.Center);
                        // 空状态
                        Column.margin({ top: 100 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('💳');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(383:9)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无银行卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(387:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('点击右上角"添加"按钮添加银行卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(392:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('添加银行卡');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(397:9)", "entry");
                        Button.fontSize(16);
                        Button.fontColor(Color.White);
                        Button.backgroundColor('#007AFF');
                        Button.borderRadius(8);
                        Button.padding({ horizontal: 24, vertical: 12 });
                        Button.onClick(() => {
                            this.showAddCardDialog = true;
                        });
                    }, Button);
                    Button.pop();
                    // 空状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡列表
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/BankCardPage.ets(412:7)", "entry");
                        // 银行卡列表
                        Scroll.width('100%');
                        // 银行卡列表
                        Scroll.layoutWeight(1);
                        // 银行卡列表
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 银行卡列表
                        Scroll.scrollBar(BarState.Off);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(413:9)", "entry");
                        Column.width('100%');
                        Column.padding({ horizontal: 16, vertical: 16 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const card = _item;
                            this.BankCardItemView(card)
                                .margin.bind(this)(makeBuilderParameterProxy("BankCardItemView", { bottom: () => 16 }));
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                    // 银行卡列表
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
    }
    BankCardItemView(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(430:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡样式
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(432:7)", "entry");
            // 银行卡样式
            Column.width('100%');
            // 银行卡样式
            Column.height(120);
            // 银行卡样式
            Column.padding(20);
            // 银行卡样式
            Column.backgroundColor(this.getCardColor(card.bankName));
            // 银行卡样式
            Column.borderRadius(12);
            // 银行卡样式
            Column.onClick(() => {
                this.showCardDetail(card);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(433:9)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(434:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(439:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(442:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFD700');
                        Text.backgroundColor('rgba(255, 215, 0, 0.2)');
                        Text.borderRadius(4);
                        Text.padding({ horizontal: 8, vertical: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(BankCardApi.formatCardNumber(card.cardNumber));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(453:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Color.White);
            Text.letterSpacing(2);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(460:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.cardHolder);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(461:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#E0E0E0');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(465:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(BankCardApi.getCardTypeText(card.cardType));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(467:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E0E0E0');
        }, Text);
        Text.pop();
        Row.pop();
        // 银行卡样式
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(483:7)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.margin({ top: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(card.status === BankCardStatus.NORMAL ? '停用' : '激活');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(484:9)", "entry");
            Button.fontSize(12);
            Button.fontColor(card.status === BankCardStatus.NORMAL ? '#FF4444' : '#00C851');
            Button.backgroundColor(Color.Transparent);
            Button.border({ width: 1, color: card.status === BankCardStatus.NORMAL ? '#FF4444' : '#00C851' });
            Button.borderRadius(4);
            Button.padding({ horizontal: 12, vertical: 6 });
            Button.onClick(() => {
                this.toggleCardStatus(card);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('设为默认');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(496:11)", "entry");
                        Button.fontSize(12);
                        Button.fontColor('#007AFF');
                        Button.backgroundColor(Color.Transparent);
                        Button.border({ width: 1, color: '#007AFF' });
                        Button.borderRadius(4);
                        Button.padding({ horizontal: 12, vertical: 6 });
                        Button.margin({ left: 8 });
                        Button.onClick(() => {
                            this.setDefaultCard(card);
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(509:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('删除');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(511:9)", "entry");
            Button.fontSize(12);
            Button.fontColor('#FF4444');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.deleteCard(card);
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        Column.pop();
    }
    AddCardDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(526:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(528:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showAddCardDialog = false;
                this.resetNewCardForm();
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加银行卡弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(538:7)", "entry");
            // 添加银行卡弹窗
            Column.width('90%');
            // 添加银行卡弹窗
            Column.maxHeight('80%');
            // 添加银行卡弹窗
            Column.padding(20);
            // 添加银行卡弹窗
            Column.backgroundColor(Color.White);
            // 添加银行卡弹窗
            Column.borderRadius(12);
            // 添加银行卡弹窗
            Column.position({ x: '5%', y: '10%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(539:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/BankCardPage.ets(544:9)", "entry");
            Scroll.width('100%');
            Scroll.height(300);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(545:11)", "entry");
        }, Column);
        // 银行名称
        this.FormInputView.bind(this)('银行名称', this.newCard.bankName || '', (value: string) => {
            this.newCard.bankName = value;
        });
        // 卡号
        this.FormInputView.bind(this)('银行卡号', this.newCard.cardNumber || '', (value: string) => {
            this.newCard.cardNumber = value;
        });
        // 持卡人姓名
        this.FormInputView.bind(this)('持卡人姓名', this.newCard.cardHolder || '', (value: string) => {
            this.newCard.cardHolder = value;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡类型选择
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(562:13)", "entry");
            // 卡类型选择
            Row.width('100%');
            // 卡类型选择
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡类型');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(563:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(567:15)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(569:15)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('借记卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(570:17)", "entry");
            Text.fontSize(14);
            Text.fontColor(this.newCard.cardType === BankCardType.DEBIT ? '#007AFF' : '#666666');
            Text.onClick(() => {
                this.newCard.cardType = BankCardType.DEBIT;
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('信用卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(577:17)", "entry");
            Text.fontSize(14);
            Text.fontColor(this.newCard.cardType === BankCardType.CREDIT ? '#007AFF' : '#666666');
            Text.margin({ left: 20 });
            Text.onClick(() => {
                this.newCard.cardType = BankCardType.CREDIT;
            });
        }, Text);
        Text.pop();
        Row.pop();
        // 卡类型选择
        Row.pop();
        // 手机号
        this.FormInputView.bind(this)('预留手机号', this.newCard.phone || '', (value: string) => {
            this.newCard.phone = value;
        });
        Column.pop();
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(598:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(599:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showAddCardDialog = false;
                this.resetNewCardForm();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(610:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '添加中...' : '确认添加');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(612:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.addBankCard();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 添加银行卡弹窗
        Column.pop();
        Column.pop();
    }
    FormInputView(label: string, value: string, onChange: (value: string) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(639:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(640:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: `请输入${label}`, text: value });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPage.ets(646:7)", "entry");
            TextInput.fontSize(14);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    CardDetailDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.selectedCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(661:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(663:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showCardDetailDialog = false;
                this.selectedCard = null;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡详情弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(673:7)", "entry");
            // 银行卡详情弹窗
            Column.width('80%');
            // 银行卡详情弹窗
            Column.padding(20);
            // 银行卡详情弹窗
            Column.backgroundColor(Color.White);
            // 银行卡详情弹窗
            Column.borderRadius(12);
            // 银行卡详情弹窗
            Column.position({ x: '10%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡详情');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(674:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(679:9)", "entry");
            Column.width('100%');
        }, Column);
        this.DetailItemView.bind(this)('银行名称', this.selectedCard.bankName);
        this.DetailItemView.bind(this)('卡号', BankCardApi.formatCardNumber(this.selectedCard.cardNumber));
        this.DetailItemView.bind(this)('持卡人', this.selectedCard.cardHolder);
        this.DetailItemView.bind(this)('卡类型', BankCardApi.getCardTypeText(this.selectedCard.cardType));
        this.DetailItemView.bind(this)('余额', `¥${this.selectedCard.balance.toFixed(2)}`);
        this.DetailItemView.bind(this)('状态', BankCardApi.getCardStatusText(this.selectedCard.status));
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCard.phone) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('预留手机号', this.selectedCard.phone);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('关闭');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(692:9)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor('#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.showCardDetailDialog = false;
                this.selectedCard = null;
            });
        }, Button);
        Button.pop();
        // 银行卡详情弹窗
        Column.pop();
        Column.pop();
    }
    DetailItemView(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(716:5)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(717:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(722:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
    }
    /**
     * 获取银行卡颜色
     */
    getCardColor(bankName: string): string {
        const colors = [
            '#4A90E2',
            '#7ED321',
            '#F5A623',
            '#D0021B',
            '#9013FE',
            '#50E3C2', // 青色
        ];
        let hash = 0;
        for (let i = 0; i < bankName.length; i++) {
            hash = bankName.charCodeAt(i) + ((hash << 5) - hash);
        }
        return colors[Math.abs(hash) % colors.length];
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardPage";
    }
}
registerNamedRoute(() => new BankCardPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/BankCardPage", pageFullPath: "entry/src/main/ets/pages/BankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
