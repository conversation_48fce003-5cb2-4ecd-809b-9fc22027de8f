if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LoginPage_Params {
    phone?: string;
    password?: string;
    code?: string;
    isLoading?: boolean;
    currentTab?: number;
    countdown?: number;
    canSendCode?: boolean;
    showNetworkSettings?: boolean;
    rememberLogin?: boolean;
    baseUrl?: string;
    isTestMode?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { LoginResponse, LocalUserInfo, NetworkConfig, ApiError } from '../common/types/index';
class LoginPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__phone = new ObservedPropertySimplePU('', this, "phone");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__code = new ObservedPropertySimplePU('', this, "code");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__currentTab = new ObservedPropertySimplePU(0, this, "currentTab");
        this.__countdown = new ObservedPropertySimplePU(0, this, "countdown");
        this.__canSendCode = new ObservedPropertySimplePU(true, this, "canSendCode");
        this.__showNetworkSettings = new ObservedPropertySimplePU(false, this, "showNetworkSettings");
        this.__rememberLogin = new ObservedPropertySimplePU(false, this, "rememberLogin");
        this.__baseUrl = new ObservedPropertySimplePU('http://127.0.0.1:8096/api', this, "baseUrl");
        this.__isTestMode = new ObservedPropertySimplePU(true, this, "isTestMode");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LoginPage_Params) {
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.code !== undefined) {
            this.code = params.code;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.currentTab !== undefined) {
            this.currentTab = params.currentTab;
        }
        if (params.countdown !== undefined) {
            this.countdown = params.countdown;
        }
        if (params.canSendCode !== undefined) {
            this.canSendCode = params.canSendCode;
        }
        if (params.showNetworkSettings !== undefined) {
            this.showNetworkSettings = params.showNetworkSettings;
        }
        if (params.rememberLogin !== undefined) {
            this.rememberLogin = params.rememberLogin;
        }
        if (params.baseUrl !== undefined) {
            this.baseUrl = params.baseUrl;
        }
        if (params.isTestMode !== undefined) {
            this.isTestMode = params.isTestMode;
        }
    }
    updateStateVars(params: LoginPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__code.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTab.purgeDependencyOnElmtId(rmElmtId);
        this.__countdown.purgeDependencyOnElmtId(rmElmtId);
        this.__canSendCode.purgeDependencyOnElmtId(rmElmtId);
        this.__showNetworkSettings.purgeDependencyOnElmtId(rmElmtId);
        this.__rememberLogin.purgeDependencyOnElmtId(rmElmtId);
        this.__baseUrl.purgeDependencyOnElmtId(rmElmtId);
        this.__isTestMode.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__phone.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__code.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__currentTab.aboutToBeDeleted();
        this.__countdown.aboutToBeDeleted();
        this.__canSendCode.aboutToBeDeleted();
        this.__showNetworkSettings.aboutToBeDeleted();
        this.__rememberLogin.aboutToBeDeleted();
        this.__baseUrl.aboutToBeDeleted();
        this.__isTestMode.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __code: ObservedPropertySimplePU<string>;
    get code() {
        return this.__code.get();
    }
    set code(newValue: string) {
        this.__code.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __currentTab: ObservedPropertySimplePU<number>; // 0: 密码登录, 1: 验证码登录
    get currentTab() {
        return this.__currentTab.get();
    }
    set currentTab(newValue: number) {
        this.__currentTab.set(newValue);
    }
    private __countdown: ObservedPropertySimplePU<number>; // 验证码倒计时
    get countdown() {
        return this.__countdown.get();
    }
    set countdown(newValue: number) {
        this.__countdown.set(newValue);
    }
    private __canSendCode: ObservedPropertySimplePU<boolean>; // 是否可以发送验证码
    get canSendCode() {
        return this.__canSendCode.get();
    }
    set canSendCode(newValue: boolean) {
        this.__canSendCode.set(newValue);
    }
    private __showNetworkSettings: ObservedPropertySimplePU<boolean>; // 显示网络设置
    get showNetworkSettings() {
        return this.__showNetworkSettings.get();
    }
    set showNetworkSettings(newValue: boolean) {
        this.__showNetworkSettings.set(newValue);
    }
    private __rememberLogin: ObservedPropertySimplePU<boolean>; // 记住登录状态
    get rememberLogin() {
        return this.__rememberLogin.get();
    }
    set rememberLogin(newValue: boolean) {
        this.__rememberLogin.set(newValue);
    }
    private __baseUrl: ObservedPropertySimplePU<string>; // API服务器地址
    get baseUrl() {
        return this.__baseUrl.get();
    }
    set baseUrl(newValue: string) {
        this.__baseUrl.set(newValue);
    }
    private __isTestMode: ObservedPropertySimplePU<boolean>; // 测试模式
    get isTestMode() {
        return this.__isTestMode.get();
    }
    set isTestMode(newValue: boolean) {
        this.__isTestMode.set(newValue);
    }
    async aboutToAppear() {
        console.log('LoginPage aboutToAppear');
        // 初始化存储管理器
        await this.ensureStorageInitialized();
        // 加载保存的配置
        await this.loadSavedConfig();
        // 检查是否记住登录状态
        await this.checkRememberLogin();
    }
    /**
     * 确保存储管理器已初始化
     */
    async ensureStorageInitialized() {
        try {
            await storageManager.init();
            console.log('StorageManager initialized in LoginPage');
        }
        catch (error) {
            console.error('Failed to initialize StorageManager:', error);
            promptAction.showToast({ message: '存储初始化失败' });
        }
    }
    /**
     * 加载保存的配置
     */
    async loadSavedConfig() {
        try {
            // 加载网络配置
            const networkConfig = await storageManager.getNetworkConfig();
            if (networkConfig) {
                this.baseUrl = networkConfig.baseUrl;
                httpClient.setBaseUrl(networkConfig.baseUrl);
            }
            // 加载保存的手机号
            const savedPhone = await storageManager.getLoginPhone();
            if (savedPhone) {
                this.phone = savedPhone;
            }
            // 加载记住登录状态
            this.rememberLogin = await storageManager.getRememberLogin();
        }
        catch (error) {
            console.error('Failed to load saved config:', error);
        }
    }
    /**
     * 检查记住登录状态
     */
    async checkRememberLogin() {
        try {
            const rememberLogin = await storageManager.getRememberLogin();
            if (rememberLogin) {
                const userInfo = await storageManager.getUserInfo();
                const token = await storageManager.getUserToken();
                if (userInfo && token) {
                    // 设置HTTP客户端的token
                    httpClient.setAuthToken(token);
                    // 直接跳转到主页
                    router.replaceUrl({
                        url: 'pages/MainPage'
                    }).catch((error: Error) => {
                        console.error('跳转主页失败:', error);
                    });
                    return;
                }
            }
        }
        catch (error) {
            console.error('检查记住登录状态失败:', error);
        }
    }
    /**
     * 发送验证码
     */
    async sendVerificationCode() {
        if (!this.phone || this.phone.length !== 11) {
            promptAction.showToast({ message: '请输入正确的手机号' });
            return;
        }
        if (!this.canSendCode) {
            return;
        }
        try {
            this.isLoading = true;
            let verificationCode: string;
            if (this.isTestMode) {
                // 测试模式：直接返回固定验证码
                verificationCode = '525624';
                promptAction.showToast({ message: `验证码: ${verificationCode}` });
            }
            else {
                // 正式模式：调用后端API
                verificationCode = await UserApi.sendVerificationCode(this.phone, 1);
                promptAction.showToast({ message: '验证码发送成功' });
            }
            this.startCountdown();
        }
        catch (error) {
            console.error('发送验证码失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '发送验证码失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 开始倒计时
     */
    startCountdown() {
        this.canSendCode = false;
        this.countdown = 60;
        const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
                clearInterval(timer);
                this.canSendCode = true;
                this.countdown = 0;
            }
        }, 1000);
    }
    /**
     * 处理登录
     */
    async handleLogin() {
        if (this.isLoading)
            return;
        // 表单验证
        if (this.phone.length !== 11) {
            promptAction.showToast({ message: '请输入正确的手机号' });
            return;
        }
        if (this.currentTab === 0) {
            // 密码登录验证
            if (this.password.length < 6) {
                promptAction.showToast({ message: '密码长度不能少于6位' });
                return;
            }
        }
        else {
            // 验证码登录验证
            if (this.code.length !== 6) {
                promptAction.showToast({ message: '请输入6位验证码' });
                return;
            }
        }
        this.isLoading = true;
        try {
            let loginResponse: LoginResponse;
            if (this.isTestMode) {
                // 测试模式登录
                if (this.currentTab === 0) {
                    // 密码登录
                    loginResponse = await UserApi.testLogin(this.phone);
                }
                else {
                    // 验证码登录
                    const isValid = await UserApi.testVerifyCode(this.phone, this.code, 'sms');
                    if (!isValid) {
                        promptAction.showToast({ message: '验证码错误' });
                        return;
                    }
                    loginResponse = await UserApi.testLogin(this.phone);
                }
            }
            else {
                // 正式模式登录
                if (this.currentTab === 0) {
                    loginResponse = await UserApi.loginWithPassword(this.phone, this.password);
                }
                else {
                    loginResponse = await UserApi.loginWithSms(this.phone, this.code);
                }
            }
            // 生成Token（实际项目中应该由后端提供）
            const token = `token_${loginResponse.userId}_${Date.now()}`;
            // 保存登录信息
            await storageManager.saveUserToken(token);
            const localUserInfo: LocalUserInfo = {
                userId: loginResponse.userId,
                phone: loginResponse.phone,
                username: loginResponse.username,
                token: token,
                loginTime: Date.now(),
                rememberLogin: this.rememberLogin
            };
            await storageManager.saveUserInfo(localUserInfo);
            await storageManager.saveLoginPhone(this.phone);
            // 保存记住登录状态
            if (this.rememberLogin) {
                await storageManager.saveRememberLogin(true);
            }
            // 设置HTTP客户端的token
            httpClient.setAuthToken(token);
            promptAction.showToast({ message: '登录成功' });
            // 跳转到主页
            router.replaceUrl({
                url: 'pages/MainPage'
            }).catch((error: Error) => {
                console.error('跳转主页失败:', error);
                promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
            });
        }
        catch (error) {
            console.error('登录失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '登录失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 保存网络配置
     */
    async saveNetworkConfig() {
        try {
            const networkConfig: NetworkConfig = {
                baseUrl: this.baseUrl,
                timeout: 30000,
                retryCount: 3,
                environment: this.baseUrl.includes('localhost') || this.baseUrl.includes('127.0.0.1') ? 'development' : 'production'
            };
            await storageManager.saveNetworkConfig(networkConfig);
            httpClient.setBaseUrl(this.baseUrl);
            promptAction.showToast({ message: '网络配置保存成功' });
            this.showNetworkSettings = false;
        }
        catch (error) {
            console.error('保存网络配置失败:', error);
            promptAction.showToast({ message: '保存网络配置失败' });
        }
    }
    /**
     * 测试网络连接
     */
    async testNetworkConnection() {
        try {
            this.isLoading = true;
            httpClient.setBaseUrl(this.baseUrl);
            const isConnected = await httpClient.testConnection();
            if (isConnected) {
                promptAction.showToast({ message: '网络连接正常' });
            }
            else {
                promptAction.showToast({ message: '网络连接失败' });
            }
        }
        catch (error) {
            console.error('网络连接测试失败:', error);
            promptAction.showToast({ message: '网络连接测试失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(305:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部标题
        this.TopTitleView.bind(this)();
        // 登录表单
        this.LoginFormView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 网络设置弹窗
            if (this.showNetworkSettings) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.NetworkSettingsDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopTitleView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(323:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(324:7)", "entry");
            Text.fontSize(28);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ top: 60, bottom: 10 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全便捷的数字钱包');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(330:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 网络设置按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(336:7)", "entry");
            // 网络设置按钮
            Row.width('90%');
            // 网络设置按钮
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(337:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('网络设置');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(338:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showNetworkSettings = true;
            });
        }, Button);
        Button.pop();
        // 网络设置按钮
        Row.pop();
        Column.pop();
    }
    LoginFormView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(352:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ top: 20 });
        }, Column);
        // 登录方式切换
        this.LoginTabsView.bind(this)();
        // 手机号输入
        this.PhoneInputView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 密码或验证码输入
            if (this.currentTab === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PasswordInputView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.CodeInputView.bind(this)();
                });
            }
        }, If);
        If.pop();
        // 记住登录选项
        this.RememberLoginView.bind(this)();
        // 登录按钮
        this.LoginButtonView.bind(this)();
        Column.pop();
    }
    LoginTabsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(381:5)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.Center);
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(382:7)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.currentTab === 0 ? '#007AFF' : '#666666');
            Text.fontWeight(this.currentTab === 0 ? FontWeight.Bold : FontWeight.Normal);
            Text.padding({ vertical: 10, horizontal: 20 });
            Text.onClick(() => {
                this.currentTab = 0;
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证码登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(391:7)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.currentTab === 1 ? '#007AFF' : '#666666');
            Text.fontWeight(this.currentTab === 1 ? FontWeight.Bold : FontWeight.Normal);
            Text.padding({ vertical: 10, horizontal: 20 });
            Text.onClick(() => {
                this.currentTab = 1;
            });
        }, Text);
        Text.pop();
        Row.pop();
    }
    PhoneInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(406:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('手机号');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(407:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入手机号', text: this.phone });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(413:7)", "entry");
            TextInput.type(InputType.PhoneNumber);
            TextInput.maxLength(11);
            TextInput.fontSize(16);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.phone = value;
            });
        }, TextInput);
        Column.pop();
    }
    PasswordInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(430:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(431:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入密码', text: this.password });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(437:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(16);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.password = value;
            });
        }, TextInput);
        Column.pop();
    }
    CodeInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(453:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(454:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证码');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(455:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(459:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.canSendCode ? '发送验证码' : `${this.countdown}s`);
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(461:9)", "entry");
            Button.fontSize(14);
            Button.fontColor(this.canSendCode ? '#007AFF' : '#999999');
            Button.backgroundColor(Color.Transparent);
            Button.enabled(this.canSendCode);
            Button.onClick(() => {
                this.sendVerificationCode();
            });
        }, Button);
        Button.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位验证码', text: this.code });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(473:7)", "entry");
            TextInput.type(InputType.Number);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.code = value;
            });
        }, TextInput);
        Column.pop();
    }
    RememberLoginView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(490:5)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Checkbox.create({ name: 'remember', group: 'loginGroup' });
            Checkbox.debugLine("entry/src/main/ets/pages/LoginPage.ets(491:7)", "entry");
            Checkbox.select(this.rememberLogin);
            Checkbox.onChange((value: boolean) => {
                this.rememberLogin = value;
            });
        }, Checkbox);
        Checkbox.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('记住登录状态（7天内免登录）');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(497:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
    }
    LoginButtonView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '登录中...' : '登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(507:5)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleLogin();
            });
        }, Button);
        Button.pop();
    }
    NetworkSettingsDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(521:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(523:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showNetworkSettings = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(532:7)", "entry");
            // 设置弹窗
            Column.width('80%');
            // 设置弹窗
            Column.padding(20);
            // 设置弹窗
            Column.backgroundColor(Color.White);
            // 设置弹窗
            Column.borderRadius(12);
            // 设置弹窗
            Column.position({ x: '10%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('网络设置');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(533:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(538:9)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('API服务器地址');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(539:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入API服务器地址', text: this.baseUrl });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(545:11)", "entry");
            TextInput.fontSize(14);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.baseUrl = value;
            });
        }, TextInput);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(557:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试连接');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(558:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#007AFF');
            Button.backgroundColor('#F0F8FF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 16, vertical: 8 });
            Button.onClick(() => {
                this.testNetworkConnection();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(568:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(570:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 16, vertical: 8 });
            Button.onClick(() => {
                this.showNetworkSettings = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('保存');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(580:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor('#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 16, vertical: 8 });
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.saveNetworkConfig();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 设置弹窗
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LoginPage";
    }
}
registerNamedRoute(() => new LoginPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/LoginPage", pageFullPath: "entry/src/main/ets/pages/LoginPage", integratedHsp: "false", moduleType: "followWithHap" });
