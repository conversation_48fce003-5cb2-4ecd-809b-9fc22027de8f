if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MainPage_Params {
    userInfo?: LocalUserInfo | null;
    walletInfo?: WalletInfo | null;
    bankCards?: BankCard[];
    recentTransactions?: Transaction[];
    isLoading?: boolean;
    currentTabIndex?: number;
    refreshing?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { TransactionType } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { LocalUserInfo, WalletInfo, BankCard, Transaction, ApiError } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class MainPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__walletInfo = new ObservedPropertyObjectPU(null, this, "walletInfo");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__recentTransactions = new ObservedPropertyObjectPU([], this, "recentTransactions");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__currentTabIndex = new ObservedPropertySimplePU(0, this, "currentTabIndex");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MainPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.walletInfo !== undefined) {
            this.walletInfo = params.walletInfo;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.recentTransactions !== undefined) {
            this.recentTransactions = params.recentTransactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.currentTabIndex !== undefined) {
            this.currentTabIndex = params.currentTabIndex;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
    }
    updateStateVars(params: MainPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__walletInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__recentTransactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTabIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__walletInfo.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__recentTransactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__currentTabIndex.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __walletInfo: ObservedPropertyObjectPU<WalletInfo | null>;
    get walletInfo() {
        return this.__walletInfo.get();
    }
    set walletInfo(newValue: WalletInfo | null) {
        this.__walletInfo.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __recentTransactions: ObservedPropertyObjectPU<Transaction[]>;
    get recentTransactions() {
        return this.__recentTransactions.get();
    }
    set recentTransactions(newValue: Transaction[]) {
        this.__recentTransactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __currentTabIndex: ObservedPropertySimplePU<number>; // 0: 钱包首页, 1: 交易记录, 2: 银行卡管理, 3: 个人设置
    get currentTabIndex() {
        return this.__currentTabIndex.get();
    }
    set currentTabIndex(newValue: number) {
        this.__currentTabIndex.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    async aboutToAppear() {
        console.log('MainPage aboutToAppear');
        // 初始化存储管理器
        await this.ensureStorageInitialized();
        // 检查登录状态
        await this.checkLoginStatus();
        // 加载数据
        await this.loadAllData();
        // 设置自动刷新
        this.setupAutoRefresh();
    }
    /**
     * 确保存储管理器已初始化
     */
    async ensureStorageInitialized() {
        try {
            await storageManager.init();
            console.log('StorageManager initialized in MainPage');
        }
        catch (error) {
            console.error('Failed to initialize StorageManager:', error);
        }
    }
    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
        try {
            const userInfo = await storageManager.getUserInfo();
            const token = await storageManager.getUserToken();
            if (!userInfo || !token) {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
                return;
            }
            this.userInfo = userInfo;
            httpClient.setAuthToken(token);
        }
        catch (error) {
            console.error('检查登录状态失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载所有数据
     */
    async loadAllData() {
        if (!this.userInfo)
            return;
        this.isLoading = true;
        try {
            // 并行加载数据
            await Promise.all([
                this.loadWalletInfo(),
                this.loadBankCards(),
                this.loadRecentTransactions()
            ]);
        }
        catch (error) {
            console.error('加载数据失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '加载数据失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 加载钱包信息
     */
    async loadWalletInfo() {
        if (!this.userInfo)
            return;
        try {
            const walletInfo = await WalletApi.getAccountInfo(this.userInfo.userId);
            this.walletInfo = walletInfo;
            // 保存到本地存储
            await storageManager.saveWalletInfo({
                accountId: walletInfo.accountId,
                balance: walletInfo.balance,
                dailyLimit: walletInfo.dailyLimit,
                singleLimit: walletInfo.singleLimit,
                monthlyLimit: walletInfo.monthlyLimit,
                lastUpdateTime: Date.now()
            });
            console.log('钱包信息加载成功:', walletInfo);
        }
        catch (error) {
            console.error('加载钱包信息失败:', error);
            // 尝试从本地存储加载
            const localWalletInfo = await storageManager.getWalletInfo();
            if (localWalletInfo) {
                this.walletInfo = {
                    accountId: localWalletInfo.accountId,
                    userId: this.userInfo.userId,
                    balance: localWalletInfo.balance,
                    dailyLimit: localWalletInfo.dailyLimit,
                    singleLimit: localWalletInfo.singleLimit,
                    monthlyLimit: localWalletInfo.monthlyLimit,
                    status: 1
                };
            }
        }
    }
    /**
     * 加载银行卡列表
     */
    async loadBankCards() {
        if (!this.userInfo)
            return;
        try {
            const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
            this.bankCards = bankCards;
            console.log('银行卡列表加载成功:', bankCards.length);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
        }
    }
    /**
     * 加载最近交易记录
     */
    async loadRecentTransactions() {
        if (!this.userInfo)
            return;
        try {
            const transactions = await TransactionApi.getRecentTransactions(this.userInfo.userId, 5);
            this.recentTransactions = transactions;
            console.log('最近交易记录加载成功:', transactions.length);
        }
        catch (error) {
            console.error('加载最近交易记录失败:', error);
        }
    }
    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒自动刷新一次数据
        setInterval(() => {
            if (!this.refreshing) {
                this.refreshData();
            }
        }, 30000);
    }
    /**
     * 刷新数据
     */
    async refreshData() {
        this.refreshing = true;
        try {
            await this.loadAllData();
            console.log('数据刷新成功');
        }
        catch (error) {
            console.error('数据刷新失败:', error);
        }
        finally {
            this.refreshing = false;
        }
    }
    /**
     * 手动下拉刷新
     */
    async onRefresh() {
        await this.refreshData();
        promptAction.showToast({ message: '刷新完成' });
    }
    /**
     * 跳转到钱包操作页面
     */
    navigateToWalletOperation(operationType: string) {
        router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: {
                operationType: operationType,
                userId: this.userInfo?.userId
            }
        }).catch((error: Error) => {
            console.error('跳转钱包操作页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 跳转到支付页面
     */
    navigateToPayment() {
        router.pushUrl({
            url: 'pages/PaymentPage',
            params: {
                userId: this.userInfo?.userId
            }
        }).catch((error: Error) => {
            console.error('跳转支付页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 跳转到银行卡管理页面
     */
    navigateToBankCardManagement() {
        router.pushUrl({
            url: 'pages/BankCardPage',
            params: {
                userId: this.userInfo?.userId
            }
        }).catch((error: Error) => {
            console.error('跳转银行卡管理页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 跳转到个人设置页面
     */
    navigateToSettings() {
        router.pushUrl({
            url: 'pages/SettingsPage',
            params: {
                userId: this.userInfo?.userId
            }
        }).catch((error: Error) => {
            console.error('跳转个人设置页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 切换到交易记录页面
     */
    switchToTransactionRecords() {
        router.pushUrl({
            url: 'pages/TransactionRecordsPage',
            params: {
                userId: this.userInfo?.userId
            }
        }).catch((error: Error) => {
            console.error('跳转交易记录页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 退出登录
     */
    async logout() {
        try {
            await storageManager.clearUserData();
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
        catch (error) {
            console.error('退出登录失败:', error);
            promptAction.showToast({ message: '退出登录失败' });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(306:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 主要内容区域
        this.MainContentView.bind(this)();
        // 底部导航栏
        this.BottomNavigationView.bind(this)();
        Column.pop();
    }
    MainContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/MainPage.ets(319:5)", "entry");
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
            Scroll.onScrollStart(() => {
                // 开始滚动时的处理
            });
            Scroll.onScrollEnd(() => {
                // 滚动结束时的处理
            });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(320:7)", "entry");
            Column.width('100%');
            Column.padding({ bottom: 80 });
        }, Column);
        // 钱包卡片区域
        this.WalletCardView.bind(this)();
        // 快捷操作区域
        this.QuickActionsView.bind(this)();
        // 最近交易区域
        this.RecentTransactionsView.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    WalletCardView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(346:5)", "entry");
            Column.width('90%');
            Column.backgroundColor('#007AFF');
            Column.borderRadius(16);
            Column.margin({ horizontal: 20, vertical: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(347:7)", "entry");
            Row.width('100%');
            Row.padding({ horizontal: 20, vertical: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户头像和信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(349:9)", "entry");
            // 用户头像和信息
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/MainPage.ets(350:11)", "entry");
            Image.width(50);
            Image.height(50);
            Image.borderRadius(25);
            Image.backgroundColor('#E0E0E0');
            Image.onClick(() => {
                this.navigateToSettings();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo?.username || '用户');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(359:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Color.White);
            Text.margin({ top: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatPhoneNumber(this.userInfo?.phone || ''));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(365:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E0E0E0');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 用户头像和信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/MainPage.ets(372:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置按钮
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/MainPage.ets(375:9)", "entry");
            // 设置按钮
            Image.width(24);
            // 设置按钮
            Image.height(24);
            // 设置按钮
            Image.fillColor(Color.White);
            // 设置按钮
            Image.onClick(() => {
                this.navigateToSettings();
            });
        }, Image);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包余额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(387:7)", "entry");
            // 钱包余额
            Column.width('100%');
            // 钱包余额
            Column.alignItems(HorizontalAlign.Center);
            // 钱包余额
            Column.padding({ horizontal: 20, vertical: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包余额（元）');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(388:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#E0E0E0');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.walletInfo?.balance?.toFixed(2) || '0.00');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(393:9)", "entry");
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        // 钱包余额
        Column.pop();
        Column.pop();
    }
    QuickActionsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(409:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ horizontal: 20, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快捷操作');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(410:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(417:7)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.QuickActionButton.bind(this)('充值', '💰', () => {
            this.navigateToWalletOperation('recharge');
        });
        this.QuickActionButton.bind(this)('提现', '💸', () => {
            this.navigateToWalletOperation('withdraw');
        });
        this.QuickActionButton.bind(this)('转账', '💳', () => {
            this.navigateToWalletOperation('transfer');
        });
        this.QuickActionButton.bind(this)('收钱', '💵', () => {
            this.navigateToWalletOperation('receive');
        });
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付功能按钮
            Button.createWithLabel('立即支付');
            Button.debugLine("entry/src/main/ets/pages/MainPage.ets(438:7)", "entry");
            // 支付功能按钮
            Button.width('100%');
            // 支付功能按钮
            Button.height(48);
            // 支付功能按钮
            Button.fontSize(16);
            // 支付功能按钮
            Button.fontColor(Color.White);
            // 支付功能按钮
            Button.backgroundColor('#FF6B35');
            // 支付功能按钮
            Button.borderRadius(8);
            // 支付功能按钮
            Button.margin({ top: 16 });
            // 支付功能按钮
            Button.onClick(() => {
                this.navigateToPayment();
            });
        }, Button);
        // 支付功能按钮
        Button.pop();
        Column.pop();
    }
    QuickActionButton(title: string, icon: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(458:5)", "entry");
            Column.width(60);
            Column.height(80);
            Column.justifyContent(FlexAlign.Center);
            Column.backgroundColor('#F8F8F8');
            Column.borderRadius(8);
            Column.onClick(onClick);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(459:7)", "entry");
            Text.fontSize(24);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(463:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Column.pop();
    }
    RecentTransactionsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(476:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ horizontal: 20, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(477:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近交易');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(478:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/MainPage.ets(483:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(485:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#007AFF');
            Text.onClick(() => {
                this.switchToTransactionRecords();
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.recentTransactions.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(496:9)", "entry");
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const transaction = _item;
                            this.TransactionItemView.bind(this)(transaction);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (index < this.recentTransactions.length - 1) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Divider.create();
                                            Divider.debugLine("entry/src/main/ets/pages/MainPage.ets(501:15)", "entry");
                                            Divider.color('#F0F0F0');
                                            Divider.strokeWidth(1);
                                            Divider.margin({ vertical: 8 });
                                        }, Divider);
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                    });
                                }
                            }, If);
                            If.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.recentTransactions, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(510:9)", "entry");
                        Column.width('100%');
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(511:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ vertical: 40 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TransactionItemView(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(528:5)", "entry");
            Row.width('100%');
            Row.padding({ vertical: 8 });
            Row.onClick(() => {
                // 跳转到交易详情页面
                router.pushUrl({
                    url: 'pages/TransactionDetailPage',
                    params: {
                        txnId: transaction.txnId
                    }
                });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Text.create(this.getTransactionIcon(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(530:7)", "entry");
            // 交易类型图标
            Text.fontSize(20);
            // 交易类型图标
            Text.width(40);
            // 交易类型图标
            Text.height(40);
            // 交易类型图标
            Text.textAlign(TextAlign.Center);
            // 交易类型图标
            Text.backgroundColor('#F0F8FF');
            // 交易类型图标
            Text.borderRadius(20);
            // 交易类型图标
            Text.margin({ right: 12 });
        }, Text);
        // 交易类型图标
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(540:7)", "entry");
            // 交易信息
            Column.alignItems(HorizontalAlign.Start);
            // 交易信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(TransactionApi.getTransactionTypeText(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(541:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatTransactionTime(transaction.createdAt));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(546:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易金额
            Text.create(TransactionApi.formatAmount(transaction.amount, transaction.type));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(556:7)", "entry");
            // 交易金额
            Text.fontSize(16);
            // 交易金额
            Text.fontWeight(FontWeight.Bold);
            // 交易金额
            Text.fontColor(TransactionApi.getAmountColor(transaction.type));
        }, Text);
        // 交易金额
        Text.pop();
        Row.pop();
    }
    BottomNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(575:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor(Color.White);
            Row.justifyContent(FlexAlign.SpaceEvenly);
            Row.position({ x: 0, y: '100%' });
            Row.translate({ y: -60 });
            Row.border({ width: { top: 1 }, color: '#E0E0E0' });
        }, Row);
        this.BottomNavItem.bind(this)('钱包', '💰', 0);
        this.BottomNavItem.bind(this)('交易', '📊', 1);
        this.BottomNavItem.bind(this)('银行卡', '💳', 2);
        this.BottomNavItem.bind(this)('设置', '⚙️', 3);
        Row.pop();
    }
    BottomNavItem(title: string, icon: string, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(591:5)", "entry");
            Column.justifyContent(FlexAlign.Center);
            Column.onClick(() => {
                this.currentTabIndex = index;
                switch (index) {
                    case 0:
                        // 当前页面，刷新数据
                        this.refreshData();
                        break;
                    case 1:
                        this.switchToTransactionRecords();
                        break;
                    case 2:
                        this.navigateToBankCardManagement();
                        break;
                    case 3:
                        this.navigateToSettings();
                        break;
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(592:7)", "entry");
            Text.fontSize(20);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(596:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.currentTabIndex === index ? '#007AFF' : '#999999');
        }, Text);
        Text.pop();
        Column.pop();
    }
    /**
     * 格式化手机号显示
     */
    formatPhoneNumber(phone: string): string {
        if (!phone || phone.length < 11) {
            return phone;
        }
        return phone.substring(0, 3) + '****' + phone.substring(7);
    }
    /**
     * 获取交易类型图标
     */
    getTransactionIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE:
                return '💰';
            case TransactionType.TRANSFER:
                return '💸';
            case TransactionType.RECEIVE:
                return '💵';
            case TransactionType.PAYMENT:
                return '🛒';
            default:
                return '💳';
        }
    }
    /**
     * 格式化交易时间显示
     */
    formatTransactionTime(timeStr: string): string {
        try {
            const date = new Date(timeStr);
            const now = new Date();
            const diff = now.getTime() - date.getTime();
            if (diff < 60000) { // 1分钟内
                return '刚刚';
            }
            else if (diff < 3600000) { // 1小时内
                return `${Math.floor(diff / 60000)}分钟前`;
            }
            else if (diff < 86400000) { // 24小时内
                return `${Math.floor(diff / 3600000)}小时前`;
            }
            else {
                return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            }
        }
        catch (error) {
            return timeStr;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MainPage";
    }
}
registerNamedRoute(() => new MainPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/MainPage", pageFullPath: "entry/src/main/ets/pages/MainPage", integratedHsp: "false", moduleType: "followWithHap" });
