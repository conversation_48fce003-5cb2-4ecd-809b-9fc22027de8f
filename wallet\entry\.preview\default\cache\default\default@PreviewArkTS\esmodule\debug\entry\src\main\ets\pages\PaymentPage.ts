if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PaymentPage_Params {
    userInfo?: LocalUserInfo | null;
    paymentChannel?: PaymentChannel;
    paymentMethod?: PaymentMethod;
    amount?: string;
    selectedCard?: BankCard | null;
    bankCards?: BankCard[];
    payPassword?: string;
    merchantInfo?: string;
    productCategory?: string;
    isLoading?: boolean;
    showCardSelector?: boolean;
    showPayPasswordDialog?: boolean;
    showChannelSelector?: boolean;
    walletBalance?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { PaymentMethod, PaymentChannel } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { LocalUserInfo, BankCard, ApiError } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class PaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__paymentChannel = new ObservedPropertySimplePU(PaymentChannel.MERCHANT, this, "paymentChannel");
        this.__paymentMethod = new ObservedPropertySimplePU(PaymentMethod.WALLET, this, "paymentMethod");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__merchantInfo = new ObservedPropertySimplePU('', this, "merchantInfo");
        this.__productCategory = new ObservedPropertySimplePU('', this, "productCategory");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.__showPayPasswordDialog = new ObservedPropertySimplePU(false, this, "showPayPasswordDialog");
        this.__showChannelSelector = new ObservedPropertySimplePU(false, this, "showChannelSelector");
        this.__walletBalance = new ObservedPropertySimplePU(0, this, "walletBalance");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PaymentPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.paymentChannel !== undefined) {
            this.paymentChannel = params.paymentChannel;
        }
        if (params.paymentMethod !== undefined) {
            this.paymentMethod = params.paymentMethod;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.merchantInfo !== undefined) {
            this.merchantInfo = params.merchantInfo;
        }
        if (params.productCategory !== undefined) {
            this.productCategory = params.productCategory;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
        if (params.showPayPasswordDialog !== undefined) {
            this.showPayPasswordDialog = params.showPayPasswordDialog;
        }
        if (params.showChannelSelector !== undefined) {
            this.showChannelSelector = params.showChannelSelector;
        }
        if (params.walletBalance !== undefined) {
            this.walletBalance = params.walletBalance;
        }
    }
    updateStateVars(params: PaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentChannel.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentMethod.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__merchantInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__productCategory.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
        this.__showPayPasswordDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showChannelSelector.purgeDependencyOnElmtId(rmElmtId);
        this.__walletBalance.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__paymentChannel.aboutToBeDeleted();
        this.__paymentMethod.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__selectedCard.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__merchantInfo.aboutToBeDeleted();
        this.__productCategory.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        this.__showPayPasswordDialog.aboutToBeDeleted();
        this.__showChannelSelector.aboutToBeDeleted();
        this.__walletBalance.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __paymentChannel: ObservedPropertySimplePU<PaymentChannel>; // merchant, QR_code, NFC
    get paymentChannel() {
        return this.__paymentChannel.get();
    }
    set paymentChannel(newValue: PaymentChannel) {
        this.__paymentChannel.set(newValue);
    }
    private __paymentMethod: ObservedPropertySimplePU<PaymentMethod>; // wallet, bank_card
    get paymentMethod() {
        return this.__paymentMethod.get();
    }
    set paymentMethod(newValue: PaymentMethod) {
        this.__paymentMethod.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>;
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __merchantInfo: ObservedPropertySimplePU<string>; // 商户信息
    get merchantInfo() {
        return this.__merchantInfo.get();
    }
    set merchantInfo(newValue: string) {
        this.__merchantInfo.set(newValue);
    }
    private __productCategory: ObservedPropertySimplePU<string>; // 商品类别
    get productCategory() {
        return this.__productCategory.get();
    }
    set productCategory(newValue: string) {
        this.__productCategory.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    private __showPayPasswordDialog: ObservedPropertySimplePU<boolean>;
    get showPayPasswordDialog() {
        return this.__showPayPasswordDialog.get();
    }
    set showPayPasswordDialog(newValue: boolean) {
        this.__showPayPasswordDialog.set(newValue);
    }
    private __showChannelSelector: ObservedPropertySimplePU<boolean>;
    get showChannelSelector() {
        return this.__showChannelSelector.get();
    }
    set showChannelSelector(newValue: boolean) {
        this.__showChannelSelector.set(newValue);
    }
    private __walletBalance: ObservedPropertySimplePU<number>;
    get walletBalance() {
        return this.__walletBalance.get();
    }
    set walletBalance(newValue: number) {
        this.__walletBalance.set(newValue);
    }
    async aboutToAppear() {
        console.log('PaymentPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params) {
            this.paymentChannel = params.paymentChannel || PaymentChannel.MERCHANT;
            this.amount = params.amount || '';
            this.merchantInfo = params.merchantInfo || '';
            if (params.userId) {
                this.userInfo = {
                    userId: params.userId,
                    phone: '',
                    username: '',
                    token: '',
                    loginTime: Date.now(),
                    rememberLogin: false
                };
            }
        }
        if (!this.userInfo) {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载银行卡列表和钱包余额
        await this.loadBankCards();
        await this.loadWalletBalance();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载银行卡列表
     */
    async loadBankCards() {
        if (!this.userInfo)
            return;
        try {
            const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
            this.bankCards = bankCards;
            // 自动选择默认银行卡
            const defaultCard = bankCards.find(card => card.isDefault);
            if (defaultCard) {
                this.selectedCard = defaultCard;
            }
            else if (bankCards.length > 0) {
                this.selectedCard = bankCards[0];
            }
            console.log(`银行卡列表加载成功: ${bankCards.length}张`);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
        }
    }
    /**
     * 加载钱包余额
     */
    async loadWalletBalance() {
        if (!this.userInfo)
            return;
        try {
            const balance = await WalletApi.getWalletBalance(this.userInfo.userId);
            this.walletBalance = balance;
            console.log(`钱包余额: ¥${balance}`);
        }
        catch (error) {
            console.error('加载钱包余额失败:', error);
        }
    }
    /**
     * 执行支付
     */
    async executePayment() {
        if (!this.userInfo || !this.validateForm()) {
            return;
        }
        this.showPayPasswordDialog = true;
    }
    /**
     * 确认支付
     */
    async confirmPayment() {
        if (!this.userInfo || !this.payPassword) {
            promptAction.showToast({ message: '请输入支付密码' });
            return;
        }
        this.isLoading = true;
        this.showPayPasswordDialog = false;
        try {
            const amountValue = parseFloat(this.amount);
            if (this.paymentMethod === PaymentMethod.WALLET) {
                // 钱包支付
                await WalletApi.walletPay(this.userInfo.userId, amountValue, this.paymentChannel, this.payPassword, this.merchantInfo || '商户支付', this.productCategory);
            }
            else {
                // 银行卡支付
                if (!this.selectedCard) {
                    throw new Error('请选择银行卡');
                }
                await WalletApi.bankCardPay(this.userInfo.userId, this.selectedCard.cardId, amountValue, this.paymentChannel, this.payPassword, this.merchantInfo || '商户支付', this.productCategory);
            }
            promptAction.showToast({ message: '支付成功' });
            // 清空表单
            this.resetForm();
            // 延迟返回上一页
            setTimeout(() => {
                router.back();
            }, 1500);
        }
        catch (error) {
            console.error('支付失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '支付失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 表单验证
     */
    validateForm(): boolean {
        if (!this.amount || parseFloat(this.amount) <= 0) {
            promptAction.showToast({ message: '请输入正确的支付金额' });
            return false;
        }
        const amountValue = parseFloat(this.amount);
        if (this.paymentMethod === PaymentMethod.WALLET) {
            if (amountValue > this.walletBalance) {
                promptAction.showToast({ message: '钱包余额不足' });
                return false;
            }
        }
        else {
            if (!this.selectedCard) {
                promptAction.showToast({ message: '请选择银行卡' });
                return false;
            }
            if (amountValue > this.selectedCard.balance) {
                promptAction.showToast({ message: '银行卡余额不足' });
                return false;
            }
        }
        return true;
    }
    /**
     * 重置表单
     */
    resetForm() {
        this.amount = '';
        this.payPassword = '';
        this.merchantInfo = '';
        this.productCategory = '';
    }
    /**
     * 选择支付方式
     */
    selectPaymentMethod(method: PaymentMethod) {
        this.paymentMethod = method;
    }
    /**
     * 选择银行卡
     */
    selectCard(card: BankCard) {
        this.selectedCard = card;
        this.showCardSelector = false;
    }
    /**
     * 选择支付渠道
     */
    selectPaymentChannel(channel: PaymentChannel) {
        this.paymentChannel = channel;
        this.showChannelSelector = false;
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    /**
     * 获取支付渠道文本
     */
    getPaymentChannelText(channel: PaymentChannel): string {
        switch (channel) {
            case PaymentChannel.MERCHANT:
                return '商户支付';
            case PaymentChannel.QR_CODE:
                return '扫码支付';
            case PaymentChannel.NFC:
                return 'NFC支付';
            default:
                return '支付';
        }
    }
    /**
     * 获取支付渠道图标
     */
    getPaymentChannelIcon(channel: PaymentChannel): string {
        switch (channel) {
            case PaymentChannel.MERCHANT:
                return '🏪';
            case PaymentChannel.QR_CODE:
                return '📱';
            case PaymentChannel.NFC:
                return '📡';
            default:
                return '💳';
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(303:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 支付表单
        this.PaymentFormView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择弹窗
            if (this.showCardSelector) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.CardSelectorDialog.bind(this)();
                });
            }
            // 支付密码弹窗
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 支付密码弹窗
            if (this.showPayPasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PayPasswordDialog.bind(this)();
                });
            }
            // 支付渠道选择弹窗
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 支付渠道选择弹窗
            if (this.showChannelSelector) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.ChannelSelectorDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(331:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(332:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(340:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PaymentPage.ets(346:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPaymentChannelIcon(this.paymentChannel));
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(348:7)", "entry");
            Text.fontSize(20);
            Text.onClick(() => {
                this.showChannelSelector = true;
            });
        }, Text);
        Text.pop();
        Row.pop();
    }
    PaymentFormView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/PaymentPage.ets(362:5)", "entry");
            Scroll.width('100%');
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(363:7)", "entry");
            Column.width('100%');
            Column.padding({ horizontal: 16, vertical: 20 });
        }, Column);
        // 支付渠道显示
        this.PaymentChannelView.bind(this)();
        // 金额输入
        this.AmountInputView.bind(this)();
        // 支付方式选择
        this.PaymentMethodView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 商户信息（如果是商户支付）
            if (this.paymentChannel === PaymentChannel.MERCHANT) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.MerchantInfoView.bind(this)();
                });
            }
            // 确认支付按钮
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 确认支付按钮
        this.ConfirmPaymentButtonView.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    PaymentChannelView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(391:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付渠道');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(392:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(399:7)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(Color.White);
            Row.borderRadius(12);
            Row.onClick(() => {
                this.showChannelSelector = true;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPaymentChannelIcon(this.paymentChannel));
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(400:9)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPaymentChannelText(this.paymentChannel));
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(404:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(409:9)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#CCCCCC');
        }, Image);
        Row.pop();
        Column.pop();
    }
    AmountInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(427:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付金额');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(428:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(435:7)", "entry");
            Row.width('100%');
            Row.padding({ horizontal: 16, vertical: 24 });
            Row.backgroundColor(Color.White);
            Row.borderRadius(12);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('¥');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(436:9)", "entry");
            Text.fontSize(32);
            Text.fontColor('#333333');
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '0.00', text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(441:9)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(32);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        Row.pop();
        Column.pop();
    }
    PaymentMethodView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(462:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付方式');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(463:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(470:7)", "entry");
            Column.width('100%');
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包支付选项
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(472:9)", "entry");
            // 钱包支付选项
            Row.width('100%');
            // 钱包支付选项
            Row.padding(16);
            // 钱包支付选项
            Row.onClick(() => {
                this.selectPaymentMethod(PaymentMethod.WALLET);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(473:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(474:13)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(478:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(479:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`余额: ¥${this.walletBalance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(484:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.paymentMethod === PaymentMethod.WALLET) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(496:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#007AFF');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 钱包支付选项
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/PaymentPage.ets(507:9)", "entry");
            Divider.color('#F0F0F0');
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡支付选项
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(511:9)", "entry");
            // 银行卡支付选项
            Row.width('100%');
            // 银行卡支付选项
            Row.padding(16);
            // 银行卡支付选项
            Row.onClick(() => {
                this.selectPaymentMethod(PaymentMethod.BANK_CARD);
                if (this.bankCards.length > 0) {
                    this.showCardSelector = true;
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(512:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(513:13)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(517:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(518:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.selectedCard.bankName} ${BankCardApi.formatCardNumber(this.selectedCard.cardNumber)}`);
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(524:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#666666');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请选择银行卡');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(530:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999999');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 2 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(542:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.paymentMethod === PaymentMethod.BANK_CARD) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(544:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#007AFF');
                        Text.margin({ right: 8 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(550:13)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#CCCCCC');
        }, Image);
        Row.pop();
        // 银行卡支付选项
        Row.pop();
        Column.pop();
        Column.pop();
    }
    MerchantInfoView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(574:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商户信息');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(575:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(582:7)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入商户名称', text: this.merchantInfo });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(583:9)", "entry");
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.margin({ bottom: 12 });
            TextInput.onChange((value: string) => {
                this.merchantInfo = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入商品类别（可选）', text: this.productCategory });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(593:9)", "entry");
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.productCategory = value;
            });
        }, TextInput);
        Column.pop();
        Column.pop();
    }
    ConfirmPaymentButtonView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '支付中...' : `确认支付 ¥${this.amount || '0.00'}`);
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(612:5)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.executePayment();
            });
        }, Button);
        Button.pop();
    }
    CardSelectorDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(626:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(628:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showCardSelector = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(637:7)", "entry");
            // 银行卡选择弹窗
            Column.width('80%');
            // 银行卡选择弹窗
            Column.padding(20);
            // 银行卡选择弹窗
            Column.backgroundColor(Color.White);
            // 银行卡选择弹窗
            Column.borderRadius(12);
            // 银行卡选择弹窗
            Column.position({ x: '10%', y: '25%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(638:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCards.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(644:11)", "entry");
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(646:15)", "entry");
                                Row.width('100%');
                                Row.padding(16);
                                Row.onClick(() => {
                                    this.selectCard(card);
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Column.create();
                                Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(647:17)", "entry");
                                Column.alignItems(HorizontalAlign.Start);
                                Column.layoutWeight(1);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(card.bankName);
                                Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(648:19)", "entry");
                                Text.fontSize(16);
                                Text.fontColor('#333333');
                                Text.alignSelf(ItemAlign.Start);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(BankCardApi.formatCardNumber(card.cardNumber));
                                Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(653:19)", "entry");
                                Text.fontSize(14);
                                Text.fontColor('#666666');
                                Text.alignSelf(ItemAlign.Start);
                                Text.margin({ top: 4 });
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(`余额: ¥${card.balance.toFixed(2)}`);
                                Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(659:19)", "entry");
                                Text.fontSize(12);
                                Text.fontColor('#999999');
                                Text.alignSelf(ItemAlign.Start);
                                Text.margin({ top: 2 });
                            }, Text);
                            Text.pop();
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (this.selectedCard?.cardId === card.cardId) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('✓');
                                            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(669:19)", "entry");
                                            Text.fontSize(16);
                                            Text.fontColor('#007AFF');
                                        }, Text);
                                        Text.pop();
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                    });
                                }
                            }, If);
                            If.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(683:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ vertical: 20 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(689:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.showCardSelector = false;
            });
        }, Button);
        Button.pop();
        // 银行卡选择弹窗
        Column.pop();
        Column.pop();
    }
    PayPasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(712:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(714:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(724:7)", "entry");
            // 支付密码弹窗
            Column.width('85%');
            // 支付密码弹窗
            Column.padding(20);
            // 支付密码弹窗
            Column.backgroundColor(Color.White);
            // 支付密码弹窗
            Column.borderRadius(12);
            // 支付密码弹窗
            Column.position({ x: '7.5%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请输入支付密码');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(725:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`支付金额: ¥${this.amount}`);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(730:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`支付方式: ${this.paymentMethod === PaymentMethod.WALLET ? '钱包支付' : '银行卡支付'}`);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(735:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位支付密码', text: this.payPassword });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(740:9)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(751:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(752:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/PaymentPage.ets(763:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '支付中...' : '确认支付');
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(765:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.confirmPayment();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 支付密码弹窗
        Column.pop();
        Column.pop();
    }
    ChannelSelectorDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(791:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(793:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showChannelSelector = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付渠道选择弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(802:7)", "entry");
            // 支付渠道选择弹窗
            Column.width('80%');
            // 支付渠道选择弹窗
            Column.padding(20);
            // 支付渠道选择弹窗
            Column.backgroundColor(Color.White);
            // 支付渠道选择弹窗
            Column.borderRadius(12);
            // 支付渠道选择弹窗
            Column.position({ x: '10%', y: '35%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择支付渠道');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(803:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(808:9)", "entry");
            Column.width('100%');
        }, Column);
        this.ChannelOptionView.bind(this)('商户支付', PaymentChannel.MERCHANT, '🏪');
        this.ChannelOptionView.bind(this)('扫码支付', PaymentChannel.QR_CODE, '📱');
        this.ChannelOptionView.bind(this)('NFC支付', PaymentChannel.NFC, '📡');
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(815:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.showChannelSelector = false;
            });
        }, Button);
        Button.pop();
        // 支付渠道选择弹窗
        Column.pop();
        Column.pop();
    }
    ChannelOptionView(title: string, channel: PaymentChannel, icon: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(838:5)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ horizontal: 16 });
            Row.onClick(() => {
                this.selectPaymentChannel(channel);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(839:7)", "entry");
            Text.fontSize(20);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(843:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.paymentChannel === channel) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(849:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#007AFF');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PaymentPage";
    }
}
registerNamedRoute(() => new PaymentPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/PaymentPage", pageFullPath: "entry/src/main/ets/pages/PaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
