if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    userInfo?: LocalUserInfo | null;
    fullUserInfo?: UserInfo | null;
    isLoading?: boolean;
    showChangePasswordDialog?: boolean;
    showPayPasswordDialog?: boolean;
    showLimitDialog?: boolean;
    showProfileDialog?: boolean;
    oldPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
    payPassword?: string;
    confirmPayPassword?: string;
    dailyLimit?: number;
    singleLimit?: number;
    monthlyLimit?: number;
    editUsername?: string;
    editEmail?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo, UserInfo, ApiError } from '../common/types/index';
class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__fullUserInfo = new ObservedPropertyObjectPU(null, this, "fullUserInfo");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showChangePasswordDialog = new ObservedPropertySimplePU(false, this, "showChangePasswordDialog");
        this.__showPayPasswordDialog = new ObservedPropertySimplePU(false, this, "showPayPasswordDialog");
        this.__showLimitDialog = new ObservedPropertySimplePU(false, this, "showLimitDialog");
        this.__showProfileDialog = new ObservedPropertySimplePU(false, this, "showProfileDialog");
        this.__oldPassword = new ObservedPropertySimplePU('', this, "oldPassword");
        this.__newPassword = new ObservedPropertySimplePU('', this, "newPassword");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__confirmPayPassword = new ObservedPropertySimplePU('', this, "confirmPayPassword");
        this.__dailyLimit = new ObservedPropertySimplePU(50000, this, "dailyLimit");
        this.__singleLimit = new ObservedPropertySimplePU(5000, this, "singleLimit");
        this.__monthlyLimit = new ObservedPropertySimplePU(200000, this, "monthlyLimit");
        this.__editUsername = new ObservedPropertySimplePU('', this, "editUsername");
        this.__editEmail = new ObservedPropertySimplePU('', this, "editEmail");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.fullUserInfo !== undefined) {
            this.fullUserInfo = params.fullUserInfo;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showChangePasswordDialog !== undefined) {
            this.showChangePasswordDialog = params.showChangePasswordDialog;
        }
        if (params.showPayPasswordDialog !== undefined) {
            this.showPayPasswordDialog = params.showPayPasswordDialog;
        }
        if (params.showLimitDialog !== undefined) {
            this.showLimitDialog = params.showLimitDialog;
        }
        if (params.showProfileDialog !== undefined) {
            this.showProfileDialog = params.showProfileDialog;
        }
        if (params.oldPassword !== undefined) {
            this.oldPassword = params.oldPassword;
        }
        if (params.newPassword !== undefined) {
            this.newPassword = params.newPassword;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.confirmPayPassword !== undefined) {
            this.confirmPayPassword = params.confirmPayPassword;
        }
        if (params.dailyLimit !== undefined) {
            this.dailyLimit = params.dailyLimit;
        }
        if (params.singleLimit !== undefined) {
            this.singleLimit = params.singleLimit;
        }
        if (params.monthlyLimit !== undefined) {
            this.monthlyLimit = params.monthlyLimit;
        }
        if (params.editUsername !== undefined) {
            this.editUsername = params.editUsername;
        }
        if (params.editEmail !== undefined) {
            this.editEmail = params.editEmail;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__fullUserInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showChangePasswordDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showPayPasswordDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showLimitDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showProfileDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__oldPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__newPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPayPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__dailyLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__singleLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__monthlyLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__editUsername.purgeDependencyOnElmtId(rmElmtId);
        this.__editEmail.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__fullUserInfo.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showChangePasswordDialog.aboutToBeDeleted();
        this.__showPayPasswordDialog.aboutToBeDeleted();
        this.__showLimitDialog.aboutToBeDeleted();
        this.__showProfileDialog.aboutToBeDeleted();
        this.__oldPassword.aboutToBeDeleted();
        this.__newPassword.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__confirmPayPassword.aboutToBeDeleted();
        this.__dailyLimit.aboutToBeDeleted();
        this.__singleLimit.aboutToBeDeleted();
        this.__monthlyLimit.aboutToBeDeleted();
        this.__editUsername.aboutToBeDeleted();
        this.__editEmail.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __fullUserInfo: ObservedPropertyObjectPU<UserInfo | null>;
    get fullUserInfo() {
        return this.__fullUserInfo.get();
    }
    set fullUserInfo(newValue: UserInfo | null) {
        this.__fullUserInfo.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showChangePasswordDialog: ObservedPropertySimplePU<boolean>;
    get showChangePasswordDialog() {
        return this.__showChangePasswordDialog.get();
    }
    set showChangePasswordDialog(newValue: boolean) {
        this.__showChangePasswordDialog.set(newValue);
    }
    private __showPayPasswordDialog: ObservedPropertySimplePU<boolean>;
    get showPayPasswordDialog() {
        return this.__showPayPasswordDialog.get();
    }
    set showPayPasswordDialog(newValue: boolean) {
        this.__showPayPasswordDialog.set(newValue);
    }
    private __showLimitDialog: ObservedPropertySimplePU<boolean>;
    get showLimitDialog() {
        return this.__showLimitDialog.get();
    }
    set showLimitDialog(newValue: boolean) {
        this.__showLimitDialog.set(newValue);
    }
    private __showProfileDialog: ObservedPropertySimplePU<boolean>;
    get showProfileDialog() {
        return this.__showProfileDialog.get();
    }
    set showProfileDialog(newValue: boolean) {
        this.__showProfileDialog.set(newValue);
    }
    // 修改密码表单
    private __oldPassword: ObservedPropertySimplePU<string>;
    get oldPassword() {
        return this.__oldPassword.get();
    }
    set oldPassword(newValue: string) {
        this.__oldPassword.set(newValue);
    }
    private __newPassword: ObservedPropertySimplePU<string>;
    get newPassword() {
        return this.__newPassword.get();
    }
    set newPassword(newValue: string) {
        this.__newPassword.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    // 支付密码表单
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __confirmPayPassword: ObservedPropertySimplePU<string>;
    get confirmPayPassword() {
        return this.__confirmPayPassword.get();
    }
    set confirmPayPassword(newValue: string) {
        this.__confirmPayPassword.set(newValue);
    }
    // 限额设置表单
    private __dailyLimit: ObservedPropertySimplePU<number>;
    get dailyLimit() {
        return this.__dailyLimit.get();
    }
    set dailyLimit(newValue: number) {
        this.__dailyLimit.set(newValue);
    }
    private __singleLimit: ObservedPropertySimplePU<number>;
    get singleLimit() {
        return this.__singleLimit.get();
    }
    set singleLimit(newValue: number) {
        this.__singleLimit.set(newValue);
    }
    private __monthlyLimit: ObservedPropertySimplePU<number>;
    get monthlyLimit() {
        return this.__monthlyLimit.get();
    }
    set monthlyLimit(newValue: number) {
        this.__monthlyLimit.set(newValue);
    }
    // 个人资料表单
    private __editUsername: ObservedPropertySimplePU<string>;
    get editUsername() {
        return this.__editUsername.get();
    }
    set editUsername(newValue: string) {
        this.__editUsername.set(newValue);
    }
    private __editEmail: ObservedPropertySimplePU<string>;
    get editEmail() {
        return this.__editEmail.get();
    }
    set editEmail(newValue: string) {
        this.__editEmail.set(newValue);
    }
    async aboutToAppear() {
        console.log('SettingsPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params && params.userId) {
            this.userInfo = {
                userId: params.userId,
                phone: '',
                username: '',
                token: '',
                loginTime: Date.now(),
                rememberLogin: false
            };
        }
        else {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载完整用户信息
        await this.loadFullUserInfo();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载完整用户信息
     */
    async loadFullUserInfo() {
        if (!this.userInfo)
            return;
        this.isLoading = true;
        try {
            const fullUserInfo = await UserApi.getUserProfile(this.userInfo.userId);
            this.fullUserInfo = fullUserInfo;
            // 初始化表单数据
            this.editUsername = fullUserInfo.username || '';
            this.editEmail = fullUserInfo.email || '';
            this.dailyLimit = fullUserInfo.dailyLimit || 50000;
            this.singleLimit = fullUserInfo.singleLimit || 5000;
            this.monthlyLimit = fullUserInfo.monthlyLimit || 200000;
            console.log('完整用户信息加载成功:', fullUserInfo);
        }
        catch (error) {
            console.error('加载完整用户信息失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '加载用户信息失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 修改登录密码
     */
    async changePassword() {
        if (!this.userInfo)
            return;
        // 表单验证
        if (!this.oldPassword || !this.newPassword || !this.confirmPassword) {
            promptAction.showToast({ message: '请填写完整的密码信息' });
            return;
        }
        if (this.newPassword !== this.confirmPassword) {
            promptAction.showToast({ message: '两次输入的新密码不一致' });
            return;
        }
        if (this.newPassword.length < 6) {
            promptAction.showToast({ message: '新密码长度不能少于6位' });
            return;
        }
        this.isLoading = true;
        try {
            await UserApi.changePassword(this.userInfo.userId, this.oldPassword, this.newPassword);
            // 重置表单
            this.oldPassword = '';
            this.newPassword = '';
            this.confirmPassword = '';
            this.showChangePasswordDialog = false;
            promptAction.showToast({ message: '密码修改成功' });
        }
        catch (error) {
            console.error('修改密码失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '修改密码失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 设置支付密码
     */
    async setPayPassword() {
        if (!this.userInfo)
            return;
        // 表单验证
        if (!this.payPassword || !this.confirmPayPassword) {
            promptAction.showToast({ message: '请填写完整的支付密码信息' });
            return;
        }
        if (this.payPassword !== this.confirmPayPassword) {
            promptAction.showToast({ message: '两次输入的支付密码不一致' });
            return;
        }
        if (this.payPassword.length !== 6) {
            promptAction.showToast({ message: '支付密码必须为6位数字' });
            return;
        }
        this.isLoading = true;
        try {
            await UserApi.setPaymentPassword(this.userInfo.userId, this.payPassword);
            // 重置表单
            this.payPassword = '';
            this.confirmPayPassword = '';
            this.showPayPasswordDialog = false;
            promptAction.showToast({ message: '支付密码设置成功' });
        }
        catch (error) {
            console.error('设置支付密码失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '设置支付密码失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 设置交易限额
     */
    async setTransactionLimits() {
        if (!this.userInfo)
            return;
        // 表单验证
        if (this.dailyLimit <= 0 || this.singleLimit <= 0 || this.monthlyLimit <= 0) {
            promptAction.showToast({ message: '限额必须大于0' });
            return;
        }
        if (this.singleLimit > this.dailyLimit) {
            promptAction.showToast({ message: '单笔限额不能大于日限额' });
            return;
        }
        if (this.dailyLimit * 30 > this.monthlyLimit) {
            promptAction.showToast({ message: '月限额设置可能过低' });
            return;
        }
        this.isLoading = true;
        try {
            await UserApi.setTransactionLimits(this.userInfo.userId, this.dailyLimit, this.singleLimit, this.monthlyLimit);
            this.showLimitDialog = false;
            promptAction.showToast({ message: '交易限额设置成功' });
            // 重新加载用户信息
            await this.loadFullUserInfo();
        }
        catch (error) {
            console.error('设置交易限额失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '设置交易限额失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 更新个人资料
     */
    async updateProfile() {
        if (!this.userInfo)
            return;
        // 表单验证
        if (!this.editUsername.trim()) {
            promptAction.showToast({ message: '用户名不能为空' });
            return;
        }
        this.isLoading = true;
        try {
            await UserApi.updateProfile(this.userInfo.userId, this.editUsername.trim(), this.editEmail.trim());
            this.showProfileDialog = false;
            promptAction.showToast({ message: '个人资料更新成功' });
            // 重新加载用户信息
            await this.loadFullUserInfo();
            // 更新本地存储的用户信息
            if (this.userInfo) {
                this.userInfo.username = this.editUsername.trim();
                await storageManager.saveUserInfo(this.userInfo);
            }
        }
        catch (error) {
            console.error('更新个人资料失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '更新个人资料失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 退出登录
     */
    async logout() {
        try {
            await storageManager.clearUserData();
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
        catch (error) {
            console.error('退出登录失败:', error);
            promptAction.showToast({ message: '退出登录失败' });
        }
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(317:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 设置内容
        this.SettingsContentView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 各种弹窗
            if (this.showChangePasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.ChangePasswordDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showPayPasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PayPasswordDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showLimitDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LimitDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showProfileDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.ProfileDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(347:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(348:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('个人设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(356:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/SettingsPage.ets(362:7)", "entry");
        }, Blank);
        Blank.pop();
        Row.pop();
    }
    SettingsContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/SettingsPage.ets(372:5)", "entry");
            Scroll.width('100%');
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(373:7)", "entry");
            Column.width('100%');
            Column.padding({ horizontal: 16, vertical: 16 });
        }, Column);
        // 用户信息卡片
        this.UserInfoCardView.bind(this)();
        // 账户安全设置
        this.SecuritySettingsView.bind(this)();
        // 交易设置
        this.TransactionSettingsView.bind(this)();
        // 其他设置
        this.OtherSettingsView.bind(this)();
        // 退出登录按钮
        this.LogoutButtonView.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    UserInfoCardView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(399:5)", "entry");
            Column.width('100%');
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(400:7)", "entry");
            Row.width('100%');
            Row.padding(20);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户头像
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(402:9)", "entry");
            // 用户头像
            Image.width(60);
            // 用户头像
            Image.height(60);
            // 用户头像
            Image.borderRadius(30);
            // 用户头像
            Image.backgroundColor('#E0E0E0');
            // 用户头像
            Image.margin({ right: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(410:9)", "entry");
            // 用户信息
            Column.alignItems(HorizontalAlign.Start);
            // 用户信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.fullUserInfo?.username || this.userInfo?.username || '用户');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(411:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatPhoneNumber(this.fullUserInfo?.phone || this.userInfo?.phone || ''));
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(417:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.fullUserInfo?.email) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.fullUserInfo.email);
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(424:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999999');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 用户信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 编辑按钮
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(435:9)", "entry");
            // 编辑按钮
            Image.width(20);
            // 编辑按钮
            Image.height(20);
            // 编辑按钮
            Image.fillColor('#666666');
            // 编辑按钮
            Image.onClick(() => {
                this.showProfileDialog = true;
            });
        }, Image);
        Row.pop();
        Column.pop();
    }
    SecuritySettingsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(453:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('账户安全');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(454:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.SettingItemView.bind(this)('修改登录密码', '保护账户安全', () => {
            this.showChangePasswordDialog = true;
        });
        this.SettingItemView.bind(this)('设置支付密码', '用于支付验证', () => {
            this.showPayPasswordDialog = true;
        });
        Column.pop();
    }
    TransactionSettingsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(477:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(478:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.SettingItemView.bind(this)('交易限额设置', `日限额: ¥${this.dailyLimit.toFixed(0)}`, () => {
            this.showLimitDialog = true;
        });
        this.SettingItemView.bind(this)('风险提醒', '开启交易风险提醒', () => {
            promptAction.showToast({ message: '功能开发中' });
        });
        Column.pop();
    }
    OtherSettingsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(501:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('其他设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(502:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.SettingItemView.bind(this)('关于我们', '版本信息和帮助', () => {
            promptAction.showToast({ message: '钱包应用 v1.0.0' });
        });
        this.SettingItemView.bind(this)('隐私政策', '查看隐私政策', () => {
            promptAction.showToast({ message: '功能开发中' });
        });
        this.SettingItemView.bind(this)('用户协议', '查看用户协议', () => {
            promptAction.showToast({ message: '功能开发中' });
        });
        Column.pop();
    }
    SettingItemView(title: string, subtitle: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(529:5)", "entry");
            Row.width('100%');
            Row.padding({ vertical: 12 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(530:7)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(531:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(subtitle);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(536:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(545:7)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#CCCCCC');
        }, Image);
        Row.pop();
    }
    LogoutButtonView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('退出登录');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(556:5)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor('#FF4444');
            Button.backgroundColor(Color.White);
            Button.borderRadius(8);
            Button.border({ width: 1, color: '#FF4444' });
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.logout();
            });
        }, Button);
        Button.pop();
    }
    ChangePasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(571:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(573:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showChangePasswordDialog = false;
                this.oldPassword = '';
                this.newPassword = '';
                this.confirmPassword = '';
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 修改密码弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(585:7)", "entry");
            // 修改密码弹窗
            Column.width('85%');
            // 修改密码弹窗
            Column.padding(20);
            // 修改密码弹窗
            Column.backgroundColor(Color.White);
            // 修改密码弹窗
            Column.borderRadius(12);
            // 修改密码弹窗
            Column.position({ x: '7.5%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改登录密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(586:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(591:9)", "entry");
            Column.width('100%');
        }, Column);
        this.PasswordInputView.bind(this)('当前密码', this.oldPassword, (value: string) => {
            this.oldPassword = value;
        });
        this.PasswordInputView.bind(this)('新密码', this.newPassword, (value: string) => {
            this.newPassword = value;
        });
        this.PasswordInputView.bind(this)('确认新密码', this.confirmPassword, (value: string) => {
            this.confirmPassword = value;
        });
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(606:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(607:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showChangePasswordDialog = false;
                this.oldPassword = '';
                this.newPassword = '';
                this.confirmPassword = '';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/SettingsPage.ets(620:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '修改中...' : '确认修改');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(622:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.changePassword();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 修改密码弹窗
        Column.pop();
        Column.pop();
    }
    PayPasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(648:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(650:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
                this.confirmPayPassword = '';
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置支付密码弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(661:7)", "entry");
            // 设置支付密码弹窗
            Column.width('85%');
            // 设置支付密码弹窗
            Column.padding(20);
            // 设置支付密码弹窗
            Column.backgroundColor(Color.White);
            // 设置支付密码弹窗
            Column.borderRadius(12);
            // 设置支付密码弹窗
            Column.position({ x: '7.5%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置支付密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(662:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(667:9)", "entry");
            Column.width('100%');
        }, Column);
        this.PasswordInputView.bind(this)('支付密码(6位数字)', this.payPassword, (value: string) => {
            this.payPassword = value;
        });
        this.PasswordInputView.bind(this)('确认支付密码', this.confirmPayPassword, (value: string) => {
            this.confirmPayPassword = value;
        });
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(678:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(679:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
                this.confirmPayPassword = '';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/SettingsPage.ets(691:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '设置中...' : '确认设置');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(693:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.setPayPassword();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 设置支付密码弹窗
        Column.pop();
        Column.pop();
    }
    LimitDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(719:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(721:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showLimitDialog = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 限额设置弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(730:7)", "entry");
            // 限额设置弹窗
            Column.width('85%');
            // 限额设置弹窗
            Column.padding(20);
            // 限额设置弹窗
            Column.backgroundColor(Color.White);
            // 限额设置弹窗
            Column.borderRadius(12);
            // 限额设置弹窗
            Column.position({ x: '7.5%', y: '25%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易限额设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(731:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(736:9)", "entry");
            Column.width('100%');
        }, Column);
        this.NumberInputView.bind(this)('单笔限额(元)', this.singleLimit, (value: number) => {
            this.singleLimit = value;
        });
        this.NumberInputView.bind(this)('日限额(元)', this.dailyLimit, (value: number) => {
            this.dailyLimit = value;
        });
        this.NumberInputView.bind(this)('月限额(元)', this.monthlyLimit, (value: number) => {
            this.monthlyLimit = value;
        });
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(751:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(752:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showLimitDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/SettingsPage.ets(762:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '设置中...' : '确认设置');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(764:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.setTransactionLimits();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 限额设置弹窗
        Column.pop();
        Column.pop();
    }
    ProfileDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(790:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(792:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showProfileDialog = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 个人资料弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(801:7)", "entry");
            // 个人资料弹窗
            Column.width('85%');
            // 个人资料弹窗
            Column.padding(20);
            // 个人资料弹窗
            Column.backgroundColor(Color.White);
            // 个人资料弹窗
            Column.borderRadius(12);
            // 个人资料弹窗
            Column.position({ x: '7.5%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('编辑个人资料');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(802:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(807:9)", "entry");
            Column.width('100%');
        }, Column);
        this.FormInputView.bind(this)('用户名', this.editUsername, (value: string) => {
            this.editUsername = value;
        });
        this.FormInputView.bind(this)('邮箱', this.editEmail, (value: string) => {
            this.editEmail = value;
        });
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(818:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(819:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showProfileDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/SettingsPage.ets(829:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '更新中...' : '确认更新');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(831:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.updateProfile();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 个人资料弹窗
        Column.pop();
        Column.pop();
    }
    PasswordInputView(label: string, value: string, onChange: (value: string) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(857:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(858:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: `请输入${label}`, text: value });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(864:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(14);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    NumberInputView(label: string, value: number, onChange: (value: number) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(878:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(879:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: `请输入${label}`, text: value.toString() });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(885:7)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(14);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((text: string) => {
                const num = parseFloat(text) || 0;
                onChange(num);
            });
        }, TextInput);
        Column.pop();
    }
    FormInputView(label: string, value: string, onChange: (value: string) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(902:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(903:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: `请输入${label}`, text: value });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(909:7)", "entry");
            TextInput.fontSize(14);
            TextInput.padding(12);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    /**
     * 格式化手机号显示
     */
    formatPhoneNumber(phone: string): string {
        if (!phone || phone.length < 11) {
            return phone;
        }
        return phone.substring(0, 3) + '****' + phone.substring(7);
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
