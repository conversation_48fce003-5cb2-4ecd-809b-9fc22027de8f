if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionDetailPage_Params {
    userInfo?: LocalUserInfo | null;
    transaction?: Transaction | null;
    isLoading?: boolean;
    txnId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { Transaction, LocalUserInfo, ApiError } from '../common/types/index';
class TransactionDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__transaction = new ObservedPropertyObjectPU(null, this, "transaction");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__txnId = new ObservedPropertySimplePU(0, this, "txnId");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionDetailPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.transaction !== undefined) {
            this.transaction = params.transaction;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.txnId !== undefined) {
            this.txnId = params.txnId;
        }
    }
    updateStateVars(params: TransactionDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__transaction.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__txnId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__transaction.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__txnId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __transaction: ObservedPropertyObjectPU<Transaction | null>;
    get transaction() {
        return this.__transaction.get();
    }
    set transaction(newValue: Transaction | null) {
        this.__transaction.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __txnId: ObservedPropertySimplePU<number>;
    get txnId() {
        return this.__txnId.get();
    }
    set txnId(newValue: number) {
        this.__txnId.set(newValue);
    }
    async aboutToAppear() {
        console.log('TransactionDetailPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params) {
            this.txnId = params.txnId || 0;
            if (params.userId) {
                this.userInfo = {
                    userId: params.userId,
                    phone: '',
                    username: '',
                    token: '',
                    loginTime: Date.now(),
                    rememberLogin: false
                };
            }
        }
        if (!this.userInfo) {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载交易详情
        await this.loadTransactionDetail();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载交易详情
     */
    async loadTransactionDetail() {
        if (!this.userInfo || !this.txnId)
            return;
        this.isLoading = true;
        try {
            const transaction = await TransactionApi.getTransactionDetail(this.txnId);
            this.transaction = transaction;
            console.log('交易详情加载成功:', transaction);
        }
        catch (error) {
            console.error('加载交易详情失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '加载交易详情失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    /**
     * 格式化交易时间
     */
    formatTransactionTime(timeStr: string): string {
        try {
            const date = new Date(timeStr);
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
        }
        catch (error) {
            return timeStr;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(113:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 交易详情内容
        this.TransactionDetailView.bind(this)();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(126:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(127:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易详情');
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(135:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(141:7)", "entry");
        }, Blank);
        Blank.pop();
        Row.pop();
    }
    TransactionDetailView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(153:7)", "entry");
                        // 加载状态
                        Column.width('100%');
                        // 加载状态
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(154:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                        Text.margin({ top: 100 });
                    }, Text);
                    Text.pop();
                    // 加载状态
                    Column.pop();
                });
            }
            else if (!this.transaction) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 空状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(163:7)", "entry");
                        // 空状态
                        Column.width('100%');
                        // 空状态
                        Column.justifyContent(FlexAlign.Center);
                        // 空状态
                        Column.margin({ top: 100 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📄');
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(164:9)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('交易详情不存在');
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(168:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                    }, Text);
                    Text.pop();
                    // 空状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 交易详情内容
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(177:7)", "entry");
                        // 交易详情内容
                        Scroll.width('100%');
                        // 交易详情内容
                        Scroll.layoutWeight(1);
                        // 交易详情内容
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 交易详情内容
                        Scroll.scrollBar(BarState.Off);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(178:9)", "entry");
                        Column.width('100%');
                        Column.padding({ horizontal: 16, vertical: 20 });
                    }, Column);
                    // 交易状态卡片
                    this.TransactionStatusCardView.bind(this)();
                    // 交易信息卡片
                    this.TransactionInfoCardView.bind(this)();
                    // 交易流水信息
                    this.TransactionFlowCardView.bind(this)();
                    Column.pop();
                    // 交易详情内容
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
    }
    TransactionStatusCardView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.transaction) {
                this.ifElseBranchUpdateFunction(0, () => {
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(201:5)", "entry");
            Column.width('100%');
            Column.padding({ vertical: 40 });
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Text.create(this.getTransactionIcon(this.transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(203:7)", "entry");
            // 交易类型图标
            Text.fontSize(48);
            // 交易类型图标
            Text.margin({ bottom: 16 });
        }, Text);
        // 交易类型图标
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易金额
            Text.create(TransactionApi.formatAmount(this.transaction.amount, this.transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(208:7)", "entry");
            // 交易金额
            Text.fontSize(32);
            // 交易金额
            Text.fontWeight(FontWeight.Bold);
            // 交易金额
            Text.fontColor(TransactionApi.getAmountColor(this.transaction.type));
            // 交易金额
            Text.margin({ bottom: 8 });
        }, Text);
        // 交易金额
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易状态
            Text.create(TransactionApi.getTransactionStatusText(this.transaction.status));
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(215:7)", "entry");
            // 交易状态
            Text.fontSize(16);
            // 交易状态
            Text.fontColor(this.getStatusColor(this.transaction.status));
            // 交易状态
            Text.margin({ bottom: 16 });
        }, Text);
        // 交易状态
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型
            Text.create(TransactionApi.getTransactionTypeText(this.transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(221:7)", "entry");
            // 交易类型
            Text.fontSize(14);
            // 交易类型
            Text.fontColor('#666666');
        }, Text);
        // 交易类型
        Text.pop();
        Column.pop();
    }
    TransactionInfoCardView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.transaction) {
                this.ifElseBranchUpdateFunction(0, () => {
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(235:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易信息');
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(236:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(243:7)", "entry");
            Column.width('100%');
        }, Column);
        this.DetailItemView.bind(this)('交易单号', this.transaction.txnId.toString());
        this.DetailItemView.bind(this)('交易时间', this.formatTransactionTime(this.transaction.createdAt));
        this.DetailItemView.bind(this)('交易金额', `¥${this.transaction.amount.toFixed(2)}`);
        this.DetailItemView.bind(this)('交易类型', TransactionApi.getTransactionTypeText(this.transaction.type));
        this.DetailItemView.bind(this)('交易状态', TransactionApi.getTransactionStatusText(this.transaction.status));
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.fromAccount) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('付款账户', this.transaction.fromAccount);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.toAccount) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('收款账户', this.transaction.toAccount);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.remark) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('交易备注', this.transaction.remark);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Column.pop();
    }
    TransactionFlowCardView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (!this.transaction) {
                this.ifElseBranchUpdateFunction(0, () => {
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(274:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易流水');
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(275:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(282:7)", "entry");
            Column.width('100%');
        }, Column);
        this.DetailItemView.bind(this)('流水号', this.transaction.txnId.toString());
        this.DetailItemView.bind(this)('创建时间', this.formatTransactionTime(this.transaction.createdAt));
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.updatedAt && this.transaction.updatedAt !== this.transaction.createdAt) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('更新时间', this.formatTransactionTime(this.transaction.updatedAt));
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.paymentMethod) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('支付方式', this.transaction.paymentMethod === 1 ? '钱包支付' : '银行卡支付');
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transaction.paymentChannel) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItemView.bind(this)('支付渠道', this.getPaymentChannelText(this.transaction.paymentChannel));
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Column.pop();
    }
    DetailItemView(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(308:5)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(309:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(314:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
    }
    /**
     * 获取交易类型图标
     */
    getTransactionIcon(type: number): string {
        switch (type) {
            case 1: // RECHARGE
                return '💰';
            case 2: // TRANSFER
                return '💸';
            case 3: // RECEIVE
                return '💵';
            case 4: // PAYMENT
                return '🛒';
            default:
                return '💳';
        }
    }
    /**
     * 获取状态颜色
     */
    getStatusColor(status: number): string {
        switch (status) {
            case 1: // SUCCESS
                return '#00C851';
            case 2: // FAILED
                return '#FF4444';
            case 0: // PENDING
                return '#FF8800';
            default:
                return '#666666';
        }
    }
    /**
     * 获取支付渠道文本
     */
    getPaymentChannelText(channel: number): string {
        switch (channel) {
            case 1:
                return '商户支付';
            case 2:
                return '扫码支付';
            case 3:
                return 'NFC支付';
            default:
                return '其他';
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionDetailPage";
    }
}
registerNamedRoute(() => new TransactionDetailPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/TransactionDetailPage", pageFullPath: "entry/src/main/ets/pages/TransactionDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
