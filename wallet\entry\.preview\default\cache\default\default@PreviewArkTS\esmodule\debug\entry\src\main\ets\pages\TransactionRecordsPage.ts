if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionRecordsPage_Params {
    userInfo?: LocalUserInfo | null;
    transactions?: Transaction[];
    isLoading?: boolean;
    currentPage?: number;
    pageSize?: number;
    totalPages?: number;
    totalRecords?: number;
    selectedType?: TransactionType | undefined;
    showFilterDialog?: boolean;
    searchKeyword?: string;
    refreshing?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { TransactionType, TransactionStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction, TransactionQueryParams, PageResult, LocalUserInfo, ApiError } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class TransactionRecordsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__pageSize = new ObservedPropertySimplePU(10, this, "pageSize");
        this.__totalPages = new ObservedPropertySimplePU(1, this, "totalPages");
        this.__totalRecords = new ObservedPropertySimplePU(0, this, "totalRecords");
        this.__selectedType = new ObservedPropertySimplePU(undefined, this, "selectedType");
        this.__showFilterDialog = new ObservedPropertySimplePU(false, this, "showFilterDialog");
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionRecordsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.totalPages !== undefined) {
            this.totalPages = params.totalPages;
        }
        if (params.totalRecords !== undefined) {
            this.totalRecords = params.totalRecords;
        }
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.showFilterDialog !== undefined) {
            this.showFilterDialog = params.showFilterDialog;
        }
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
    }
    updateStateVars(params: TransactionRecordsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__totalPages.purgeDependencyOnElmtId(rmElmtId);
        this.__totalRecords.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilterDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__totalPages.aboutToBeDeleted();
        this.__totalRecords.aboutToBeDeleted();
        this.__selectedType.aboutToBeDeleted();
        this.__showFilterDialog.aboutToBeDeleted();
        this.__searchKeyword.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __totalPages: ObservedPropertySimplePU<number>;
    get totalPages() {
        return this.__totalPages.get();
    }
    set totalPages(newValue: number) {
        this.__totalPages.set(newValue);
    }
    private __totalRecords: ObservedPropertySimplePU<number>;
    get totalRecords() {
        return this.__totalRecords.get();
    }
    set totalRecords(newValue: number) {
        this.__totalRecords.set(newValue);
    }
    private __selectedType: ObservedPropertySimplePU<TransactionType | undefined>;
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: TransactionType | undefined) {
        this.__selectedType.set(newValue);
    }
    private __showFilterDialog: ObservedPropertySimplePU<boolean>;
    get showFilterDialog() {
        return this.__showFilterDialog.get();
    }
    set showFilterDialog(newValue: boolean) {
        this.__showFilterDialog.set(newValue);
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    async aboutToAppear() {
        console.log('TransactionRecordsPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params && params.userId) {
            this.userInfo = {
                userId: params.userId,
                phone: '',
                username: '',
                token: '',
                loginTime: Date.now(),
                rememberLogin: false
            };
        }
        else {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载交易记录
        await this.loadTransactions();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载交易记录
     */
    async loadTransactions(reset: boolean = false) {
        if (!this.userInfo)
            return;
        if (reset) {
            this.currentPage = 1;
            this.transactions = [];
        }
        this.isLoading = true;
        try {
            const params: TransactionQueryParams = {
                userId: this.userInfo.userId,
                page: this.currentPage,
                size: this.pageSize,
                type: this.selectedType
            };
            let result: PageResult<Transaction>;
            if (this.searchKeyword.trim()) {
                // 搜索模式
                result = await TransactionApi.searchTransactions(this.userInfo.userId, this.searchKeyword.trim(), this.currentPage, this.pageSize);
            }
            else {
                // 普通列表模式
                result = await TransactionApi.getTransactionList(params);
            }
            if (reset) {
                this.transactions = result.records;
            }
            else {
                this.transactions = [...this.transactions, ...result.records];
            }
            this.totalPages = result.pages;
            this.totalRecords = result.total;
            console.log(`交易记录加载成功: ${result.records.length}条, 总计${result.total}条`);
        }
        catch (error) {
            console.error('加载交易记录失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '加载交易记录失败'
            });
        }
        finally {
            this.isLoading = false;
            this.refreshing = false;
        }
    }
    /**
     * 加载更多数据
     */
    async loadMore() {
        if (this.isLoading || this.currentPage >= this.totalPages) {
            return;
        }
        this.currentPage++;
        await this.loadTransactions(false);
    }
    /**
     * 刷新数据
     */
    async refreshData() {
        this.refreshing = true;
        await this.loadTransactions(true);
    }
    /**
     * 搜索交易记录
     */
    async searchTransactions() {
        await this.loadTransactions(true);
    }
    /**
     * 筛选交易类型
     */
    async filterByType(type?: TransactionType) {
        this.selectedType = type;
        this.showFilterDialog = false;
        await this.loadTransactions(true);
    }
    /**
     * 跳转到交易详情页面
     */
    navigateToTransactionDetail(txnId: number) {
        router.pushUrl({
            url: 'pages/TransactionDetailPage',
            params: {
                txnId: txnId
            }
        }).catch((error: Error) => {
            console.error('跳转交易详情页面失败:', error);
            promptAction.showToast({ message: '页面跳转失败' });
        });
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(194:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 搜索和筛选栏
        this.SearchFilterView.bind(this)();
        // 交易记录列表
        this.TransactionListView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 筛选弹窗
            if (this.showFilterDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.FilterDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(215:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(216:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(224:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(230:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`共${this.totalRecords}条`);
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(232:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Row.pop();
    }
    SearchFilterView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(244:5)", "entry");
            Row.width('100%');
            Row.padding({ horizontal: 16, vertical: 12 });
            Row.backgroundColor(Color.White);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 搜索框
            TextInput.create({ placeholder: '搜索交易记录...', text: this.searchKeyword });
            TextInput.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(246:7)", "entry");
            // 搜索框
            TextInput.layoutWeight(1);
            // 搜索框
            TextInput.height(40);
            // 搜索框
            TextInput.fontSize(14);
            // 搜索框
            TextInput.backgroundColor('#F8F8F8');
            // 搜索框
            TextInput.borderRadius(20);
            // 搜索框
            TextInput.padding({ horizontal: 16 });
            // 搜索框
            TextInput.onChange((value: string) => {
                this.searchKeyword = value;
            });
            // 搜索框
            TextInput.onSubmit(() => {
                this.searchTransactions();
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 筛选按钮
            Button.createWithLabel('筛选');
            Button.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(261:7)", "entry");
            // 筛选按钮
            Button.height(40);
            // 筛选按钮
            Button.fontSize(14);
            // 筛选按钮
            Button.fontColor('#007AFF');
            // 筛选按钮
            Button.backgroundColor('#F0F8FF');
            // 筛选按钮
            Button.borderRadius(20);
            // 筛选按钮
            Button.padding({ horizontal: 16 });
            // 筛选按钮
            Button.margin({ left: 12 });
            // 筛选按钮
            Button.onClick(() => {
                this.showFilterDialog = true;
            });
        }, Button);
        // 筛选按钮
        Button.pop();
        Row.pop();
    }
    TransactionListView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading && this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 首次加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(281:7)", "entry");
                        // 首次加载状态
                        Column.width('100%');
                        // 首次加载状态
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(282:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                        Text.margin({ top: 100 });
                    }, Text);
                    Text.pop();
                    // 首次加载状态
                    Column.pop();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 空状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(291:7)", "entry");
                        // 空状态
                        Column.width('100%');
                        // 空状态
                        Column.justifyContent(FlexAlign.Center);
                        // 空状态
                        Column.margin({ top: 100 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📝');
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(292:9)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(296:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#666666');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('完成交易后记录将显示在这里');
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(301:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                    }, Text);
                    Text.pop();
                    // 空状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 交易记录列表
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(310:7)", "entry");
                        // 交易记录列表
                        List.width('100%');
                        // 交易记录列表
                        List.layoutWeight(1);
                        // 交易记录列表
                        List.divider({ strokeWidth: 1, color: '#F0F0F0' });
                        // 交易记录列表
                        List.onReachEnd(() => {
                            // 滚动到底部时自动加载更多
                            if (!this.isLoading && this.currentPage < this.totalPages) {
                                this.loadMore();
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const transaction = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        this.navigateToTransactionDetail(transaction.txnId);
                                    });
                                    ListItem.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(312:11)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.TransactionItemView.bind(this)(transaction);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 加载更多指示器
                        if (this.currentPage < this.totalPages) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(322:11)", "entry");
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(323:13)", "entry");
                                            Row.width('100%');
                                            Row.height(50);
                                            Row.justifyContent(FlexAlign.Center);
                                            Row.onClick(() => {
                                                if (!this.isLoading) {
                                                    this.loadMore();
                                                }
                                            });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (this.isLoading) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create('加载中...');
                                                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(325:17)", "entry");
                                                        Text.fontSize(14);
                                                        Text.fontColor('#666666');
                                                    }, Text);
                                                    Text.pop();
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create('点击加载更多');
                                                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(329:17)", "entry");
                                                        Text.fontSize(14);
                                                        Text.fontColor('#007AFF');
                                                    }, Text);
                                                    Text.pop();
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        Row.pop();
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 交易记录列表
                    List.pop();
                });
            }
        }, If);
        If.pop();
    }
    TransactionItemView(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(358:5)", "entry");
            Row.width('100%');
            Row.padding({ horizontal: 16, vertical: 12 });
            Row.backgroundColor(Color.White);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Text.create(this.getTransactionIcon(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(360:7)", "entry");
            // 交易类型图标
            Text.fontSize(20);
            // 交易类型图标
            Text.width(40);
            // 交易类型图标
            Text.height(40);
            // 交易类型图标
            Text.textAlign(TextAlign.Center);
            // 交易类型图标
            Text.backgroundColor(this.getTransactionIconBg(transaction.type));
            // 交易类型图标
            Text.borderRadius(20);
            // 交易类型图标
            Text.margin({ right: 12 });
        }, Text);
        // 交易类型图标
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(370:7)", "entry");
            // 交易信息
            Column.layoutWeight(1);
            // 交易信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(371:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(TransactionApi.getTransactionTypeText(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(372:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(377:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(TransactionApi.formatAmount(transaction.amount, transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(379:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(TransactionApi.getAmountColor(transaction.type));
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(386:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatTransactionTime(transaction.createdAt));
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(387:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(391:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(TransactionApi.getTransactionStatusText(transaction.status));
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(393:11)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(transaction.status));
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (transaction.remark) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(transaction.remark);
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(401:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#666666');
                        Text.maxLines(1);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 交易信息
        Column.pop();
        Row.pop();
    }
    FilterDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(418:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(420:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showFilterDialog = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 筛选弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(429:7)", "entry");
            // 筛选弹窗
            Column.width('80%');
            // 筛选弹窗
            Column.padding(20);
            // 筛选弹窗
            Column.backgroundColor(Color.White);
            // 筛选弹窗
            Column.borderRadius(12);
            // 筛选弹窗
            Column.position({ x: '10%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('筛选交易类型');
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(430:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(435:9)", "entry");
            Column.width('100%');
        }, Column);
        this.FilterOptionView.bind(this)('全部', undefined);
        this.FilterOptionView.bind(this)('充值', TransactionType.RECHARGE);
        this.FilterOptionView.bind(this)('转账', TransactionType.TRANSFER);
        this.FilterOptionView.bind(this)('收款', TransactionType.RECEIVE);
        this.FilterOptionView.bind(this)('支付', TransactionType.PAYMENT);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(444:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(445:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showFilterDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(455:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('重置');
            Button.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(457:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#007AFF');
            Button.backgroundColor('#F0F8FF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.filterByType(undefined);
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 筛选弹窗
        Column.pop();
        Column.pop();
    }
    FilterOptionView(title: string, type?: TransactionType, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(482:5)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ horizontal: 16 });
            Row.onClick(() => {
                this.filterByType(type);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(483:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(487:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedType === type) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/TransactionRecordsPage.ets(490:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#007AFF');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    /**
     * 获取交易类型图标
     */
    getTransactionIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE:
                return '💰';
            case TransactionType.TRANSFER:
                return '💸';
            case TransactionType.RECEIVE:
                return '💵';
            case TransactionType.PAYMENT:
                return '🛒';
            default:
                return '💳';
        }
    }
    /**
     * 获取交易类型图标背景色
     */
    getTransactionIconBg(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE:
                return '#E8F5E8';
            case TransactionType.TRANSFER:
                return '#FFF0E6';
            case TransactionType.RECEIVE:
                return '#E8F5E8';
            case TransactionType.PAYMENT:
                return '#F0F8FF';
            default:
                return '#F5F5F5';
        }
    }
    /**
     * 获取状态颜色
     */
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS:
                return '#00C851';
            case TransactionStatus.FAILED:
                return '#FF4444';
            case TransactionStatus.PENDING:
                return '#FF8800';
            default:
                return '#666666';
        }
    }
    /**
     * 格式化交易时间显示
     */
    formatTransactionTime(timeStr: string): string {
        try {
            const date = new Date(timeStr);
            const now = new Date();
            const diff = now.getTime() - date.getTime();
            if (diff < 60000) { // 1分钟内
                return '刚刚';
            }
            else if (diff < 3600000) { // 1小时内
                return `${Math.floor(diff / 60000)}分钟前`;
            }
            else if (diff < 86400000) { // 24小时内
                return `${Math.floor(diff / 3600000)}小时前`;
            }
            else if (diff < 86400000 * 7) { // 7天内
                return `${Math.floor(diff / 86400000)}天前`;
            }
            else {
                return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
            }
        }
        catch (error) {
            return timeStr;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionRecordsPage";
    }
}
registerNamedRoute(() => new TransactionRecordsPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/TransactionRecordsPage", pageFullPath: "entry/src/main/ets/pages/TransactionRecordsPage", integratedHsp: "false", moduleType: "followWithHap" });
