if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WalletOperationPage_Params {
    userInfo?: LocalUserInfo | null;
    operationType?: string;
    amount?: string;
    selectedCard?: BankCard | null;
    bankCards?: BankCard[];
    payPassword?: string;
    remark?: string;
    recipientAccount?: string;
    recipientPhone?: string;
    isLoading?: boolean;
    showCardSelector?: boolean;
    showPayPasswordDialog?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo, BankCard, ApiError } from '../common/types/index';
class WalletOperationPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__operationType = new ObservedPropertySimplePU('recharge', this, "operationType");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__remark = new ObservedPropertySimplePU('', this, "remark");
        this.__recipientAccount = new ObservedPropertySimplePU('', this, "recipientAccount");
        this.__recipientPhone = new ObservedPropertySimplePU('', this, "recipientPhone");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.__showPayPasswordDialog = new ObservedPropertySimplePU(false, this, "showPayPasswordDialog");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WalletOperationPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.operationType !== undefined) {
            this.operationType = params.operationType;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.remark !== undefined) {
            this.remark = params.remark;
        }
        if (params.recipientAccount !== undefined) {
            this.recipientAccount = params.recipientAccount;
        }
        if (params.recipientPhone !== undefined) {
            this.recipientPhone = params.recipientPhone;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
        if (params.showPayPasswordDialog !== undefined) {
            this.showPayPasswordDialog = params.showPayPasswordDialog;
        }
    }
    updateStateVars(params: WalletOperationPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__operationType.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__remark.purgeDependencyOnElmtId(rmElmtId);
        this.__recipientAccount.purgeDependencyOnElmtId(rmElmtId);
        this.__recipientPhone.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
        this.__showPayPasswordDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__operationType.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__selectedCard.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__remark.aboutToBeDeleted();
        this.__recipientAccount.aboutToBeDeleted();
        this.__recipientPhone.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        this.__showPayPasswordDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __operationType: ObservedPropertySimplePU<string>; // recharge, withdraw, transfer, receive
    get operationType() {
        return this.__operationType.get();
    }
    set operationType(newValue: string) {
        this.__operationType.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>;
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __remark: ObservedPropertySimplePU<string>;
    get remark() {
        return this.__remark.get();
    }
    set remark(newValue: string) {
        this.__remark.set(newValue);
    }
    private __recipientAccount: ObservedPropertySimplePU<string>; // 转账收款账号
    get recipientAccount() {
        return this.__recipientAccount.get();
    }
    set recipientAccount(newValue: string) {
        this.__recipientAccount.set(newValue);
    }
    private __recipientPhone: ObservedPropertySimplePU<string>; // 转账收款手机号
    get recipientPhone() {
        return this.__recipientPhone.get();
    }
    set recipientPhone(newValue: string) {
        this.__recipientPhone.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    private __showPayPasswordDialog: ObservedPropertySimplePU<boolean>;
    get showPayPasswordDialog() {
        return this.__showPayPasswordDialog.get();
    }
    set showPayPasswordDialog(newValue: boolean) {
        this.__showPayPasswordDialog.set(newValue);
    }
    async aboutToAppear() {
        console.log('WalletOperationPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, any>;
        if (params) {
            this.operationType = params.operationType || 'recharge';
            if (params.userId) {
                this.userInfo = {
                    userId: params.userId,
                    phone: '',
                    username: '',
                    token: '',
                    loginTime: Date.now(),
                    rememberLogin: false
                };
            }
        }
        if (!this.userInfo) {
            // 从存储中获取用户信息
            await this.loadUserInfo();
        }
        // 加载银行卡列表
        await this.loadBankCards();
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            await storageManager.init();
            const userInfo = await storageManager.getUserInfo();
            if (userInfo) {
                this.userInfo = userInfo;
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载银行卡列表
     */
    async loadBankCards() {
        if (!this.userInfo)
            return;
        try {
            const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
            this.bankCards = bankCards;
            // 自动选择默认银行卡
            const defaultCard = bankCards.find(card => card.isDefault);
            if (defaultCard) {
                this.selectedCard = defaultCard;
            }
            else if (bankCards.length > 0) {
                this.selectedCard = bankCards[0];
            }
            console.log(`银行卡列表加载成功: ${bankCards.length}张`);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
        }
    }
    /**
     * 执行钱包操作
     */
    async executeOperation() {
        if (!this.userInfo || !this.validateForm()) {
            return;
        }
        this.showPayPasswordDialog = true;
    }
    /**
     * 确认执行操作
     */
    async confirmOperation() {
        if (!this.userInfo || !this.payPassword) {
            promptAction.showToast({ message: '请输入支付密码' });
            return;
        }
        this.isLoading = true;
        this.showPayPasswordDialog = false;
        try {
            const amountValue = parseFloat(this.amount);
            switch (this.operationType) {
                case 'recharge':
                    await this.handleRecharge(amountValue);
                    break;
                case 'withdraw':
                    await this.handleWithdraw(amountValue);
                    break;
                case 'transfer':
                    await this.handleTransfer(amountValue);
                    break;
                case 'receive':
                    await this.handleReceive(amountValue);
                    break;
            }
            promptAction.showToast({ message: '操作成功' });
            // 清空表单
            this.resetForm();
            // 延迟返回上一页
            setTimeout(() => {
                router.back();
            }, 1500);
        }
        catch (error) {
            console.error('钱包操作失败:', error);
            const apiError = error as ApiError;
            promptAction.showToast({
                message: apiError.message || '操作失败'
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 处理充值
     */
    async handleRecharge(amount: number) {
        if (!this.userInfo || !this.selectedCard)
            return;
        await WalletApi.recharge(this.userInfo.userId, amount, this.selectedCard.cardId, this.payPassword, this.remark || '钱包充值');
    }
    /**
     * 处理提现
     */
    async handleWithdraw(amount: number) {
        if (!this.userInfo || !this.selectedCard)
            return;
        await WalletApi.withdraw(this.userInfo.userId, amount, this.selectedCard.cardId, this.payPassword, this.remark || '钱包提现');
    }
    /**
     * 处理转账
     */
    async handleTransfer(amount: number) {
        if (!this.userInfo)
            return;
        if (this.recipientPhone) {
            // 通过手机号转账
            await WalletApi.transferByPhone(this.userInfo.userId, this.recipientPhone, amount, this.payPassword, this.remark || '钱包转账');
        }
        else if (this.recipientAccount) {
            // 通过账号转账
            await WalletApi.transfer(this.userInfo.userId, this.recipientAccount, amount, this.payPassword, this.remark || '钱包转账');
        }
    }
    /**
     * 处理收款
     */
    async handleReceive(amount: number) {
        if (!this.userInfo)
            return;
        // 生成收款码或处理收款逻辑
        await WalletApi.generateReceiveQR(this.userInfo.userId, amount, this.remark || '收款');
    }
    /**
     * 表单验证
     */
    validateForm(): boolean {
        if (!this.amount || parseFloat(this.amount) <= 0) {
            promptAction.showToast({ message: '请输入正确的金额' });
            return false;
        }
        if ((this.operationType === 'recharge' || this.operationType === 'withdraw') && !this.selectedCard) {
            promptAction.showToast({ message: '请选择银行卡' });
            return false;
        }
        if (this.operationType === 'transfer' && !this.recipientAccount && !this.recipientPhone) {
            promptAction.showToast({ message: '请输入收款账号或手机号' });
            return false;
        }
        return true;
    }
    /**
     * 重置表单
     */
    resetForm() {
        this.amount = '';
        this.payPassword = '';
        this.remark = '';
        this.recipientAccount = '';
        this.recipientPhone = '';
    }
    /**
     * 选择银行卡
     */
    selectCard(card: BankCard) {
        this.selectedCard = card;
        this.showCardSelector = false;
    }
    /**
     * 返回上一页
     */
    goBack() {
        router.back();
    }
    /**
     * 获取操作标题
     */
    getOperationTitle(): string {
        switch (this.operationType) {
            case 'recharge':
                return '钱包充值';
            case 'withdraw':
                return '钱包提现';
            case 'transfer':
                return '钱包转账';
            case 'receive':
                return '收款';
            default:
                return '钱包操作';
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(303:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 操作表单
        this.OperationFormView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择弹窗
            if (this.showCardSelector) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.CardSelectorDialog.bind(this)();
                });
            }
            // 支付密码弹窗
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 支付密码弹窗
            if (this.showPayPasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PayPasswordDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(326:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ horizontal: 16 });
            Row.backgroundColor(Color.White);
            Row.border({ width: { bottom: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(327:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
            Image.onClick(() => {
                this.goBack();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getOperationTitle());
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(335:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ left: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(341:7)", "entry");
        }, Blank);
        Blank.pop();
        Row.pop();
    }
    OperationFormView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(351:5)", "entry");
            Scroll.width('100%');
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(352:7)", "entry");
            Column.width('100%');
            Column.padding({ horizontal: 16, vertical: 20 });
        }, Column);
        // 金额输入
        this.AmountInputView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择（充值和提现需要）
            if (this.operationType === 'recharge' || this.operationType === 'withdraw') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.BankCardSelectorView.bind(this)();
                });
            }
            // 收款信息（转账需要）
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 收款信息（转账需要）
            if (this.operationType === 'transfer') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.RecipientInfoView.bind(this)();
                });
            }
            // 备注输入
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 备注输入
        this.RemarkInputView.bind(this)();
        // 确认按钮
        this.ConfirmButtonView.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    AmountInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(382:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('金额');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(383:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(390:7)", "entry");
            Row.width('100%');
            Row.padding({ horizontal: 16, vertical: 20 });
            Row.backgroundColor(Color.White);
            Row.borderRadius(12);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('¥');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(391:9)", "entry");
            Text.fontSize(24);
            Text.fontColor('#333333');
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '0.00', text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(396:9)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(24);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        Row.pop();
        Column.pop();
    }
    BankCardSelectorView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(417:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.operationType === 'recharge' ? '充值到银行卡' : '提现到银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(418:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(425:7)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(Color.White);
            Row.borderRadius(12);
            Row.onClick(() => {
                this.showCardSelector = true;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(427:11)", "entry");
                        Column.alignItems(HorizontalAlign.Start);
                        Column.layoutWeight(1);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCard.bankName);
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(428:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(BankCardApi.formatCardNumber(this.selectedCard.cardNumber));
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(433:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请选择银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(442:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.myapplication", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(448:9)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#CCCCCC');
        }, Image);
        Row.pop();
        Column.pop();
    }
    RecipientInfoView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(466:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收款信息');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(467:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(474:7)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收款手机号', text: this.recipientPhone });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(475:9)", "entry");
            TextInput.type(InputType.PhoneNumber);
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.margin({ bottom: 12 });
            TextInput.onChange((value: string) => {
                this.recipientPhone = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('或');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(486:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Center);
            Text.margin({ vertical: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收款账号', text: this.recipientAccount });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(492:9)", "entry");
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.recipientAccount = value;
            });
        }, TextInput);
        Column.pop();
        Column.pop();
    }
    RemarkInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(511:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('备注（可选）');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(512:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入备注信息', text: this.remark });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(519:7)", "entry");
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor(Color.White);
            TextInput.borderRadius(12);
            TextInput.onChange((value: string) => {
                this.remark = value;
            });
        }, TextInput);
        Column.pop();
    }
    ConfirmButtonView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '处理中...' : '确认操作');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(533:5)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.executeOperation();
            });
        }, Button);
        Button.pop();
    }
    CardSelectorDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(547:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(549:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showCardSelector = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(558:7)", "entry");
            // 银行卡选择弹窗
            Column.width('80%');
            // 银行卡选择弹窗
            Column.padding(20);
            // 银行卡选择弹窗
            Column.backgroundColor(Color.White);
            // 银行卡选择弹窗
            Column.borderRadius(12);
            // 银行卡选择弹窗
            Column.position({ x: '10%', y: '30%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(559:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCards.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(565:11)", "entry");
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(567:15)", "entry");
                                Row.width('100%');
                                Row.padding(16);
                                Row.onClick(() => {
                                    this.selectCard(card);
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Column.create();
                                Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(568:17)", "entry");
                                Column.alignItems(HorizontalAlign.Start);
                                Column.layoutWeight(1);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(card.bankName);
                                Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(569:19)", "entry");
                                Text.fontSize(16);
                                Text.fontColor('#333333');
                                Text.alignSelf(ItemAlign.Start);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(BankCardApi.formatCardNumber(card.cardNumber));
                                Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(574:19)", "entry");
                                Text.fontSize(14);
                                Text.fontColor('#666666');
                                Text.alignSelf(ItemAlign.Start);
                                Text.margin({ top: 4 });
                            }, Text);
                            Text.pop();
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (this.selectedCard?.cardId === card.cardId) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('✓');
                                            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(584:19)", "entry");
                                            Text.fontSize(16);
                                            Text.fontColor('#007AFF');
                                        }, Text);
                                        Text.pop();
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                    });
                                }
                            }, If);
                            If.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(598:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ vertical: 20 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(604:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.showCardSelector = false;
            });
        }, Button);
        Button.pop();
        // 银行卡选择弹窗
        Column.pop();
        Column.pop();
    }
    PayPasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(627:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.position({ x: 0, y: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(629:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0, 0, 0, 0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码弹窗
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(639:7)", "entry");
            // 支付密码弹窗
            Column.width('80%');
            // 支付密码弹窗
            Column.padding(20);
            // 支付密码弹窗
            Column.backgroundColor(Color.White);
            // 支付密码弹窗
            Column.borderRadius(12);
            // 支付密码弹窗
            Column.position({ x: '10%', y: '35%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请输入支付密码');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(640:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位支付密码', text: this.payPassword });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(645:9)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.padding(16);
            TextInput.backgroundColor('#F8F8F8');
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(656:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(657:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.onClick(() => {
                this.showPayPasswordDialog = false;
                this.payPassword = '';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(668:11)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '处理中...' : '确认');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(670:11)", "entry");
            Button.fontSize(14);
            Button.fontColor(Color.White);
            Button.backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF');
            Button.borderRadius(6);
            Button.padding({ horizontal: 20, vertical: 8 });
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.confirmOperation();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 支付密码弹窗
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WalletOperationPage";
    }
}
registerNamedRoute(() => new WalletOperationPage(undefined, {}), "", { bundleName: "com.icss.myapplication", moduleName: "entry", pagePath: "pages/WalletOperationPage", pageFullPath: "entry/src/main/ets/pages/WalletOperationPage", integratedHsp: "false", moduleType: "followWithHap" });
