/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ENTRYABILITY_DESC = 0x01000005;
const int32_t STRING_ENTRYABILITY_LABEL = 0x01000006;
const int32_t STRING_APP_NAME = 0x01000002;
const int32_t STRING_INTERNET_PERMISSION_REASON = 0x0100000c;
const int32_t STRING_MODULE_DESC = 0x01000007;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x01000008;
const int32_t FLOAT_PAGE_TEXT_FONT_SIZE = 0x0100000b;
const int32_t MEDIA_BACKGROUND = 0x01000003;
const int32_t MEDIA_FOREGROUND = 0x01000001;
const int32_t MEDIA_ICON = 0x0100000d;
const int32_t MEDIA_LAYERED_IMAGE = 0x01000000;
const int32_t MEDIA_STARTICON = 0x0100000a;
const int32_t PROFILE_BACKUP_CONFIG = 0x01000004;
const int32_t PROFILE_MAIN_PAGES = 0x01000009;
}
#endif