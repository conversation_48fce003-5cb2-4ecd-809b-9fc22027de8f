{"app": {"bundleName": "com.icss.myapplication", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:layered_image", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50003015, "minAPIVersion": 50003015, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:internet_permission_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}], "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config"}]}], "packageName": "entry"}}