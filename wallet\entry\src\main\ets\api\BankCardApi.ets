import { httpClient } from '../common/http/HttpClient';
import { BankCard, BankCardType, BankCardStatus, ApiResponse } from '../common/types/index';

/**
 * 银行卡相关API服务
 */
export class BankCardApi {
  
  /**
   * 获取用户银行卡列表
   * @param userId 用户ID
   */
  public static async getCardList(userId: number): Promise<BankCard[]> {
    try {
      const response = await httpClient.get<any[]>(`/bank-card/user/${userId}`);
      
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(card => this.convertToBankCard(card));
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取银行卡列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取已绑定的银行卡列表
   * @param userId 用户ID
   */
  public static async getBoundCards(userId: number): Promise<BankCard[]> {
    try {
      const response = await httpClient.get<any[]>(`/bank-card/bound-cards/${userId}`);
      
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(card => this.convertToBankCard(card));
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取已绑定银行卡列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取银行卡详情
   * @param cardId 银行卡ID
   */
  public static async getCardDetails(cardId: number): Promise<BankCard> {
    try {
      const response = await httpClient.get<any>(`/bank-card/${cardId}`);
      
      if (response.data) {
        return this.convertToBankCard(response.data);
      } else {
        throw new Error('获取银行卡详情响应数据为空');
      }
    } catch (error) {
      console.error('获取银行卡详情失败:', error);
      throw error;
    }
  }

  /**
   * 添加银行卡
   * @param bankCard 银行卡信息
   */
  public static async addBankCard(bankCard: Partial<BankCard>): Promise<BankCard> {
    try {
      const requestData = {
        userId: bankCard.userId,
        bankName: bankCard.bankName,
        cardNumber: bankCard.cardNumber,
        cardType: bankCard.cardType,
        cardHolder: bankCard.cardHolder,
        balance: bankCard.balance || 0,
        expiryDate: bankCard.expiryDate,
        cvv: bankCard.cvv,
        phone: bankCard.phone,
        isDefault: bankCard.isDefault ? 1 : 0,
        status: bankCard.status || BankCardStatus.NORMAL
      };

      const response = await httpClient.post<any>('/bank-card', requestData);
      
      if (response.data) {
        return this.convertToBankCard(response.data);
      } else {
        throw new Error('添加银行卡响应数据为空');
      }
    } catch (error) {
      console.error('添加银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 绑定银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async bindCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.put<number>(`/bank-card/bind/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('绑定银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 解绑银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async unbindCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.put<number>(`/bank-card/unbind/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 删除银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async deleteCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.delete<number>(`/bank-card/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('删除银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 设置默认银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async setDefaultCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.put<string>(`/bank-card/set-default/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('设置默认银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认银行卡
   * @param userId 用户ID
   */
  public static async getDefaultCard(userId: number): Promise<BankCard | null> {
    try {
      const response = await httpClient.get<any>(`/bank-card/default/${userId}`);
      
      if (response.data) {
        return this.convertToBankCard(response.data);
      } else {
        return null;
      }
    } catch (error) {
      console.error('获取默认银行卡失败:', error);
      return null;
    }
  }

  /**
   * 获取信用卡列表
   * @param userId 用户ID
   */
  public static async getCreditCards(userId: number): Promise<BankCard[]> {
    try {
      const response = await httpClient.get<any[]>(`/bank-card/credit-cards/${userId}`);
      
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(card => this.convertToBankCard(card));
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取信用卡列表失败:', error);
      throw error;
    }
  }

  /**
   * 验证银行卡
   * @param bankCard 银行卡信息
   */
  public static async validateBankCard(bankCard: Partial<BankCard>): Promise<boolean> {
    try {
      const response = await httpClient.post<boolean>('/bank-card/validate', bankCard);
      return response.data || false;
    } catch (error) {
      console.error('验证银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 发送验证码
   * @param cardId 银行卡ID
   * @param phone 手机号
   */
  public static async sendVerificationCode(cardId: number, phone: string): Promise<boolean> {
    try {
      const response = await httpClient.post<boolean>(`/bank-card/send-verification/${cardId}?phone=${phone}`);
      return response.data || false;
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  }

  /**
   * 激活银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async activateCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.put<string>(`/bank-card/activate/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('激活银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 停用银行卡
   * @param userId 用户ID
   * @param cardId 银行卡ID
   */
  public static async deactivateCard(userId: number, cardId: number): Promise<void> {
    try {
      await httpClient.put<string>(`/bank-card/deactivate/${cardId}?userId=${userId}`);
    } catch (error) {
      console.error('停用银行卡失败:', error);
      throw error;
    }
  }

  /**
   * 检查银行卡是否可操作
   * @param cardId 银行卡ID
   */
  public static async isCardOperational(cardId: number): Promise<boolean> {
    try {
      const response = await httpClient.get<boolean>(`/bank-card/operational/${cardId}`);
      return response.data || false;
    } catch (error) {
      console.error('检查银行卡状态失败:', error);
      return false;
    }
  }

  /**
   * 转换后端数据为本地BankCard格式
   * @param data 后端返回的银行卡数据
   */
  private static convertToBankCard(data: any): BankCard {
    return {
      cardId: data.cardId || 0,
      userId: data.userId || 0,
      bankName: data.bankName || '',
      cardNumber: data.cardNumber || '',
      cardType: data.cardType || BankCardType.DEBIT,
      cardHolder: data.cardHolder || '',
      balance: data.balance || 0,
      expiryDate: data.expiryDate,
      cvv: data.cvv,
      phone: data.phone,
      isDefault: data.isDefault === 1 || data.isDefault === true,
      status: data.status || BankCardStatus.NORMAL,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    };
  }

  /**
   * 格式化银行卡号显示
   * @param cardNumber 银行卡号
   */
  public static formatCardNumber(cardNumber: string): string {
    if (!cardNumber || cardNumber.length < 8) {
      return cardNumber;
    }
    
    const firstFour = cardNumber.substring(0, 4);
    const lastFour = cardNumber.substring(cardNumber.length - 4);
    return `${firstFour} **** **** ${lastFour}`;
  }

  /**
   * 获取银行卡类型显示文本
   * @param cardType 银行卡类型
   */
  public static getCardTypeText(cardType: BankCardType): string {
    switch (cardType) {
      case BankCardType.DEBIT:
        return '借记卡';
      case BankCardType.CREDIT:
        return '信用卡';
      default:
        return '未知';
    }
  }

  /**
   * 获取银行卡状态显示文本
   * @param status 银行卡状态
   */
  public static getCardStatusText(status: BankCardStatus): string {
    switch (status) {
      case BankCardStatus.NORMAL:
        return '正常';
      case BankCardStatus.FROZEN:
        return '冻结';
      default:
        return '未知';
    }
  }
}
