import preferences from '@ohos.data.preferences';
import { LocalUserInfo, LocalWalletInfo, NetworkConfig } from '../types/index';

/**
 * 本地存储管理器
 * 统一管理应用的本地数据存储
 */
export class StorageManager {
  private static instance: StorageManager;
  private preferences: preferences.Preferences | null = null;
  private readonly STORE_NAME = 'wallet_app_store';

  // 存储键名常量
  private readonly KEYS = {
    USER_INFO: 'user_info',
    WALLET_INFO: 'wallet_info',
    AUTH_TOKEN: 'auth_token',
    LOGIN_PHONE: 'login_phone',
    NETWORK_CONFIG: 'network_config',
    REMEMBER_LOGIN: 'remember_login',
    LOGIN_TIME: 'login_time'
  };

  private constructor() {}

  public static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * 初始化存储
   */
  public async init(): Promise<void> {
    try {
      this.preferences = await preferences.getPreferences(getContext(), this.STORE_NAME);
      console.log('StorageManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize StorageManager:', error);
      throw error;
    }
  }

  /**
   * 确保存储已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.preferences) {
      await this.init();
    }
  }

  /**
   * 保存用户信息
   */
  public async saveUserInfo(userInfo: LocalUserInfo): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.put(this.KEYS.USER_INFO, JSON.stringify(userInfo));
      await this.preferences!.flush();
      console.log('User info saved successfully');
    } catch (error) {
      console.error('Failed to save user info:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  public async getUserInfo(): Promise<LocalUserInfo | null> {
    await this.ensureInitialized();
    try {
      const userInfoStr = await this.preferences!.get(this.KEYS.USER_INFO, '') as string;
      if (userInfoStr) {
        return JSON.parse(userInfoStr) as LocalUserInfo;
      }
      return null;
    } catch (error) {
      console.error('Failed to get user info:', error);
      return null;
    }
  }

  /**
   * 保存钱包信息
   */
  public async saveWalletInfo(walletInfo: LocalWalletInfo): Promise<void> {
    await this.ensureInitialized();
    try {
      const walletData = {
        ...walletInfo,
        lastUpdateTime: Date.now()
      };
      await this.preferences!.put(this.KEYS.WALLET_INFO, JSON.stringify(walletData));
      await this.preferences!.flush();
      console.log('Wallet info saved successfully');
    } catch (error) {
      console.error('Failed to save wallet info:', error);
      throw error;
    }
  }

  /**
   * 获取钱包信息
   */
  public async getWalletInfo(): Promise<LocalWalletInfo | null> {
    await this.ensureInitialized();
    try {
      const walletInfoStr = await this.preferences!.get(this.KEYS.WALLET_INFO, '') as string;
      if (walletInfoStr) {
        return JSON.parse(walletInfoStr) as LocalWalletInfo;
      }
      return null;
    } catch (error) {
      console.error('Failed to get wallet info:', error);
      return null;
    }
  }

  /**
   * 保存认证Token
   */
  public async saveUserToken(token: string): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.put(this.KEYS.AUTH_TOKEN, token);
      await this.preferences!.flush();
      console.log('Auth token saved successfully');
    } catch (error) {
      console.error('Failed to save auth token:', error);
      throw error;
    }
  }

  /**
   * 获取认证Token
   */
  public async getUserToken(): Promise<string | null> {
    await this.ensureInitialized();
    try {
      const token = await this.preferences!.get(this.KEYS.AUTH_TOKEN, '') as string;
      return token || null;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  /**
   * 保存登录手机号
   */
  public async saveLoginPhone(phone: string): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.put(this.KEYS.LOGIN_PHONE, phone);
      await this.preferences!.flush();
      console.log('Login phone saved successfully');
    } catch (error) {
      console.error('Failed to save login phone:', error);
      throw error;
    }
  }

  /**
   * 获取登录手机号
   */
  public async getLoginPhone(): Promise<string | null> {
    await this.ensureInitialized();
    try {
      const phone = await this.preferences!.get(this.KEYS.LOGIN_PHONE, '') as string;
      return phone || null;
    } catch (error) {
      console.error('Failed to get login phone:', error);
      return null;
    }
  }

  /**
   * 保存网络配置
   */
  public async saveNetworkConfig(config: NetworkConfig): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.put(this.KEYS.NETWORK_CONFIG, JSON.stringify(config));
      await this.preferences!.flush();
      console.log('Network config saved successfully');
    } catch (error) {
      console.error('Failed to save network config:', error);
      throw error;
    }
  }

  /**
   * 获取网络配置
   */
  public async getNetworkConfig(): Promise<NetworkConfig | null> {
    await this.ensureInitialized();
    try {
      const configStr = await this.preferences!.get(this.KEYS.NETWORK_CONFIG, '') as string;
      if (configStr) {
        return JSON.parse(configStr) as NetworkConfig;
      }
      return null;
    } catch (error) {
      console.error('Failed to get network config:', error);
      return null;
    }
  }

  /**
   * 保存记住登录状态
   */
  public async saveRememberLogin(remember: boolean): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.put(this.KEYS.REMEMBER_LOGIN, remember);
      if (remember) {
        await this.preferences!.put(this.KEYS.LOGIN_TIME, Date.now());
      }
      await this.preferences!.flush();
      console.log('Remember login status saved successfully');
    } catch (error) {
      console.error('Failed to save remember login status:', error);
      throw error;
    }
  }

  /**
   * 获取记住登录状态
   */
  public async getRememberLogin(): Promise<boolean> {
    await this.ensureInitialized();
    try {
      const remember = await this.preferences!.get(this.KEYS.REMEMBER_LOGIN, false) as boolean;
      if (remember) {
        // 检查是否超过7天
        const loginTime = await this.preferences!.get(this.KEYS.LOGIN_TIME, 0) as number;
        const now = Date.now();
        const sevenDays = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数
        
        if (now - loginTime > sevenDays) {
          // 超过7天，清除记住登录状态
          await this.saveRememberLogin(false);
          return false;
        }
      }
      return remember;
    } catch (error) {
      console.error('Failed to get remember login status:', error);
      return false;
    }
  }

  /**
   * 清除所有用户数据
   */
  public async clearUserData(): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.delete(this.KEYS.USER_INFO);
      await this.preferences!.delete(this.KEYS.WALLET_INFO);
      await this.preferences!.delete(this.KEYS.AUTH_TOKEN);
      await this.preferences!.delete(this.KEYS.REMEMBER_LOGIN);
      await this.preferences!.delete(this.KEYS.LOGIN_TIME);
      await this.preferences!.flush();
      console.log('User data cleared successfully');
    } catch (error) {
      console.error('Failed to clear user data:', error);
      throw error;
    }
  }

  /**
   * 清除所有数据
   */
  public async clearAll(): Promise<void> {
    await this.ensureInitialized();
    try {
      await this.preferences!.clear();
      await this.preferences!.flush();
      console.log('All data cleared successfully');
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const storageManager = StorageManager.getInstance();
