/**
 * 通用类型定义文件
 * 统一管理所有接口和数据模型的类型定义
 */

// ==================== 基础类型 ====================

/**
 * API响应基础接口 - 匹配SpringBoot3后端R类格式
 */
export interface ApiResponse<T> {
  code: number;  // 0-成功，其他-失败
  msg: string;   // 消息
  data: T;       // 数据
}

/**
 * 分页查询参数基础接口
 */
export interface BasePageParams {
  page?: number;
  size?: number;
}

/**
 * 分页结果基础接口
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 操作结果枚举
 */
export enum OperationResult {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending'
}

/**
 * 加载状态枚举
 */
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// ==================== 用户相关类型 ====================

/**
 * 用户信息接口
 */
export interface UserInfo {
  userId: number;
  phone: string;
  username?: string;
  status: number;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  userId: number;
  phone: string;
  username?: string;
  status: number;
  lastLogin?: string;
}

/**
 * 登录请求接口
 */
export interface LoginRequest {
  phone: string;
  loginType: 'password' | 'sms';
  password?: string;
  verificationCode?: string;
}

// ==================== 钱包相关类型 ====================

/**
 * 钱包信息接口
 */
export interface WalletInfo {
  accountId: number;
  userId: number;
  balance: number;
  dailyLimit: number;
  singleLimit: number;
  monthlyLimit: number;
  status: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 钱包操作类型枚举
 */
export enum WalletOperationType {
  RECHARGE = 'recharge',    // 充值
  WITHDRAW = 'withdraw',    // 提现
  TRANSFER = 'transfer',    // 转账
  RECEIVE = 'receive'       // 收钱
}

// ==================== 银行卡相关类型 ====================

/**
 * 银行卡信息接口
 */
export interface BankCard {
  cardId: number;
  userId: number;
  bankName: string;
  cardNumber: string;
  cardType: BankCardType;
  cardHolder: string;
  balance: number;
  expiryDate?: string;
  cvv?: string;
  phone?: string;
  isDefault: boolean;
  status: BankCardStatus;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 银行卡类型枚举
 */
export enum BankCardType {
  DEBIT = 1,    // 借记卡
  CREDIT = 2    // 信用卡
}

/**
 * 银行卡状态枚举
 */
export enum BankCardStatus {
  FROZEN = 0,   // 冻结
  NORMAL = 1    // 正常
}

// ==================== 交易相关类型 ====================

/**
 * 交易记录接口
 */
export interface Transaction {
  txnId: number;
  txnNo: string;
  userId: number;
  accountId: number;
  type: TransactionType;
  amount: number;
  balance: number;
  counterparty?: string;
  counterpartyPhone?: string;
  merchantId?: number;
  cardId?: number;
  paymentMethod?: PaymentMethod;
  paymentChannel?: PaymentChannel;
  status: TransactionStatus;
  remark?: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * 交易类型枚举
 */
export enum TransactionType {
  RECHARGE = 1,   // 充值
  TRANSFER = 2,   // 转账
  RECEIVE = 3,    // 收款
  PAYMENT = 4     // 消费
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  WALLET = 1,     // 钱包支付
  BANK_CARD = 2   // 银行卡支付
}

/**
 * 支付渠道枚举
 */
export enum PaymentChannel {
  MERCHANT = 1,   // 商户付款
  QR_CODE = 2,    // 扫码付款
  NFC = 3         // NFC支付
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  PENDING = 0,    // 处理中
  SUCCESS = 1,    // 成功
  FAILED = 2      // 失败
}

/**
 * 交易查询参数接口
 */
export interface TransactionQueryParams extends BasePageParams {
  userId: number;
  type?: TransactionType;
  status?: TransactionStatus;
  startDate?: string;
  endDate?: string;
}

// ==================== 商户相关类型 ====================

/**
 * 商户信息接口
 */
export interface Merchant {
  merchantId: number;
  merchantName: string;
  merchantNo: string;
  category: number;
  contactPhone: string;
  address?: string;
  status: number;
  createdAt?: string;
  updatedAt?: string;
}

// ==================== 错误处理类型 ====================

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * API错误接口
 */
export interface ApiError {
  type: ErrorType;
  code: number;
  message: string;
  details?: string;
}

// ==================== 本地存储类型 ====================

/**
 * 本地用户信息接口
 */
export interface LocalUserInfo {
  userId: number;
  phone: string;
  username?: string;
  token?: string;
  loginTime?: number;
  rememberLogin?: boolean;
}

/**
 * 本地钱包信息接口
 */
export interface LocalWalletInfo {
  accountId: number;
  balance: number;
  dailyLimit: number;
  singleLimit: number;
  monthlyLimit: number;
  lastUpdateTime?: number;
}

// ==================== 网络配置类型 ====================

/**
 * 网络配置接口
 */
export interface NetworkConfig {
  baseUrl: string;
  timeout: number;
  retryCount: number;
  environment: 'development' | 'production';
}
