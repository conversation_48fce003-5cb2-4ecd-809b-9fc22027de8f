import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  BankCard, 
  BankCardType, 
  BankCardStatus, 
  LocalUserInfo,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct BankCardPage {
  @State userInfo: LocalUserInfo | null = null;
  @State bankCards: BankCard[] = [];
  @State isLoading: boolean = false;
  @State showAddCardDialog: boolean = false;
  @State showCardDetailDialog: boolean = false;
  @State selectedCard: BankCard | null = null;
  @State refreshing: boolean = false;

  // 添加银行卡表单数据
  @State newCard: Partial<BankCard> = {
    bankName: '',
    cardNumber: '',
    cardType: BankCardType.DEBIT,
    cardHolder: '',
    balance: 0,
    expiryDate: '',
    cvv: '',
    phone: '',
    isDefault: false
  };

  async aboutToAppear() {
    console.log('BankCardPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params && params.userId) {
      this.userInfo = {
        userId: params.userId,
        phone: '',
        username: '',
        token: '',
        loginTime: Date.now(),
        rememberLogin: false
      };
    } else {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载银行卡列表
    await this.loadBankCards();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载银行卡列表
   */
  async loadBankCards() {
    if (!this.userInfo) return;
    
    this.isLoading = true;
    
    try {
      const bankCards = await BankCardApi.getCardList(this.userInfo.userId);
      this.bankCards = bankCards;
      console.log(`银行卡列表加载成功: ${bankCards.length}张`);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '加载银行卡列表失败' 
      });
    } finally {
      this.isLoading = false;
      this.refreshing = false;
    }
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    this.refreshing = true;
    await this.loadBankCards();
  }

  /**
   * 添加银行卡
   */
  async addBankCard() {
    if (!this.userInfo) return;
    
    // 表单验证
    if (!this.newCard.bankName || !this.newCard.cardNumber || !this.newCard.cardHolder) {
      promptAction.showToast({ message: '请填写完整的银行卡信息' });
      return;
    }
    
    if (this.newCard.cardNumber!.length < 16) {
      promptAction.showToast({ message: '银行卡号长度不正确' });
      return;
    }
    
    this.isLoading = true;
    
    try {
      const cardData: Partial<BankCard> = {
        ...this.newCard,
        userId: this.userInfo.userId,
        status: BankCardStatus.NORMAL
      };
      
      const addedCard = await BankCardApi.addBankCard(cardData);
      
      // 添加到本地列表
      this.bankCards = [...this.bankCards, addedCard];
      
      // 重置表单
      this.resetNewCardForm();
      this.showAddCardDialog = false;
      
      promptAction.showToast({ message: '银行卡添加成功' });
      
    } catch (error) {
      console.error('添加银行卡失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '添加银行卡失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 绑定银行卡
   */
  async bindCard(card: BankCard) {
    if (!this.userInfo) return;
    
    try {
      await BankCardApi.bindCard(this.userInfo.userId, card.cardId);
      
      // 更新本地状态
      const index = this.bankCards.findIndex(c => c.cardId === card.cardId);
      if (index !== -1) {
        this.bankCards[index] = { ...this.bankCards[index] };
      }
      
      promptAction.showToast({ message: '银行卡绑定成功' });
      await this.loadBankCards(); // 重新加载列表
      
    } catch (error) {
      console.error('绑定银行卡失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '绑定银行卡失败' 
      });
    }
  }

  /**
   * 解绑银行卡
   */
  async unbindCard(card: BankCard) {
    if (!this.userInfo) return;
    
    try {
      await BankCardApi.unbindCard(this.userInfo.userId, card.cardId);
      
      promptAction.showToast({ message: '银行卡解绑成功' });
      await this.loadBankCards(); // 重新加载列表
      
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '解绑银行卡失败' 
      });
    }
  }

  /**
   * 删除银行卡
   */
  async deleteCard(card: BankCard) {
    if (!this.userInfo) return;
    
    try {
      await BankCardApi.deleteCard(this.userInfo.userId, card.cardId);
      
      // 从本地列表中移除
      this.bankCards = this.bankCards.filter(c => c.cardId !== card.cardId);
      
      promptAction.showToast({ message: '银行卡删除成功' });
      
    } catch (error) {
      console.error('删除银行卡失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '删除银行卡失败' 
      });
    }
  }

  /**
   * 设置默认银行卡
   */
  async setDefaultCard(card: BankCard) {
    if (!this.userInfo) return;
    
    try {
      await BankCardApi.setDefaultCard(this.userInfo.userId, card.cardId);
      
      promptAction.showToast({ message: '默认银行卡设置成功' });
      await this.loadBankCards(); // 重新加载列表
      
    } catch (error) {
      console.error('设置默认银行卡失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '设置默认银行卡失败' 
      });
    }
  }

  /**
   * 激活/停用银行卡
   */
  async toggleCardStatus(card: BankCard) {
    if (!this.userInfo) return;
    
    try {
      if (card.status === BankCardStatus.NORMAL) {
        await BankCardApi.deactivateCard(this.userInfo.userId, card.cardId);
        promptAction.showToast({ message: '银行卡已停用' });
      } else {
        await BankCardApi.activateCard(this.userInfo.userId, card.cardId);
        promptAction.showToast({ message: '银行卡已激活' });
      }
      
      await this.loadBankCards(); // 重新加载列表
      
    } catch (error) {
      console.error('切换银行卡状态失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '操作失败' 
      });
    }
  }

  /**
   * 显示银行卡详情
   */
  showCardDetail(card: BankCard) {
    this.selectedCard = card;
    this.showCardDetailDialog = true;
  }

  /**
   * 重置新卡表单
   */
  resetNewCardForm() {
    this.newCard = {
      bankName: '',
      cardNumber: '',
      cardType: BankCardType.DEBIT,
      cardHolder: '',
      balance: 0,
      expiryDate: '',
      cvv: '',
      phone: '',
      isDefault: false
    };
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 银行卡列表
      this.BankCardListView()
      
      // 添加银行卡弹窗
      if (this.showAddCardDialog) {
        this.AddCardDialog()
      }
      
      // 银行卡详情弹窗
      if (this.showCardDetailDialog) {
        this.CardDetailDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })
      
      Text('银行卡管理')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })
      
      Blank()
      
      Button('添加')
        .fontSize(14)
        .fontColor('#007AFF')
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.showAddCardDialog = true;
        })
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder BankCardListView() {
    if (this.isLoading && this.bankCards.length === 0) {
      // 首次加载状态
      Column() {
        Text('加载中...')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 100 })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
    } else if (this.bankCards.length === 0) {
      // 空状态
      Column() {
        Text('💳')
          .fontSize(48)
          .margin({ bottom: 16 })
        
        Text('暂无银行卡')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ bottom: 8 })
        
        Text('点击右上角"添加"按钮添加银行卡')
          .fontSize(14)
          .fontColor('#999999')
          .margin({ bottom: 20 })
        
        Button('添加银行卡')
          .fontSize(16)
          .fontColor(Color.White)
          .backgroundColor('#007AFF')
          .borderRadius(8)
          .padding({ horizontal: 24, vertical: 12 })
          .onClick(() => {
            this.showAddCardDialog = true;
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ top: 100 })
    } else {
      // 银行卡列表
      Scroll() {
        Column() {
          ForEach(this.bankCards, (card: BankCard, index: number) => {
            this.BankCardItemView(card)
              .margin({ bottom: 16 })
          })
        }
        .width('100%')
        .padding({ horizontal: 16, vertical: 16 })
      }
      .width('100%')
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
    }
  }

  @Builder BankCardItemView(card: BankCard) {
    Column() {
      // 银行卡样式
      Column() {
        Row() {
          Text(card.bankName)
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(Color.White)
          
          Blank()
          
          if (card.isDefault) {
            Text('默认')
              .fontSize(12)
              .fontColor('#FFD700')
              .backgroundColor('rgba(255, 215, 0, 0.2)')
              .borderRadius(4)
              .padding({ horizontal: 8, vertical: 2 })
          }
        }
        .width('100%')
        .margin({ bottom: 16 })
        
        Text(BankCardApi.formatCardNumber(card.cardNumber))
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor(Color.White)
          .letterSpacing(2)
          .margin({ bottom: 16 })
        
        Row() {
          Text(card.cardHolder)
            .fontSize(14)
            .fontColor('#E0E0E0')
          
          Blank()
          
          Text(BankCardApi.getCardTypeText(card.cardType))
            .fontSize(12)
            .fontColor('#E0E0E0')
        }
        .width('100%')
      }
      .width('100%')
      .height(120)
      .padding(20)
      .backgroundColor(this.getCardColor(card.bankName))
      .borderRadius(12)
      .onClick(() => {
        this.showCardDetail(card);
      })
      
      // 操作按钮
      Row() {
        Button(card.status === BankCardStatus.NORMAL ? '停用' : '激活')
          .fontSize(12)
          .fontColor(card.status === BankCardStatus.NORMAL ? '#FF4444' : '#00C851')
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: card.status === BankCardStatus.NORMAL ? '#FF4444' : '#00C851' })
          .borderRadius(4)
          .padding({ horizontal: 12, vertical: 6 })
          .onClick(() => {
            this.toggleCardStatus(card);
          })
        
        if (!card.isDefault) {
          Button('设为默认')
            .fontSize(12)
            .fontColor('#007AFF')
            .backgroundColor(Color.Transparent)
            .border({ width: 1, color: '#007AFF' })
            .borderRadius(4)
            .padding({ horizontal: 12, vertical: 6 })
            .margin({ left: 8 })
            .onClick(() => {
              this.setDefaultCard(card);
            })
        }
        
        Blank()
        
        Button('删除')
          .fontSize(12)
          .fontColor('#FF4444')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.deleteCard(card);
          })
      }
      .width('100%')
      .margin({ top: 8 })
    }
    .width('100%')
  }

  @Builder AddCardDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showAddCardDialog = false;
          this.resetNewCardForm();
        })
      
      // 添加银行卡弹窗
      Column() {
        Text('添加银行卡')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Scroll() {
          Column() {
            // 银行名称
            this.FormInputView('银行名称', this.newCard.bankName || '', (value: string) => {
              this.newCard.bankName = value;
            })
            
            // 卡号
            this.FormInputView('银行卡号', this.newCard.cardNumber || '', (value: string) => {
              this.newCard.cardNumber = value;
            })
            
            // 持卡人姓名
            this.FormInputView('持卡人姓名', this.newCard.cardHolder || '', (value: string) => {
              this.newCard.cardHolder = value;
            })
            
            // 卡类型选择
            Row() {
              Text('卡类型')
                .fontSize(14)
                .fontColor('#333333')
              
              Blank()
              
              Row() {
                Text('借记卡')
                  .fontSize(14)
                  .fontColor(this.newCard.cardType === BankCardType.DEBIT ? '#007AFF' : '#666666')
                  .onClick(() => {
                    this.newCard.cardType = BankCardType.DEBIT;
                  })
                
                Text('信用卡')
                  .fontSize(14)
                  .fontColor(this.newCard.cardType === BankCardType.CREDIT ? '#007AFF' : '#666666')
                  .margin({ left: 20 })
                  .onClick(() => {
                    this.newCard.cardType = BankCardType.CREDIT;
                  })
              }
            }
            .width('100%')
            .margin({ bottom: 16 })
            
            // 手机号
            this.FormInputView('预留手机号', this.newCard.phone || '', (value: string) => {
              this.newCard.phone = value;
            })
          }
        }
        .width('100%')
        .height(300)
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showAddCardDialog = false;
              this.resetNewCardForm();
            })
          
          Blank()
          
          Button(this.isLoading ? '添加中...' : '确认添加')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.addBankCard();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('90%')
      .maxHeight('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '5%', y: '10%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder FormInputView(label: string, value: string, onChange: (value: string) => void) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: `请输入${label}`, text: value })
        .fontSize(14)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange(onChange)
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder CardDetailDialog() {
    if (!this.selectedCard) return;
    
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showCardDetailDialog = false;
          this.selectedCard = null;
        })
      
      // 银行卡详情弹窗
      Column() {
        Text('银行卡详情')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.DetailItemView('银行名称', this.selectedCard.bankName)
          this.DetailItemView('卡号', BankCardApi.formatCardNumber(this.selectedCard.cardNumber))
          this.DetailItemView('持卡人', this.selectedCard.cardHolder)
          this.DetailItemView('卡类型', BankCardApi.getCardTypeText(this.selectedCard.cardType))
          this.DetailItemView('余额', `¥${this.selectedCard.balance.toFixed(2)}`)
          this.DetailItemView('状态', BankCardApi.getCardStatusText(this.selectedCard.status))
          if (this.selectedCard.phone) {
            this.DetailItemView('预留手机号', this.selectedCard.phone);
          }
        }
        .width('100%')
        
        Button('关闭')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#007AFF')
          .borderRadius(6)
          .padding({ horizontal: 20, vertical: 8 })
          .margin({ top: 20 })
          .onClick(() => {
            this.showCardDetailDialog = false;
            this.selectedCard = null;
          })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder DetailItemView(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)
      
      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
    }
    .width('100%')
    .margin({ bottom: 12 })
  }

  /**
   * 获取银行卡颜色
   */
  getCardColor(bankName: string): string {
    const colors = [
      '#4A90E2', // 蓝色
      '#7ED321', // 绿色
      '#F5A623', // 橙色
      '#D0021B', // 红色
      '#9013FE', // 紫色
      '#50E3C2', // 青色
    ];
    
    let hash = 0;
    for (let i = 0; i < bankName.length; i++) {
      hash = bankName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
}
