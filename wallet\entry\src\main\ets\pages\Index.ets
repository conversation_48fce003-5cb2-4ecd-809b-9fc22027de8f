import router from '@ohos.router';

@Entry
@Component
struct Index {
  @State message: string = '钱包应用启动中...';

  aboutToAppear() {
    // 启动时直接跳转到登录页
    setTimeout(() => {
      router.replaceUrl({
        url: 'pages/LoginPage'
      }).catch((error: Error) => {
        console.error('跳转登录页失败:', error);
      });
    }, 1000);
  }

  build() {
    RelativeContainer() {
      Column() {
        Text('💰')
          .fontSize(60)
          .margin({ bottom: 20 })

        Text(this.message)
          .id('HelloWorld')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#007AFF')
      }
      .alignRules({
        center: { anchor: '__container__', align: VerticalAlign.Center },
        middle: { anchor: '__container__', align: HorizontalAlign.Center }
      })
    }
    .height('100%')
    .width('100%')
    .backgroundColor('#F5F5F5')
  }
}