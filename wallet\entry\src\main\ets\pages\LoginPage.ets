import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { storageManager } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';
import { LoginResponse, LocalUserInfo, NetworkConfig, ApiError, ErrorType } from '../common/types/index';

@Entry
@Component
struct LoginPage {
  @State phone: string = '';
  @State password: string = '';
  @State code: string = '';
  @State isLoading: boolean = false;
  @State currentTab: number = 0; // 0: 密码登录, 1: 验证码登录
  @State countdown: number = 0; // 验证码倒计时
  @State canSendCode: boolean = true; // 是否可以发送验证码
  @State showNetworkSettings: boolean = false; // 显示网络设置
  @State rememberLogin: boolean = false; // 记住登录状态
  @State baseUrl: string = 'http://127.0.0.1:8096/api'; // API服务器地址
  @State isTestMode: boolean = true; // 测试模式

  async aboutToAppear() {
    console.log('LoginPage aboutToAppear');
    
    // 初始化存储管理器
    await this.ensureStorageInitialized();
    
    // 加载保存的配置
    await this.loadSavedConfig();
    
    // 检查是否记住登录状态
    await this.checkRememberLogin();
  }

  /**
   * 确保存储管理器已初始化
   */
  async ensureStorageInitialized() {
    try {
      await storageManager.init();
      console.log('StorageManager initialized in LoginPage');
    } catch (error) {
      console.error('Failed to initialize StorageManager:', error);
      promptAction.showToast({ message: '存储初始化失败' });
    }
  }

  /**
   * 加载保存的配置
   */
  async loadSavedConfig() {
    try {
      // 加载网络配置
      const networkConfig = await storageManager.getNetworkConfig();
      if (networkConfig) {
        this.baseUrl = networkConfig.baseUrl;
        httpClient.setBaseUrl(networkConfig.baseUrl);
      }
      
      // 加载保存的手机号
      const savedPhone = await storageManager.getLoginPhone();
      if (savedPhone) {
        this.phone = savedPhone;
      }
      
      // 加载记住登录状态
      this.rememberLogin = await storageManager.getRememberLogin();
      
    } catch (error) {
      console.error('Failed to load saved config:', error);
    }
  }

  /**
   * 检查记住登录状态
   */
  async checkRememberLogin() {
    try {
      const rememberLogin = await storageManager.getRememberLogin();
      if (rememberLogin) {
        const userInfo = await storageManager.getUserInfo();
        const token = await storageManager.getUserToken();
        
        if (userInfo && token) {
          // 设置HTTP客户端的token
          httpClient.setAuthToken(token);
          
          // 直接跳转到主页
          router.replaceUrl({
            url: 'pages/MainPage'
          }).catch((error: Error) => {
            console.error('跳转主页失败:', error);
          });
          return;
        }
      }
    } catch (error) {
      console.error('检查记住登录状态失败:', error);
    }
  }

  /**
   * 发送验证码
   */
  async sendVerificationCode() {
    if (!this.phone || this.phone.length !== 11) {
      promptAction.showToast({ message: '请输入正确的手机号' });
      return;
    }

    if (!this.canSendCode) {
      return;
    }

    try {
      this.isLoading = true;
      
      let verificationCode: string;
      if (this.isTestMode) {
        // 测试模式：直接返回固定验证码
        verificationCode = '525624';
        promptAction.showToast({ message: `验证码: ${verificationCode}` });
      } else {
        // 正式模式：调用后端API
        verificationCode = await UserApi.sendVerificationCode(this.phone, 1);
        promptAction.showToast({ message: '验证码发送成功' });
      }
      
      this.startCountdown();
      
    } catch (error) {
      console.error('发送验证码失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '发送验证码失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 开始倒计时
   */
  startCountdown() {
    this.canSendCode = false;
    this.countdown = 60;

    const timer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(timer);
        this.canSendCode = true;
        this.countdown = 0;
      }
    }, 1000);
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    if (this.isLoading) return;

    // 表单验证
    if (this.phone.length !== 11) {
      promptAction.showToast({ message: '请输入正确的手机号' });
      return;
    }

    if (this.currentTab === 0) {
      // 密码登录验证
      if (this.password.length < 6) {
        promptAction.showToast({ message: '密码长度不能少于6位' });
        return;
      }
    } else {
      // 验证码登录验证
      if (this.code.length !== 6) {
        promptAction.showToast({ message: '请输入6位验证码' });
        return;
      }
    }

    this.isLoading = true;

    try {
      let loginResponse: LoginResponse;

      if (this.isTestMode) {
        // 测试模式登录
        if (this.currentTab === 0) {
          // 密码登录
          loginResponse = await UserApi.testLogin(this.phone);
        } else {
          // 验证码登录
          const isValid = await UserApi.testVerifyCode(this.phone, this.code, 'sms');
          if (!isValid) {
            promptAction.showToast({ message: '验证码错误' });
            return;
          }
          loginResponse = await UserApi.testLogin(this.phone);
        }
      } else {
        // 正式模式登录
        if (this.currentTab === 0) {
          loginResponse = await UserApi.loginWithPassword(this.phone, this.password);
        } else {
          loginResponse = await UserApi.loginWithSms(this.phone, this.code);
        }
      }

      // 生成Token（实际项目中应该由后端提供）
      const token = `token_${loginResponse.userId}_${Date.now()}`;

      // 保存登录信息
      await storageManager.saveUserToken(token);
      const localUserInfo: LocalUserInfo = {
        userId: loginResponse.userId,
        phone: loginResponse.phone,
        username: loginResponse.username,
        token: token,
        loginTime: Date.now(),
        rememberLogin: this.rememberLogin
      };
      await storageManager.saveUserInfo(localUserInfo);
      await storageManager.saveLoginPhone(this.phone);
      
      // 保存记住登录状态
      if (this.rememberLogin) {
        await storageManager.saveRememberLogin(true);
      }

      // 设置HTTP客户端的token
      httpClient.setAuthToken(token);

      promptAction.showToast({ message: '登录成功' });

      // 跳转到主页
      router.replaceUrl({
        url: 'pages/MainPage'
      }).catch((error: Error) => {
        console.error('跳转主页失败:', error);
        promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
      });

    } catch (error) {
      console.error('登录失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '登录失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 保存网络配置
   */
  async saveNetworkConfig() {
    try {
      const networkConfig: NetworkConfig = {
        baseUrl: this.baseUrl,
        timeout: 30000,
        retryCount: 3,
        environment: this.baseUrl.includes('localhost') || this.baseUrl.includes('127.0.0.1') ? 'development' : 'production'
      };
      
      await storageManager.saveNetworkConfig(networkConfig);
      httpClient.setBaseUrl(this.baseUrl);
      
      promptAction.showToast({ message: '网络配置保存成功' });
      this.showNetworkSettings = false;
    } catch (error) {
      console.error('保存网络配置失败:', error);
      promptAction.showToast({ message: '保存网络配置失败' });
    }
  }

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    try {
      this.isLoading = true;
      httpClient.setBaseUrl(this.baseUrl);
      const isConnected = await httpClient.testConnection();
      
      if (isConnected) {
        promptAction.showToast({ message: '网络连接正常' });
      } else {
        promptAction.showToast({ message: '网络连接失败' });
      }
    } catch (error) {
      console.error('网络连接测试失败:', error);
      promptAction.showToast({ message: '网络连接测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部标题
      this.TopTitleView()
      
      // 登录表单
      this.LoginFormView()
      
      // 网络设置弹窗
      if (this.showNetworkSettings) {
        this.NetworkSettingsDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopTitleView() {
    Column() {
      Text('钱包登录')
        .fontSize(28)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ top: 60, bottom: 10 })
      
      Text('安全便捷的数字钱包')
        .fontSize(16)
        .fontColor('#666666')
        .margin({ bottom: 40 })
      
      // 网络设置按钮
      Row() {
        Blank()
        Button('网络设置')
          .fontSize(14)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.showNetworkSettings = true;
          })
      }
      .width('90%')
      .margin({ bottom: 20 })
    }
  }

  @Builder LoginFormView() {
    Column() {
      // 登录方式切换
      this.LoginTabsView()
      
      // 手机号输入
      this.PhoneInputView()
      
      // 密码或验证码输入
      if (this.currentTab === 0) {
        this.PasswordInputView()
      } else {
        this.CodeInputView()
      }
      
      // 记住登录选项
      this.RememberLoginView()
      
      // 登录按钮
      this.LoginButtonView()
      
    }
    .width('90%')
    .padding(20)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ top: 20 })
  }

  @Builder LoginTabsView() {
    Row() {
      Text('密码登录')
        .fontSize(16)
        .fontColor(this.currentTab === 0 ? '#007AFF' : '#666666')
        .fontWeight(this.currentTab === 0 ? FontWeight.Bold : FontWeight.Normal)
        .padding({ vertical: 10, horizontal: 20 })
        .onClick(() => {
          this.currentTab = 0;
        })
      
      Text('验证码登录')
        .fontSize(16)
        .fontColor(this.currentTab === 1 ? '#007AFF' : '#666666')
        .fontWeight(this.currentTab === 1 ? FontWeight.Bold : FontWeight.Normal)
        .padding({ vertical: 10, horizontal: 20 })
        .onClick(() => {
          this.currentTab = 1;
        })
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .margin({ bottom: 20 })
  }

  @Builder PhoneInputView() {
    Column() {
      Text('手机号')
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: '请输入手机号', text: this.phone })
        .type(InputType.PhoneNumber)
        .maxLength(11)
        .fontSize(16)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange((value: string) => {
          this.phone = value;
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder PasswordInputView() {
    Column() {
      Text('密码')
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: '请输入密码', text: this.password })
        .type(InputType.Password)
        .fontSize(16)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange((value: string) => {
          this.password = value;
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder CodeInputView() {
    Column() {
      Row() {
        Text('验证码')
          .fontSize(14)
          .fontColor('#333333')
        
        Blank()
        
        Button(this.canSendCode ? '发送验证码' : `${this.countdown}s`)
          .fontSize(14)
          .fontColor(this.canSendCode ? '#007AFF' : '#999999')
          .backgroundColor(Color.Transparent)
          .enabled(this.canSendCode)
          .onClick(() => {
            this.sendVerificationCode();
          })
      }
      .width('100%')
      .margin({ bottom: 8 })
      
      TextInput({ placeholder: '请输入6位验证码', text: this.code })
        .type(InputType.Number)
        .maxLength(6)
        .fontSize(16)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange((value: string) => {
          this.code = value;
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder RememberLoginView() {
    Row() {
      Checkbox({ name: 'remember', group: 'loginGroup' })
        .select(this.rememberLogin)
        .onChange((value: boolean) => {
          this.rememberLogin = value;
        })
      
      Text('记住登录状态（7天内免登录）')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ left: 8 })
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder LoginButtonView() {
    Button(this.isLoading ? '登录中...' : '登录')
      .width('100%')
      .height(48)
      .fontSize(16)
      .fontColor(Color.White)
      .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
      .borderRadius(8)
      .enabled(!this.isLoading)
      .onClick(() => {
        this.handleLogin();
      })
  }

  @Builder NetworkSettingsDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showNetworkSettings = false;
        })
      
      // 设置弹窗
      Column() {
        Text('网络设置')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          Text('API服务器地址')
            .fontSize(14)
            .fontColor('#333333')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })
          
          TextInput({ placeholder: '请输入API服务器地址', text: this.baseUrl })
            .fontSize(14)
            .padding(12)
            .backgroundColor('#F8F8F8')
            .borderRadius(8)
            .onChange((value: string) => {
              this.baseUrl = value;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })
        
        Row() {
          Button('测试连接')
            .fontSize(14)
            .fontColor('#007AFF')
            .backgroundColor('#F0F8FF')
            .borderRadius(6)
            .padding({ horizontal: 16, vertical: 8 })
            .onClick(() => {
              this.testNetworkConnection();
            })
          
          Blank()
          
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 16, vertical: 8 })
            .onClick(() => {
              this.showNetworkSettings = false;
            })
          
          Button('保存')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor('#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 16, vertical: 8 })
            .margin({ left: 10 })
            .onClick(() => {
              this.saveNetworkConfig();
            })
        }
        .width('100%')
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }
}
