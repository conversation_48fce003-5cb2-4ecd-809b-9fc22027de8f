import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { WalletApi } from '../api/WalletApi';
import { BankCardApi } from '../api/BankCardApi';
import { TransactionApi } from '../api/TransactionApi';
import { storageManager } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';
import { 
  LocalUserInfo, 
  WalletInfo, 
  BankCard, 
  Transaction, 
  TransactionType,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct MainPage {
  @State userInfo: LocalUserInfo | null = null;
  @State walletInfo: WalletInfo | null = null;
  @State bankCards: BankCard[] = [];
  @State recentTransactions: Transaction[] = [];
  @State isLoading: boolean = false;
  @State currentTabIndex: number = 0; // 0: 钱包首页, 1: 交易记录, 2: 银行卡管理, 3: 个人设置
  @State refreshing: boolean = false;

  async aboutToAppear() {
    console.log('MainPage aboutToAppear');
    
    // 初始化存储管理器
    await this.ensureStorageInitialized();
    
    // 检查登录状态
    await this.checkLoginStatus();
    
    // 加载数据
    await this.loadAllData();
    
    // 设置自动刷新
    this.setupAutoRefresh();
  }

  /**
   * 确保存储管理器已初始化
   */
  async ensureStorageInitialized() {
    try {
      await storageManager.init();
      console.log('StorageManager initialized in MainPage');
    } catch (error) {
      console.error('Failed to initialize StorageManager:', error);
    }
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      const userInfo = await storageManager.getUserInfo();
      const token = await storageManager.getUserToken();
      
      if (!userInfo || !token) {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
        return;
      }
      
      this.userInfo = userInfo;
      httpClient.setAuthToken(token);
      
    } catch (error) {
      console.error('检查登录状态失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载所有数据
   */
  async loadAllData() {
    if (!this.userInfo) return;
    
    this.isLoading = true;
    
    try {
      // 并行加载数据
      await Promise.all([
        this.loadWalletInfo(),
        this.loadBankCards(),
        this.loadRecentTransactions()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '加载数据失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 加载钱包信息
   */
  async loadWalletInfo() {
    if (!this.userInfo) return;
    
    try {
      const walletInfo = await WalletApi.getAccountInfo(this.userInfo.userId);
      this.walletInfo = walletInfo;
      
      // 保存到本地存储
      await storageManager.saveWalletInfo({
        accountId: walletInfo.accountId,
        balance: walletInfo.balance,
        dailyLimit: walletInfo.dailyLimit,
        singleLimit: walletInfo.singleLimit,
        monthlyLimit: walletInfo.monthlyLimit,
        lastUpdateTime: Date.now()
      });
      
      console.log('钱包信息加载成功:', walletInfo);
    } catch (error) {
      console.error('加载钱包信息失败:', error);
      // 尝试从本地存储加载
      const localWalletInfo = await storageManager.getWalletInfo();
      if (localWalletInfo) {
        this.walletInfo = {
          accountId: localWalletInfo.accountId,
          userId: this.userInfo.userId,
          balance: localWalletInfo.balance,
          dailyLimit: localWalletInfo.dailyLimit,
          singleLimit: localWalletInfo.singleLimit,
          monthlyLimit: localWalletInfo.monthlyLimit,
          status: 1
        };
      }
    }
  }

  /**
   * 加载银行卡列表
   */
  async loadBankCards() {
    if (!this.userInfo) return;
    
    try {
      const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
      this.bankCards = bankCards;
      console.log('银行卡列表加载成功:', bankCards.length);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
    }
  }

  /**
   * 加载最近交易记录
   */
  async loadRecentTransactions() {
    if (!this.userInfo) return;
    
    try {
      const transactions = await TransactionApi.getRecentTransactions(this.userInfo.userId, 5);
      this.recentTransactions = transactions;
      console.log('最近交易记录加载成功:', transactions.length);
    } catch (error) {
      console.error('加载最近交易记录失败:', error);
    }
  }

  /**
   * 设置自动刷新
   */
  setupAutoRefresh() {
    // 每30秒自动刷新一次数据
    setInterval(() => {
      if (!this.refreshing) {
        this.refreshData();
      }
    }, 30000);
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    this.refreshing = true;
    
    try {
      await this.loadAllData();
      console.log('数据刷新成功');
    } catch (error) {
      console.error('数据刷新失败:', error);
    } finally {
      this.refreshing = false;
    }
  }

  /**
   * 手动下拉刷新
   */
  async onRefresh() {
    await this.refreshData();
    promptAction.showToast({ message: '刷新完成' });
  }

  /**
   * 跳转到钱包操作页面
   */
  navigateToWalletOperation(operationType: string) {
    router.pushUrl({
      url: 'pages/WalletOperationPage',
      params: {
        operationType: operationType,
        userId: this.userInfo?.userId
      }
    }).catch((error: Error) => {
      console.error('跳转钱包操作页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 跳转到支付页面
   */
  navigateToPayment() {
    router.pushUrl({
      url: 'pages/PaymentPage',
      params: {
        userId: this.userInfo?.userId
      }
    }).catch((error: Error) => {
      console.error('跳转支付页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 跳转到银行卡管理页面
   */
  navigateToBankCardManagement() {
    router.pushUrl({
      url: 'pages/BankCardPage',
      params: {
        userId: this.userInfo?.userId
      }
    }).catch((error: Error) => {
      console.error('跳转银行卡管理页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 跳转到个人设置页面
   */
  navigateToSettings() {
    router.pushUrl({
      url: 'pages/SettingsPage',
      params: {
        userId: this.userInfo?.userId
      }
    }).catch((error: Error) => {
      console.error('跳转个人设置页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 切换到交易记录页面
   */
  switchToTransactionRecords() {
    router.pushUrl({
      url: 'pages/TransactionRecordsPage',
      params: {
        userId: this.userInfo?.userId
      }
    }).catch((error: Error) => {
      console.error('跳转交易记录页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 退出登录
   */
  async logout() {
    try {
      await storageManager.clearUserData();
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      promptAction.showToast({ message: '退出登录失败' });
    }
  }

  build() {
    Column() {
      // 主要内容区域
      this.MainContentView()
      
      // 底部导航栏
      this.BottomNavigationView()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder MainContentView() {
    Scroll() {
      Column() {
        // 钱包卡片区域
        this.WalletCardView()
        
        // 快捷操作区域
        this.QuickActionsView()
        
        // 最近交易区域
        this.RecentTransactionsView()
      }
      .width('100%')
      .padding({ bottom: 80 }) // 为底部导航栏留出空间
    }
    .width('100%')
    .height('100%')
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
    .onScrollStart(() => {
      // 开始滚动时的处理
    })
    .onScrollEnd(() => {
      // 滚动结束时的处理
    })
  }

  @Builder WalletCardView() {
    Column() {
      Row() {
        // 用户头像和信息
        Column() {
          Image($r('app.media.icon'))
            .width(50)
            .height(50)
            .borderRadius(25)
            .backgroundColor('#E0E0E0')
            .onClick(() => {
              this.navigateToSettings();
            })
          
          Text(this.userInfo?.username || '用户')
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(Color.White)
            .margin({ top: 8 })
          
          Text(this.formatPhoneNumber(this.userInfo?.phone || ''))
            .fontSize(12)
            .fontColor('#E0E0E0')
            .margin({ top: 4 })
        }
        .alignItems(HorizontalAlign.Center)
        
        Blank()
        
        // 设置按钮
        Image($r('app.media.icon'))
          .width(24)
          .height(24)
          .fillColor(Color.White)
          .onClick(() => {
            this.navigateToSettings();
          })
      }
      .width('100%')
      .padding({ horizontal: 20, vertical: 16 })
      
      // 钱包余额
      Column() {
        Text('钱包余额（元）')
          .fontSize(14)
          .fontColor('#E0E0E0')
          .margin({ bottom: 8 })
        
        Text(this.walletInfo?.balance?.toFixed(2) || '0.00')
          .fontSize(32)
          .fontWeight(FontWeight.Bold)
          .fontColor(Color.White)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Center)
      .padding({ horizontal: 20, vertical: 20 })
    }
    .width('90%')
    .backgroundColor('#007AFF')
    .borderRadius(16)
    .margin({ horizontal: 20, vertical: 16 })
  }

  @Builder QuickActionsView() {
    Column() {
      Text('快捷操作')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })
      
      Row() {
        this.QuickActionButton('充值', '💰', () => {
          this.navigateToWalletOperation('recharge');
        })
        
        this.QuickActionButton('提现', '💸', () => {
          this.navigateToWalletOperation('withdraw');
        })
        
        this.QuickActionButton('转账', '💳', () => {
          this.navigateToWalletOperation('transfer');
        })
        
        this.QuickActionButton('收钱', '💵', () => {
          this.navigateToWalletOperation('receive');
        })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      
      // 支付功能按钮
      Button('立即支付')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor(Color.White)
        .backgroundColor('#FF6B35')
        .borderRadius(8)
        .margin({ top: 16 })
        .onClick(() => {
          this.navigateToPayment();
        })
    }
    .width('90%')
    .padding(20)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ horizontal: 20, bottom: 16 })
  }

  @Builder QuickActionButton(title: string, icon: string, onClick: () => void) {
    Column() {
      Text(icon)
        .fontSize(24)
        .margin({ bottom: 8 })
      
      Text(title)
        .fontSize(14)
        .fontColor('#333333')
    }
    .width(60)
    .height(80)
    .justifyContent(FlexAlign.Center)
    .backgroundColor('#F8F8F8')
    .borderRadius(8)
    .onClick(onClick)
  }

  @Builder RecentTransactionsView() {
    Column() {
      Row() {
        Text('最近交易')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
        
        Blank()
        
        Text('查看全部')
          .fontSize(14)
          .fontColor('#007AFF')
          .onClick(() => {
            this.switchToTransactionRecords();
          })
      }
      .width('100%')
      .margin({ bottom: 16 })
      
      if (this.recentTransactions.length > 0) {
        Column() {
          ForEach(this.recentTransactions, (transaction: Transaction, index: number) => {
            this.TransactionItemView(transaction)
            
            if (index < this.recentTransactions.length - 1) {
              Divider()
                .color('#F0F0F0')
                .strokeWidth(1)
                .margin({ vertical: 8 })
            }
          })
        }
        .width('100%')
      } else {
        Column() {
          Text('暂无交易记录')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ vertical: 40 })
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ horizontal: 20, bottom: 16 })
  }

  @Builder TransactionItemView(transaction: Transaction) {
    Row() {
      // 交易类型图标
      Text(this.getTransactionIcon(transaction.type))
        .fontSize(20)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor('#F0F8FF')
        .borderRadius(20)
        .margin({ right: 12 })
      
      // 交易信息
      Column() {
        Text(TransactionApi.getTransactionTypeText(transaction.type))
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
        
        Text(this.formatTransactionTime(transaction.createdAt))
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
      
      // 交易金额
      Text(TransactionApi.formatAmount(transaction.amount, transaction.type))
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor(TransactionApi.getAmountColor(transaction.type))
    }
    .width('100%')
    .padding({ vertical: 8 })
    .onClick(() => {
      // 跳转到交易详情页面
      router.pushUrl({
        url: 'pages/TransactionDetailPage',
        params: {
          txnId: transaction.txnId
        }
      });
    })
  }

  @Builder BottomNavigationView() {
    Row() {
      this.BottomNavItem('钱包', '💰', 0)
      this.BottomNavItem('交易', '📊', 1)
      this.BottomNavItem('银行卡', '💳', 2)
      this.BottomNavItem('设置', '⚙️', 3)
    }
    .width('100%')
    .height(60)
    .backgroundColor(Color.White)
    .justifyContent(FlexAlign.SpaceEvenly)
    .position({ x: 0, y: '100%' })
    .translate({ y: -60 })
    .border({ width: { top: 1 }, color: '#E0E0E0' })
  }

  @Builder BottomNavItem(title: string, icon: string, index: number) {
    Column() {
      Text(icon)
        .fontSize(20)
        .margin({ bottom: 4 })
      
      Text(title)
        .fontSize(12)
        .fontColor(this.currentTabIndex === index ? '#007AFF' : '#999999')
    }
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      this.currentTabIndex = index;
      switch (index) {
        case 0:
          // 当前页面，刷新数据
          this.refreshData();
          break;
        case 1:
          this.switchToTransactionRecords();
          break;
        case 2:
          this.navigateToBankCardManagement();
          break;
        case 3:
          this.navigateToSettings();
          break;
      }
    })
  }

  /**
   * 格式化手机号显示
   */
  formatPhoneNumber(phone: string): string {
    if (!phone || phone.length < 11) {
      return phone;
    }
    return phone.substring(0, 3) + '****' + phone.substring(7);
  }

  /**
   * 获取交易类型图标
   */
  getTransactionIcon(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE:
        return '💰';
      case TransactionType.TRANSFER:
        return '💸';
      case TransactionType.RECEIVE:
        return '💵';
      case TransactionType.PAYMENT:
        return '🛒';
      default:
        return '💳';
    }
  }

  /**
   * 格式化交易时间显示
   */
  formatTransactionTime(timeStr: string): string {
    try {
      const date = new Date(timeStr);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      
      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) { // 24小时内
        return `${Math.floor(diff / 3600000)}小时前`;
      } else {
        return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
    } catch (error) {
      return timeStr;
    }
  }
}
