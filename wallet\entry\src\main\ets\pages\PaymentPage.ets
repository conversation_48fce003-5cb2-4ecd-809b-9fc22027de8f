import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { WalletApi } from '../api/WalletApi';
import { BankCardApi } from '../api/BankCardApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  LocalUserInfo, 
  BankCard,
  PaymentMethod,
  PaymentChannel,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct PaymentPage {
  @State userInfo: LocalUserInfo | null = null;
  @State paymentChannel: PaymentChannel = PaymentChannel.MERCHANT; // merchant, QR_code, NFC
  @State paymentMethod: PaymentMethod = PaymentMethod.WALLET; // wallet, bank_card
  @State amount: string = '';
  @State selectedCard: BankCard | null = null;
  @State bankCards: BankCard[] = [];
  @State payPassword: string = '';
  @State merchantInfo: string = ''; // 商户信息
  @State productCategory: string = ''; // 商品类别
  @State isLoading: boolean = false;
  @State showCardSelector: boolean = false;
  @State showPayPasswordDialog: boolean = false;
  @State showChannelSelector: boolean = false;
  @State walletBalance: number = 0;

  async aboutToAppear() {
    console.log('PaymentPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params) {
      this.paymentChannel = params.paymentChannel || PaymentChannel.MERCHANT;
      this.amount = params.amount || '';
      this.merchantInfo = params.merchantInfo || '';
      if (params.userId) {
        this.userInfo = {
          userId: params.userId,
          phone: '',
          username: '',
          token: '',
          loginTime: Date.now(),
          rememberLogin: false
        };
      }
    }
    
    if (!this.userInfo) {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载银行卡列表和钱包余额
    await this.loadBankCards();
    await this.loadWalletBalance();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载银行卡列表
   */
  async loadBankCards() {
    if (!this.userInfo) return;
    
    try {
      const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
      this.bankCards = bankCards;
      
      // 自动选择默认银行卡
      const defaultCard = bankCards.find(card => card.isDefault);
      if (defaultCard) {
        this.selectedCard = defaultCard;
      } else if (bankCards.length > 0) {
        this.selectedCard = bankCards[0];
      }
      
      console.log(`银行卡列表加载成功: ${bankCards.length}张`);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
    }
  }

  /**
   * 加载钱包余额
   */
  async loadWalletBalance() {
    if (!this.userInfo) return;
    
    try {
      const balance = await WalletApi.getWalletBalance(this.userInfo.userId);
      this.walletBalance = balance;
      console.log(`钱包余额: ¥${balance}`);
    } catch (error) {
      console.error('加载钱包余额失败:', error);
    }
  }

  /**
   * 执行支付
   */
  async executePayment() {
    if (!this.userInfo || !this.validateForm()) {
      return;
    }
    
    this.showPayPasswordDialog = true;
  }

  /**
   * 确认支付
   */
  async confirmPayment() {
    if (!this.userInfo || !this.payPassword) {
      promptAction.showToast({ message: '请输入支付密码' });
      return;
    }
    
    this.isLoading = true;
    this.showPayPasswordDialog = false;
    
    try {
      const amountValue = parseFloat(this.amount);
      
      if (this.paymentMethod === PaymentMethod.WALLET) {
        // 钱包支付
        await WalletApi.walletPay(
          this.userInfo.userId,
          amountValue,
          this.paymentChannel,
          this.payPassword,
          this.merchantInfo || '商户支付',
          this.productCategory
        );
      } else {
        // 银行卡支付
        if (!this.selectedCard) {
          throw new Error('请选择银行卡');
        }
        
        await WalletApi.bankCardPay(
          this.userInfo.userId,
          this.selectedCard.cardId,
          amountValue,
          this.paymentChannel,
          this.payPassword,
          this.merchantInfo || '商户支付',
          this.productCategory
        );
      }
      
      promptAction.showToast({ message: '支付成功' });
      
      // 清空表单
      this.resetForm();
      
      // 延迟返回上一页
      setTimeout(() => {
        router.back();
      }, 1500);
      
    } catch (error) {
      console.error('支付失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '支付失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 表单验证
   */
  validateForm(): boolean {
    if (!this.amount || parseFloat(this.amount) <= 0) {
      promptAction.showToast({ message: '请输入正确的支付金额' });
      return false;
    }
    
    const amountValue = parseFloat(this.amount);
    
    if (this.paymentMethod === PaymentMethod.WALLET) {
      if (amountValue > this.walletBalance) {
        promptAction.showToast({ message: '钱包余额不足' });
        return false;
      }
    } else {
      if (!this.selectedCard) {
        promptAction.showToast({ message: '请选择银行卡' });
        return false;
      }
      
      if (amountValue > this.selectedCard.balance) {
        promptAction.showToast({ message: '银行卡余额不足' });
        return false;
      }
    }
    
    return true;
  }

  /**
   * 重置表单
   */
  resetForm() {
    this.amount = '';
    this.payPassword = '';
    this.merchantInfo = '';
    this.productCategory = '';
  }

  /**
   * 选择支付方式
   */
  selectPaymentMethod(method: PaymentMethod) {
    this.paymentMethod = method;
  }

  /**
   * 选择银行卡
   */
  selectCard(card: BankCard) {
    this.selectedCard = card;
    this.showCardSelector = false;
  }

  /**
   * 选择支付渠道
   */
  selectPaymentChannel(channel: PaymentChannel) {
    this.paymentChannel = channel;
    this.showChannelSelector = false;
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  /**
   * 获取支付渠道文本
   */
  getPaymentChannelText(channel: PaymentChannel): string {
    switch (channel) {
      case PaymentChannel.MERCHANT:
        return '商户支付';
      case PaymentChannel.QR_CODE:
        return '扫码支付';
      case PaymentChannel.NFC:
        return 'NFC支付';
      default:
        return '支付';
    }
  }

  /**
   * 获取支付渠道图标
   */
  getPaymentChannelIcon(channel: PaymentChannel): string {
    switch (channel) {
      case PaymentChannel.MERCHANT:
        return '🏪';
      case PaymentChannel.QR_CODE:
        return '📱';
      case PaymentChannel.NFC:
        return '📡';
      default:
        return '💳';
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()

      // 支付表单
      this.PaymentFormView()

      // 银行卡选择弹窗
      if (this.showCardSelector) {
        this.CardSelectorDialog()
      }

      // 支付密码弹窗
      if (this.showPayPasswordDialog) {
        this.PayPasswordDialog()
      }

      // 支付渠道选择弹窗
      if (this.showChannelSelector) {
        this.ChannelSelectorDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })

      Text('支付')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })

      Blank()

      Text(this.getPaymentChannelIcon(this.paymentChannel))
        .fontSize(20)
        .onClick(() => {
          this.showChannelSelector = true;
        })
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder PaymentFormView() {
    Scroll() {
      Column() {
        // 支付渠道显示
        this.PaymentChannelView()

        // 金额输入
        this.AmountInputView()

        // 支付方式选择
        this.PaymentMethodView()

        // 商户信息（如果是商户支付）
        if (this.paymentChannel === PaymentChannel.MERCHANT) {
          this.MerchantInfoView()
        }

        // 确认支付按钮
        this.ConfirmPaymentButtonView()
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 20 })
    }
    .width('100%')
    .layoutWeight(1)
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
  }

  @Builder PaymentChannelView() {
    Column() {
      Text('支付渠道')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      Row() {
        Text(this.getPaymentChannelIcon(this.paymentChannel))
          .fontSize(24)
          .margin({ right: 12 })

        Text(this.getPaymentChannelText(this.paymentChannel))
          .fontSize(16)
          .fontColor('#333333')
          .layoutWeight(1)

        Image($r('app.media.icon'))
          .width(16)
          .height(16)
          .fillColor('#CCCCCC')
      }
      .width('100%')
      .padding(16)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .onClick(() => {
        this.showChannelSelector = true;
      })
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder AmountInputView() {
    Column() {
      Text('支付金额')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      Row() {
        Text('¥')
          .fontSize(32)
          .fontColor('#333333')
          .margin({ right: 8 })

        TextInput({ placeholder: '0.00', text: this.amount })
          .type(InputType.Number)
          .fontSize(32)
          .fontWeight(FontWeight.Bold)
          .backgroundColor(Color.Transparent)
          .border({ width: 0 })
          .layoutWeight(1)
          .onChange((value: string) => {
            this.amount = value;
          })
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 24 })
      .backgroundColor(Color.White)
      .borderRadius(12)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder PaymentMethodView() {
    Column() {
      Text('支付方式')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      Column() {
        // 钱包支付选项
        Row() {
          Row() {
            Text('💰')
              .fontSize(20)
              .margin({ right: 12 })

            Column() {
              Text('钱包支付')
                .fontSize(16)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)

              Text(`余额: ¥${this.walletBalance.toFixed(2)}`)
                .fontSize(12)
                .fontColor('#666666')
                .alignSelf(ItemAlign.Start)
                .margin({ top: 2 })
            }
            .alignItems(HorizontalAlign.Start)
            .layoutWeight(1)
          }
          .layoutWeight(1)

          if (this.paymentMethod === PaymentMethod.WALLET) {
            Text('✓')
              .fontSize(16)
              .fontColor('#007AFF')
          }
        }
        .width('100%')
        .padding(16)
        .onClick(() => {
          this.selectPaymentMethod(PaymentMethod.WALLET);
        })

        Divider()
          .color('#F0F0F0')

        // 银行卡支付选项
        Row() {
          Row() {
            Text('💳')
              .fontSize(20)
              .margin({ right: 12 })

            Column() {
              Text('银行卡支付')
                .fontSize(16)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)

              if (this.selectedCard) {
                Text(`${this.selectedCard.bankName} ${BankCardApi.formatCardNumber(this.selectedCard.cardNumber)}`)
                  .fontSize(12)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 2 })
              } else {
                Text('请选择银行卡')
                  .fontSize(12)
                  .fontColor('#999999')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 2 })
              }
            }
            .alignItems(HorizontalAlign.Start)
            .layoutWeight(1)
          }
          .layoutWeight(1)

          Row() {
            if (this.paymentMethod === PaymentMethod.BANK_CARD) {
              Text('✓')
                .fontSize(16)
                .fontColor('#007AFF')
                .margin({ right: 8 })
            }

            Image($r('app.media.icon'))
              .width(16)
              .height(16)
              .fillColor('#CCCCCC')
          }
        }
        .width('100%')
        .padding(16)
        .onClick(() => {
          this.selectPaymentMethod(PaymentMethod.BANK_CARD);
          if (this.bankCards.length > 0) {
            this.showCardSelector = true;
          }
        })
      }
      .width('100%')
      .backgroundColor(Color.White)
      .borderRadius(12)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder MerchantInfoView() {
    Column() {
      Text('商户信息')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      Column() {
        TextInput({ placeholder: '请输入商户名称', text: this.merchantInfo })
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .margin({ bottom: 12 })
          .onChange((value: string) => {
            this.merchantInfo = value;
          })

        TextInput({ placeholder: '请输入商品类别（可选）', text: this.productCategory })
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .onChange((value: string) => {
            this.productCategory = value;
          })
      }
      .width('100%')
      .padding(16)
      .backgroundColor(Color.White)
      .borderRadius(12)
    }
    .width('100%')
    .margin({ bottom: 30 })
  }

  @Builder ConfirmPaymentButtonView() {
    Button(this.isLoading ? '支付中...' : `确认支付 ¥${this.amount || '0.00'}`)
      .width('100%')
      .height(48)
      .fontSize(16)
      .fontColor(Color.White)
      .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
      .borderRadius(8)
      .enabled(!this.isLoading)
      .onClick(() => {
        this.executePayment();
      })
  }

  @Builder CardSelectorDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showCardSelector = false;
        })

      // 银行卡选择弹窗
      Column() {
        Text('选择银行卡')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        if (this.bankCards.length > 0) {
          Column() {
            ForEach(this.bankCards, (card: BankCard) => {
              Row() {
                Column() {
                  Text(card.bankName)
                    .fontSize(16)
                    .fontColor('#333333')
                    .alignSelf(ItemAlign.Start)

                  Text(BankCardApi.formatCardNumber(card.cardNumber))
                    .fontSize(14)
                    .fontColor('#666666')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })

                  Text(`余额: ¥${card.balance.toFixed(2)}`)
                    .fontSize(12)
                    .fontColor('#999999')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)

                if (this.selectedCard?.cardId === card.cardId) {
                  Text('✓')
                    .fontSize(16)
                    .fontColor('#007AFF')
                }
              }
              .width('100%')
              .padding(16)
              .onClick(() => {
                this.selectCard(card);
              })
            })
          }
          .width('100%')
        } else {
          Text('暂无可用银行卡')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ vertical: 20 })
        }

        Button('取消')
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F5F5F5')
          .borderRadius(6)
          .padding({ horizontal: 20, vertical: 8 })
          .margin({ top: 20 })
          .onClick(() => {
            this.showCardSelector = false;
          })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '25%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder PayPasswordDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showPayPasswordDialog = false;
          this.payPassword = '';
        })

      // 支付密码弹窗
      Column() {
        Text('请输入支付密码')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        Text(`支付金额: ¥${this.amount}`)
          .fontSize(16)
          .fontColor('#333333')
          .margin({ bottom: 16 })

        Text(`支付方式: ${this.paymentMethod === PaymentMethod.WALLET ? '钱包支付' : '银行卡支付'}`)
          .fontSize(14)
          .fontColor('#666666')
          .margin({ bottom: 20 })

        TextInput({ placeholder: '请输入6位支付密码', text: this.payPassword })
          .type(InputType.Password)
          .maxLength(6)
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .onChange((value: string) => {
            this.payPassword = value;
          })

        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showPayPasswordDialog = false;
              this.payPassword = '';
            })

          Blank()

          Button(this.isLoading ? '支付中...' : '确认支付')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.confirmPayment();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('85%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '7.5%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder ChannelSelectorDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showChannelSelector = false;
        })

      // 支付渠道选择弹窗
      Column() {
        Text('选择支付渠道')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        Column() {
          this.ChannelOptionView('商户支付', PaymentChannel.MERCHANT, '🏪')
          this.ChannelOptionView('扫码支付', PaymentChannel.QR_CODE, '📱')
          this.ChannelOptionView('NFC支付', PaymentChannel.NFC, '📡')
        }
        .width('100%')

        Button('取消')
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F5F5F5')
          .borderRadius(6)
          .padding({ horizontal: 20, vertical: 8 })
          .margin({ top: 20 })
          .onClick(() => {
            this.showChannelSelector = false;
          })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '35%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder ChannelOptionView(title: string, channel: PaymentChannel, icon: string) {
    Row() {
      Text(icon)
        .fontSize(20)
        .margin({ right: 12 })

      Text(title)
        .fontSize(16)
        .fontColor('#333333')
        .layoutWeight(1)

      if (this.paymentChannel === channel) {
        Text('✓')
          .fontSize(16)
          .fontColor('#007AFF')
      }
    }
    .width('100%')
    .height(48)
    .padding({ horizontal: 16 })
    .onClick(() => {
      this.selectPaymentChannel(channel);
    })
  }
}
