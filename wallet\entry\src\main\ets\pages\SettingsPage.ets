import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  LocalUserInfo, 
  UserInfo,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct SettingsPage {
  @State userInfo: LocalUserInfo | null = null;
  @State fullUserInfo: UserInfo | null = null;
  @State isLoading: boolean = false;
  @State showChangePasswordDialog: boolean = false;
  @State showPayPasswordDialog: boolean = false;
  @State showLimitDialog: boolean = false;
  @State showProfileDialog: boolean = false;

  // 修改密码表单
  @State oldPassword: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';

  // 支付密码表单
  @State payPassword: string = '';
  @State confirmPayPassword: string = '';

  // 限额设置表单
  @State dailyLimit: number = 50000;
  @State singleLimit: number = 5000;
  @State monthlyLimit: number = 200000;

  // 个人资料表单
  @State editUsername: string = '';
  @State editEmail: string = '';

  async aboutToAppear() {
    console.log('SettingsPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params && params.userId) {
      this.userInfo = {
        userId: params.userId,
        phone: '',
        username: '',
        token: '',
        loginTime: Date.now(),
        rememberLogin: false
      };
    } else {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载完整用户信息
    await this.loadFullUserInfo();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载完整用户信息
   */
  async loadFullUserInfo() {
    if (!this.userInfo) return;
    
    this.isLoading = true;
    
    try {
      const fullUserInfo = await UserApi.getUserProfile(this.userInfo.userId);
      this.fullUserInfo = fullUserInfo;
      
      // 初始化表单数据
      this.editUsername = fullUserInfo.username || '';
      this.editEmail = fullUserInfo.email || '';
      this.dailyLimit = fullUserInfo.dailyLimit || 50000;
      this.singleLimit = fullUserInfo.singleLimit || 5000;
      this.monthlyLimit = fullUserInfo.monthlyLimit || 200000;
      
      console.log('完整用户信息加载成功:', fullUserInfo);
    } catch (error) {
      console.error('加载完整用户信息失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '加载用户信息失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 修改登录密码
   */
  async changePassword() {
    if (!this.userInfo) return;
    
    // 表单验证
    if (!this.oldPassword || !this.newPassword || !this.confirmPassword) {
      promptAction.showToast({ message: '请填写完整的密码信息' });
      return;
    }
    
    if (this.newPassword !== this.confirmPassword) {
      promptAction.showToast({ message: '两次输入的新密码不一致' });
      return;
    }
    
    if (this.newPassword.length < 6) {
      promptAction.showToast({ message: '新密码长度不能少于6位' });
      return;
    }
    
    this.isLoading = true;
    
    try {
      await UserApi.changePassword(this.userInfo.userId, this.oldPassword, this.newPassword);
      
      // 重置表单
      this.oldPassword = '';
      this.newPassword = '';
      this.confirmPassword = '';
      this.showChangePasswordDialog = false;
      
      promptAction.showToast({ message: '密码修改成功' });
      
    } catch (error) {
      console.error('修改密码失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '修改密码失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 设置支付密码
   */
  async setPayPassword() {
    if (!this.userInfo) return;
    
    // 表单验证
    if (!this.payPassword || !this.confirmPayPassword) {
      promptAction.showToast({ message: '请填写完整的支付密码信息' });
      return;
    }
    
    if (this.payPassword !== this.confirmPayPassword) {
      promptAction.showToast({ message: '两次输入的支付密码不一致' });
      return;
    }
    
    if (this.payPassword.length !== 6) {
      promptAction.showToast({ message: '支付密码必须为6位数字' });
      return;
    }
    
    this.isLoading = true;
    
    try {
      await UserApi.setPaymentPassword(this.userInfo.userId, this.payPassword);
      
      // 重置表单
      this.payPassword = '';
      this.confirmPayPassword = '';
      this.showPayPasswordDialog = false;
      
      promptAction.showToast({ message: '支付密码设置成功' });
      
    } catch (error) {
      console.error('设置支付密码失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '设置支付密码失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 设置交易限额
   */
  async setTransactionLimits() {
    if (!this.userInfo) return;
    
    // 表单验证
    if (this.dailyLimit <= 0 || this.singleLimit <= 0 || this.monthlyLimit <= 0) {
      promptAction.showToast({ message: '限额必须大于0' });
      return;
    }
    
    if (this.singleLimit > this.dailyLimit) {
      promptAction.showToast({ message: '单笔限额不能大于日限额' });
      return;
    }
    
    if (this.dailyLimit * 30 > this.monthlyLimit) {
      promptAction.showToast({ message: '月限额设置可能过低' });
      return;
    }
    
    this.isLoading = true;
    
    try {
      await UserApi.setTransactionLimits(this.userInfo.userId, this.dailyLimit, this.singleLimit, this.monthlyLimit);
      
      this.showLimitDialog = false;
      
      promptAction.showToast({ message: '交易限额设置成功' });
      
      // 重新加载用户信息
      await this.loadFullUserInfo();
      
    } catch (error) {
      console.error('设置交易限额失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '设置交易限额失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 更新个人资料
   */
  async updateProfile() {
    if (!this.userInfo) return;
    
    // 表单验证
    if (!this.editUsername.trim()) {
      promptAction.showToast({ message: '用户名不能为空' });
      return;
    }
    
    this.isLoading = true;
    
    try {
      await UserApi.updateProfile(this.userInfo.userId, this.editUsername.trim(), this.editEmail.trim());
      
      this.showProfileDialog = false;
      
      promptAction.showToast({ message: '个人资料更新成功' });
      
      // 重新加载用户信息
      await this.loadFullUserInfo();
      
      // 更新本地存储的用户信息
      if (this.userInfo) {
        this.userInfo.username = this.editUsername.trim();
        await storageManager.saveUserInfo(this.userInfo);
      }
      
    } catch (error) {
      console.error('更新个人资料失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '更新个人资料失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 退出登录
   */
  async logout() {
    try {
      await storageManager.clearUserData();
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      promptAction.showToast({ message: '退出登录失败' });
    }
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 设置内容
      this.SettingsContentView()
      
      // 各种弹窗
      if (this.showChangePasswordDialog) {
        this.ChangePasswordDialog()
      }
      
      if (this.showPayPasswordDialog) {
        this.PayPasswordDialog()
      }
      
      if (this.showLimitDialog) {
        this.LimitDialog()
      }
      
      if (this.showProfileDialog) {
        this.ProfileDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })
      
      Text('个人设置')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })
      
      Blank()
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder SettingsContentView() {
    Scroll() {
      Column() {
        // 用户信息卡片
        this.UserInfoCardView()
        
        // 账户安全设置
        this.SecuritySettingsView()
        
        // 交易设置
        this.TransactionSettingsView()
        
        // 其他设置
        this.OtherSettingsView()
        
        // 退出登录按钮
        this.LogoutButtonView()
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 16 })
    }
    .width('100%')
    .layoutWeight(1)
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
  }

  @Builder UserInfoCardView() {
    Column() {
      Row() {
        // 用户头像
        Image($r('app.media.icon'))
          .width(60)
          .height(60)
          .borderRadius(30)
          .backgroundColor('#E0E0E0')
          .margin({ right: 16 })
        
        // 用户信息
        Column() {
          Text(this.fullUserInfo?.username || this.userInfo?.username || '用户')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#333333')
            .alignSelf(ItemAlign.Start)
          
          Text(this.formatPhoneNumber(this.fullUserInfo?.phone || this.userInfo?.phone || ''))
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ top: 4 })
          
          if (this.fullUserInfo?.email) {
            Text(this.fullUserInfo.email)
              .fontSize(12)
              .fontColor('#999999')
              .alignSelf(ItemAlign.Start)
              .margin({ top: 2 })
          }
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)
        
        // 编辑按钮
        Image($r('app.media.icon'))
          .width(20)
          .height(20)
          .fillColor('#666666')
          .onClick(() => {
            this.showProfileDialog = true;
          })
      }
      .width('100%')
      .padding(20)
    }
    .width('100%')
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder SecuritySettingsView() {
    Column() {
      Text('账户安全')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      this.SettingItemView('修改登录密码', '保护账户安全', () => {
        this.showChangePasswordDialog = true;
      })
      
      this.SettingItemView('设置支付密码', '用于支付验证', () => {
        this.showPayPasswordDialog = true;
      })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder TransactionSettingsView() {
    Column() {
      Text('交易设置')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      this.SettingItemView('交易限额设置', `日限额: ¥${this.dailyLimit.toFixed(0)}`, () => {
        this.showLimitDialog = true;
      })
      
      this.SettingItemView('风险提醒', '开启交易风险提醒', () => {
        promptAction.showToast({ message: '功能开发中' });
      })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder OtherSettingsView() {
    Column() {
      Text('其他设置')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      this.SettingItemView('关于我们', '版本信息和帮助', () => {
        promptAction.showToast({ message: '钱包应用 v1.0.0' });
      })
      
      this.SettingItemView('隐私政策', '查看隐私政策', () => {
        promptAction.showToast({ message: '功能开发中' });
      })
      
      this.SettingItemView('用户协议', '查看用户协议', () => {
        promptAction.showToast({ message: '功能开发中' });
      })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder SettingItemView(title: string, subtitle: string, onClick: () => void) {
    Row() {
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
        
        Text(subtitle)
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
      
      Image($r('app.media.icon'))
        .width(16)
        .height(16)
        .fillColor('#CCCCCC')
    }
    .width('100%')
    .padding({ vertical: 12 })
    .onClick(onClick)
  }

  @Builder LogoutButtonView() {
    Button('退出登录')
      .width('100%')
      .height(48)
      .fontSize(16)
      .fontColor('#FF4444')
      .backgroundColor(Color.White)
      .borderRadius(8)
      .border({ width: 1, color: '#FF4444' })
      .margin({ top: 20 })
      .onClick(() => {
        this.logout();
      })
  }

  @Builder ChangePasswordDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showChangePasswordDialog = false;
          this.oldPassword = '';
          this.newPassword = '';
          this.confirmPassword = '';
        })
      
      // 修改密码弹窗
      Column() {
        Text('修改登录密码')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.PasswordInputView('当前密码', this.oldPassword, (value: string) => {
            this.oldPassword = value;
          })
          
          this.PasswordInputView('新密码', this.newPassword, (value: string) => {
            this.newPassword = value;
          })
          
          this.PasswordInputView('确认新密码', this.confirmPassword, (value: string) => {
            this.confirmPassword = value;
          })
        }
        .width('100%')
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showChangePasswordDialog = false;
              this.oldPassword = '';
              this.newPassword = '';
              this.confirmPassword = '';
            })
          
          Blank()
          
          Button(this.isLoading ? '修改中...' : '确认修改')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.changePassword();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('85%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '7.5%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder PayPasswordDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showPayPasswordDialog = false;
          this.payPassword = '';
          this.confirmPayPassword = '';
        })
      
      // 设置支付密码弹窗
      Column() {
        Text('设置支付密码')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.PasswordInputView('支付密码(6位数字)', this.payPassword, (value: string) => {
            this.payPassword = value;
          })
          
          this.PasswordInputView('确认支付密码', this.confirmPayPassword, (value: string) => {
            this.confirmPayPassword = value;
          })
        }
        .width('100%')
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showPayPasswordDialog = false;
              this.payPassword = '';
              this.confirmPayPassword = '';
            })
          
          Blank()
          
          Button(this.isLoading ? '设置中...' : '确认设置')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.setPayPassword();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('85%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '7.5%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder LimitDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showLimitDialog = false;
        })
      
      // 限额设置弹窗
      Column() {
        Text('交易限额设置')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.NumberInputView('单笔限额(元)', this.singleLimit, (value: number) => {
            this.singleLimit = value;
          })
          
          this.NumberInputView('日限额(元)', this.dailyLimit, (value: number) => {
            this.dailyLimit = value;
          })
          
          this.NumberInputView('月限额(元)', this.monthlyLimit, (value: number) => {
            this.monthlyLimit = value;
          })
        }
        .width('100%')
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showLimitDialog = false;
            })
          
          Blank()
          
          Button(this.isLoading ? '设置中...' : '确认设置')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.setTransactionLimits();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('85%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '7.5%', y: '25%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder ProfileDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showProfileDialog = false;
        })
      
      // 个人资料弹窗
      Column() {
        Text('编辑个人资料')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.FormInputView('用户名', this.editUsername, (value: string) => {
            this.editUsername = value;
          })
          
          this.FormInputView('邮箱', this.editEmail, (value: string) => {
            this.editEmail = value;
          })
        }
        .width('100%')
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showProfileDialog = false;
            })
          
          Blank()
          
          Button(this.isLoading ? '更新中...' : '确认更新')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.updateProfile();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('85%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '7.5%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder PasswordInputView(label: string, value: string, onChange: (value: string) => void) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: `请输入${label}`, text: value })
        .type(InputType.Password)
        .fontSize(14)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange(onChange)
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder NumberInputView(label: string, value: number, onChange: (value: number) => void) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: `请输入${label}`, text: value.toString() })
        .type(InputType.Number)
        .fontSize(14)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange((text: string) => {
          const num = parseFloat(text) || 0;
          onChange(num);
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  @Builder FormInputView(label: string, value: string, onChange: (value: string) => void) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })
      
      TextInput({ placeholder: `请输入${label}`, text: value })
        .fontSize(14)
        .padding(12)
        .backgroundColor('#F8F8F8')
        .borderRadius(8)
        .onChange(onChange)
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 16 })
  }

  /**
   * 格式化手机号显示
   */
  formatPhoneNumber(phone: string): string {
    if (!phone || phone.length < 11) {
      return phone;
    }
    return phone.substring(0, 3) + '****' + phone.substring(7);
  }
}
