import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  Transaction, 
  LocalUserInfo,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct TransactionDetailPage {
  @State userInfo: LocalUserInfo | null = null;
  @State transaction: Transaction | null = null;
  @State isLoading: boolean = false;
  @State txnId: number = 0;

  async aboutToAppear() {
    console.log('TransactionDetailPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params) {
      this.txnId = params.txnId || 0;
      if (params.userId) {
        this.userInfo = {
          userId: params.userId,
          phone: '',
          username: '',
          token: '',
          loginTime: Date.now(),
          rememberLogin: false
        };
      }
    }
    
    if (!this.userInfo) {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载交易详情
    await this.loadTransactionDetail();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载交易详情
   */
  async loadTransactionDetail() {
    if (!this.userInfo || !this.txnId) return;
    
    this.isLoading = true;
    
    try {
      const transaction = await TransactionApi.getTransactionDetail(this.txnId);
      this.transaction = transaction;
      console.log('交易详情加载成功:', transaction);
    } catch (error) {
      console.error('加载交易详情失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '加载交易详情失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  /**
   * 格式化交易时间
   */
  formatTransactionTime(timeStr: string): string {
    try {
      const date = new Date(timeStr);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    } catch (error) {
      return timeStr;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 交易详情内容
      this.TransactionDetailView()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })
      
      Text('交易详情')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })
      
      Blank()
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder TransactionDetailView() {
    if (this.isLoading) {
      // 加载状态
      Column() {
        Text('加载中...')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 100 })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
    } else if (!this.transaction) {
      // 空状态
      Column() {
        Text('📄')
          .fontSize(48)
          .margin({ bottom: 16 })
        
        Text('交易详情不存在')
          .fontSize(16)
          .fontColor('#666666')
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ top: 100 })
    } else {
      // 交易详情内容
      Scroll() {
        Column() {
          // 交易状态卡片
          this.TransactionStatusCardView()
          
          // 交易信息卡片
          this.TransactionInfoCardView()
          
          // 交易流水信息
          this.TransactionFlowCardView()
        }
        .width('100%')
        .padding({ horizontal: 16, vertical: 20 })
      }
      .width('100%')
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
    }
  }

  @Builder TransactionStatusCardView() {
    if (!this.transaction) return;
    
    Column() {
      // 交易类型图标
      Text(this.getTransactionIcon(this.transaction.type))
        .fontSize(48)
        .margin({ bottom: 16 })
      
      // 交易金额
      Text(TransactionApi.formatAmount(this.transaction.amount, this.transaction.type))
        .fontSize(32)
        .fontWeight(FontWeight.Bold)
        .fontColor(TransactionApi.getAmountColor(this.transaction.type))
        .margin({ bottom: 8 })
      
      // 交易状态
      Text(TransactionApi.getTransactionStatusText(this.transaction.status))
        .fontSize(16)
        .fontColor(this.getStatusColor(this.transaction.status))
        .margin({ bottom: 16 })
      
      // 交易类型
      Text(TransactionApi.getTransactionTypeText(this.transaction.type))
        .fontSize(14)
        .fontColor('#666666')
    }
    .width('100%')
    .padding({ vertical: 40 })
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder TransactionInfoCardView() {
    if (!this.transaction) return;
    
    Column() {
      Text('交易信息')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })
      
      Column() {
        this.DetailItemView('交易单号', this.transaction.txnId.toString())
        this.DetailItemView('交易时间', this.formatTransactionTime(this.transaction.createdAt))
        this.DetailItemView('交易金额', `¥${this.transaction.amount.toFixed(2)}`)
        this.DetailItemView('交易类型', TransactionApi.getTransactionTypeText(this.transaction.type))
        this.DetailItemView('交易状态', TransactionApi.getTransactionStatusText(this.transaction.status))
        
        if (this.transaction.fromAccount) {
          this.DetailItemView('付款账户', this.transaction.fromAccount);
        }
        
        if (this.transaction.toAccount) {
          this.DetailItemView('收款账户', this.transaction.toAccount);
        }
        
        if (this.transaction.remark) {
          this.DetailItemView('交易备注', this.transaction.remark);
        }
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder TransactionFlowCardView() {
    if (!this.transaction) return;
    
    Column() {
      Text('交易流水')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })
      
      Column() {
        this.DetailItemView('流水号', this.transaction.txnId.toString())
        this.DetailItemView('创建时间', this.formatTransactionTime(this.transaction.createdAt))
        
        if (this.transaction.updatedAt && this.transaction.updatedAt !== this.transaction.createdAt) {
          this.DetailItemView('更新时间', this.formatTransactionTime(this.transaction.updatedAt));
        }
        
        if (this.transaction.paymentMethod) {
          this.DetailItemView('支付方式', this.transaction.paymentMethod === 1 ? '钱包支付' : '银行卡支付');
        }
        
        if (this.transaction.paymentChannel) {
          this.DetailItemView('支付渠道', this.getPaymentChannelText(this.transaction.paymentChannel));
        }
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .margin({ bottom: 16 })
  }

  @Builder DetailItemView(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)
      
      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .margin({ bottom: 12 })
  }

  /**
   * 获取交易类型图标
   */
  getTransactionIcon(type: number): string {
    switch (type) {
      case 1: // RECHARGE
        return '💰';
      case 2: // TRANSFER
        return '💸';
      case 3: // RECEIVE
        return '💵';
      case 4: // PAYMENT
        return '🛒';
      default:
        return '💳';
    }
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status: number): string {
    switch (status) {
      case 1: // SUCCESS
        return '#00C851';
      case 2: // FAILED
        return '#FF4444';
      case 0: // PENDING
        return '#FF8800';
      default:
        return '#666666';
    }
  }

  /**
   * 获取支付渠道文本
   */
  getPaymentChannelText(channel: number): string {
    switch (channel) {
      case 1:
        return '商户支付';
      case 2:
        return '扫码支付';
      case 3:
        return 'NFC支付';
      default:
        return '其他';
    }
  }
}
