import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  Transaction, 
  TransactionQueryParams, 
  TransactionType, 
  TransactionStatus, 
  PageResult,
  LocalUserInfo,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct TransactionRecordsPage {
  @State userInfo: LocalUserInfo | null = null;
  @State transactions: Transaction[] = [];
  @State isLoading: boolean = false;
  @State currentPage: number = 1;
  @State pageSize: number = 10;
  @State totalPages: number = 1;
  @State totalRecords: number = 0;
  @State selectedType: TransactionType | undefined = undefined;
  @State showFilterDialog: boolean = false;
  @State searchKeyword: string = '';
  @State refreshing: boolean = false;

  async aboutToAppear() {
    console.log('TransactionRecordsPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params && params.userId) {
      this.userInfo = {
        userId: params.userId,
        phone: '',
        username: '',
        token: '',
        loginTime: Date.now(),
        rememberLogin: false
      };
    } else {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载交易记录
    await this.loadTransactions();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载交易记录
   */
  async loadTransactions(reset: boolean = false) {
    if (!this.userInfo) return;
    
    if (reset) {
      this.currentPage = 1;
      this.transactions = [];
    }
    
    this.isLoading = true;
    
    try {
      const params: TransactionQueryParams = {
        userId: this.userInfo.userId,
        page: this.currentPage,
        size: this.pageSize,
        type: this.selectedType
      };
      
      let result: PageResult<Transaction>;
      
      if (this.searchKeyword.trim()) {
        // 搜索模式
        result = await TransactionApi.searchTransactions(
          this.userInfo.userId, 
          this.searchKeyword.trim(), 
          this.currentPage, 
          this.pageSize
        );
      } else {
        // 普通列表模式
        result = await TransactionApi.getTransactionList(params);
      }
      
      if (reset) {
        this.transactions = result.records;
      } else {
        this.transactions = [...this.transactions, ...result.records];
      }
      
      this.totalPages = result.pages;
      this.totalRecords = result.total;
      
      console.log(`交易记录加载成功: ${result.records.length}条, 总计${result.total}条`);
      
    } catch (error) {
      console.error('加载交易记录失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '加载交易记录失败' 
      });
    } finally {
      this.isLoading = false;
      this.refreshing = false;
    }
  }

  /**
   * 加载更多数据
   */
  async loadMore() {
    if (this.isLoading || this.currentPage >= this.totalPages) {
      return;
    }
    
    this.currentPage++;
    await this.loadTransactions(false);
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    this.refreshing = true;
    await this.loadTransactions(true);
  }

  /**
   * 搜索交易记录
   */
  async searchTransactions() {
    await this.loadTransactions(true);
  }

  /**
   * 筛选交易类型
   */
  async filterByType(type?: TransactionType) {
    this.selectedType = type;
    this.showFilterDialog = false;
    await this.loadTransactions(true);
  }

  /**
   * 跳转到交易详情页面
   */
  navigateToTransactionDetail(txnId: number) {
    router.pushUrl({
      url: 'pages/TransactionDetailPage',
      params: {
        txnId: txnId
      }
    }).catch((error: Error) => {
      console.error('跳转交易详情页面失败:', error);
      promptAction.showToast({ message: '页面跳转失败' });
    });
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 搜索和筛选栏
      this.SearchFilterView()
      
      // 交易记录列表
      this.TransactionListView()
      
      // 筛选弹窗
      if (this.showFilterDialog) {
        this.FilterDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })
      
      Text('交易记录')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })
      
      Blank()
      
      Text(`共${this.totalRecords}条`)
        .fontSize(14)
        .fontColor('#666666')
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder SearchFilterView() {
    Row() {
      // 搜索框
      TextInput({ placeholder: '搜索交易记录...', text: this.searchKeyword })
        .layoutWeight(1)
        .height(40)
        .fontSize(14)
        .backgroundColor('#F8F8F8')
        .borderRadius(20)
        .padding({ horizontal: 16 })
        .onChange((value: string) => {
          this.searchKeyword = value;
        })
        .onSubmit(() => {
          this.searchTransactions();
        })
      
      // 筛选按钮
      Button('筛选')
        .height(40)
        .fontSize(14)
        .fontColor('#007AFF')
        .backgroundColor('#F0F8FF')
        .borderRadius(20)
        .padding({ horizontal: 16 })
        .margin({ left: 12 })
        .onClick(() => {
          this.showFilterDialog = true;
        })
    }
    .width('100%')
    .padding({ horizontal: 16, vertical: 12 })
    .backgroundColor(Color.White)
  }

  @Builder TransactionListView() {
    if (this.isLoading && this.transactions.length === 0) {
      // 首次加载状态
      Column() {
        Text('加载中...')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 100 })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
    } else if (this.transactions.length === 0) {
      // 空状态
      Column() {
        Text('📝')
          .fontSize(48)
          .margin({ bottom: 16 })
        
        Text('暂无交易记录')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ bottom: 8 })
        
        Text('完成交易后记录将显示在这里')
          .fontSize(14)
          .fontColor('#999999')
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ top: 100 })
    } else {
      // 交易记录列表
      List() {
        ForEach(this.transactions, (transaction: Transaction, index: number) => {
          ListItem() {
            this.TransactionItemView(transaction)
          }
          .onClick(() => {
            this.navigateToTransactionDetail(transaction.txnId);
          })
        })
        
        // 加载更多指示器
        if (this.currentPage < this.totalPages) {
          ListItem() {
            Row() {
              if (this.isLoading) {
                Text('加载中...')
                  .fontSize(14)
                  .fontColor('#666666')
              } else {
                Text('点击加载更多')
                  .fontSize(14)
                  .fontColor('#007AFF')
              }
            }
            .width('100%')
            .height(50)
            .justifyContent(FlexAlign.Center)
            .onClick(() => {
              if (!this.isLoading) {
                this.loadMore();
              }
            })
          }
        }
      }
      .width('100%')
      .layoutWeight(1)
      .divider({ strokeWidth: 1, color: '#F0F0F0' })
      .onReachEnd(() => {
        // 滚动到底部时自动加载更多
        if (!this.isLoading && this.currentPage < this.totalPages) {
          this.loadMore();
        }
      })
    }
  }

  @Builder TransactionItemView(transaction: Transaction) {
    Row() {
      // 交易类型图标
      Text(this.getTransactionIcon(transaction.type))
        .fontSize(20)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor(this.getTransactionIconBg(transaction.type))
        .borderRadius(20)
        .margin({ right: 12 })
      
      // 交易信息
      Column() {
        Row() {
          Text(TransactionApi.getTransactionTypeText(transaction.type))
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
          
          Blank()
          
          Text(TransactionApi.formatAmount(transaction.amount, transaction.type))
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(TransactionApi.getAmountColor(transaction.type))
        }
        .width('100%')
        
        Row() {
          Text(this.formatTransactionTime(transaction.createdAt))
            .fontSize(12)
            .fontColor('#999999')
          
          Blank()
          
          Text(TransactionApi.getTransactionStatusText(transaction.status))
            .fontSize(12)
            .fontColor(this.getStatusColor(transaction.status))
        }
        .width('100%')
        .margin({ top: 4 })
        
        if (transaction.remark) {
          Text(transaction.remark)
            .fontSize(12)
            .fontColor('#666666')
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .margin({ top: 4 })
        }
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding({ horizontal: 16, vertical: 12 })
    .backgroundColor(Color.White)
  }

  @Builder FilterDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showFilterDialog = false;
        })
      
      // 筛选弹窗
      Column() {
        Text('筛选交易类型')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        Column() {
          this.FilterOptionView('全部', undefined)
          this.FilterOptionView('充值', TransactionType.RECHARGE)
          this.FilterOptionView('转账', TransactionType.TRANSFER)
          this.FilterOptionView('收款', TransactionType.RECEIVE)
          this.FilterOptionView('支付', TransactionType.PAYMENT)
        }
        .width('100%')
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showFilterDialog = false;
            })
          
          Blank()
          
          Button('重置')
            .fontSize(14)
            .fontColor('#007AFF')
            .backgroundColor('#F0F8FF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.filterByType(undefined);
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder FilterOptionView(title: string, type?: TransactionType) {
    Row() {
      Text(title)
        .fontSize(16)
        .fontColor('#333333')
      
      Blank()
      
      if (this.selectedType === type) {
        Text('✓')
          .fontSize(16)
          .fontColor('#007AFF')
      }
    }
    .width('100%')
    .height(48)
    .padding({ horizontal: 16 })
    .onClick(() => {
      this.filterByType(type);
    })
  }

  /**
   * 获取交易类型图标
   */
  getTransactionIcon(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE:
        return '💰';
      case TransactionType.TRANSFER:
        return '💸';
      case TransactionType.RECEIVE:
        return '💵';
      case TransactionType.PAYMENT:
        return '🛒';
      default:
        return '💳';
    }
  }

  /**
   * 获取交易类型图标背景色
   */
  getTransactionIconBg(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE:
        return '#E8F5E8';
      case TransactionType.TRANSFER:
        return '#FFF0E6';
      case TransactionType.RECEIVE:
        return '#E8F5E8';
      case TransactionType.PAYMENT:
        return '#F0F8FF';
      default:
        return '#F5F5F5';
    }
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status: TransactionStatus): string {
    switch (status) {
      case TransactionStatus.SUCCESS:
        return '#00C851';
      case TransactionStatus.FAILED:
        return '#FF4444';
      case TransactionStatus.PENDING:
        return '#FF8800';
      default:
        return '#666666';
    }
  }

  /**
   * 格式化交易时间显示
   */
  formatTransactionTime(timeStr: string): string {
    try {
      const date = new Date(timeStr);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      
      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) { // 24小时内
        return `${Math.floor(diff / 3600000)}小时前`;
      } else if (diff < 86400000 * 7) { // 7天内
        return `${Math.floor(diff / 86400000)}天前`;
      } else {
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      }
    } catch (error) {
      return timeStr;
    }
  }
}
