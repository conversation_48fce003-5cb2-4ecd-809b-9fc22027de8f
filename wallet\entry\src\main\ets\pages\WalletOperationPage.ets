import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { WalletApi } from '../api/WalletApi';
import { BankCardApi } from '../api/BankCardApi';
import { storageManager } from '../common/storage/StorageManager';
import { 
  LocalUserInfo, 
  BankCard,
  ApiError 
} from '../common/types/index';

@Entry
@Component
struct WalletOperationPage {
  @State userInfo: LocalUserInfo | null = null;
  @State operationType: string = 'recharge'; // recharge, withdraw, transfer, receive
  @State amount: string = '';
  @State selectedCard: BankCard | null = null;
  @State bankCards: BankCard[] = [];
  @State payPassword: string = '';
  @State remark: string = '';
  @State recipientAccount: string = ''; // 转账收款账号
  @State recipientPhone: string = ''; // 转账收款手机号
  @State isLoading: boolean = false;
  @State showCardSelector: boolean = false;
  @State showPayPasswordDialog: boolean = false;

  async aboutToAppear() {
    console.log('WalletOperationPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, any>;
    if (params) {
      this.operationType = params.operationType || 'recharge';
      if (params.userId) {
        this.userInfo = {
          userId: params.userId,
          phone: '',
          username: '',
          token: '',
          loginTime: Date.now(),
          rememberLogin: false
        };
      }
    }
    
    if (!this.userInfo) {
      // 从存储中获取用户信息
      await this.loadUserInfo();
    }
    
    // 加载银行卡列表
    await this.loadBankCards();
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      await storageManager.init();
      const userInfo = await storageManager.getUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载银行卡列表
   */
  async loadBankCards() {
    if (!this.userInfo) return;
    
    try {
      const bankCards = await BankCardApi.getBoundCards(this.userInfo.userId);
      this.bankCards = bankCards;
      
      // 自动选择默认银行卡
      const defaultCard = bankCards.find(card => card.isDefault);
      if (defaultCard) {
        this.selectedCard = defaultCard;
      } else if (bankCards.length > 0) {
        this.selectedCard = bankCards[0];
      }
      
      console.log(`银行卡列表加载成功: ${bankCards.length}张`);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
    }
  }

  /**
   * 执行钱包操作
   */
  async executeOperation() {
    if (!this.userInfo || !this.validateForm()) {
      return;
    }
    
    this.showPayPasswordDialog = true;
  }

  /**
   * 确认执行操作
   */
  async confirmOperation() {
    if (!this.userInfo || !this.payPassword) {
      promptAction.showToast({ message: '请输入支付密码' });
      return;
    }
    
    this.isLoading = true;
    this.showPayPasswordDialog = false;
    
    try {
      const amountValue = parseFloat(this.amount);
      
      switch (this.operationType) {
        case 'recharge':
          await this.handleRecharge(amountValue);
          break;
        case 'withdraw':
          await this.handleWithdraw(amountValue);
          break;
        case 'transfer':
          await this.handleTransfer(amountValue);
          break;
        case 'receive':
          await this.handleReceive(amountValue);
          break;
      }
      
      promptAction.showToast({ message: '操作成功' });
      
      // 清空表单
      this.resetForm();
      
      // 延迟返回上一页
      setTimeout(() => {
        router.back();
      }, 1500);
      
    } catch (error) {
      console.error('钱包操作失败:', error);
      const apiError = error as ApiError;
      promptAction.showToast({ 
        message: apiError.message || '操作失败' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理充值
   */
  async handleRecharge(amount: number) {
    if (!this.userInfo || !this.selectedCard) return;
    
    await WalletApi.recharge(
      this.userInfo.userId,
      amount,
      this.selectedCard.cardId,
      this.payPassword,
      this.remark || '钱包充值'
    );
  }

  /**
   * 处理提现
   */
  async handleWithdraw(amount: number) {
    if (!this.userInfo || !this.selectedCard) return;
    
    await WalletApi.withdraw(
      this.userInfo.userId,
      amount,
      this.selectedCard.cardId,
      this.payPassword,
      this.remark || '钱包提现'
    );
  }

  /**
   * 处理转账
   */
  async handleTransfer(amount: number) {
    if (!this.userInfo) return;
    
    if (this.recipientPhone) {
      // 通过手机号转账
      await WalletApi.transferByPhone(
        this.userInfo.userId,
        this.recipientPhone,
        amount,
        this.payPassword,
        this.remark || '钱包转账'
      );
    } else if (this.recipientAccount) {
      // 通过账号转账
      await WalletApi.transfer(
        this.userInfo.userId,
        this.recipientAccount,
        amount,
        this.payPassword,
        this.remark || '钱包转账'
      );
    }
  }

  /**
   * 处理收款
   */
  async handleReceive(amount: number) {
    if (!this.userInfo) return;
    
    // 生成收款码或处理收款逻辑
    await WalletApi.generateReceiveQR(
      this.userInfo.userId,
      amount,
      this.remark || '收款'
    );
  }

  /**
   * 表单验证
   */
  validateForm(): boolean {
    if (!this.amount || parseFloat(this.amount) <= 0) {
      promptAction.showToast({ message: '请输入正确的金额' });
      return false;
    }
    
    if ((this.operationType === 'recharge' || this.operationType === 'withdraw') && !this.selectedCard) {
      promptAction.showToast({ message: '请选择银行卡' });
      return false;
    }
    
    if (this.operationType === 'transfer' && !this.recipientAccount && !this.recipientPhone) {
      promptAction.showToast({ message: '请输入收款账号或手机号' });
      return false;
    }
    
    return true;
  }

  /**
   * 重置表单
   */
  resetForm() {
    this.amount = '';
    this.payPassword = '';
    this.remark = '';
    this.recipientAccount = '';
    this.recipientPhone = '';
  }

  /**
   * 选择银行卡
   */
  selectCard(card: BankCard) {
    this.selectedCard = card;
    this.showCardSelector = false;
  }

  /**
   * 返回上一页
   */
  goBack() {
    router.back();
  }

  /**
   * 获取操作标题
   */
  getOperationTitle(): string {
    switch (this.operationType) {
      case 'recharge':
        return '钱包充值';
      case 'withdraw':
        return '钱包提现';
      case 'transfer':
        return '钱包转账';
      case 'receive':
        return '收款';
      default:
        return '钱包操作';
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 操作表单
      this.OperationFormView()
      
      // 银行卡选择弹窗
      if (this.showCardSelector) {
        this.CardSelectorDialog()
      }
      
      // 支付密码弹窗
      if (this.showPayPasswordDialog) {
        this.PayPasswordDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder TopNavigationView() {
    Row() {
      Image($r('app.media.icon'))
        .width(24)
        .height(24)
        .fillColor('#333333')
        .onClick(() => {
          this.goBack();
        })
      
      Text(this.getOperationTitle())
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ left: 16 })
      
      Blank()
    }
    .width('100%')
    .height(56)
    .padding({ horizontal: 16 })
    .backgroundColor(Color.White)
    .border({ width: { bottom: 1 }, color: '#E0E0E0' })
  }

  @Builder OperationFormView() {
    Scroll() {
      Column() {
        // 金额输入
        this.AmountInputView()
        
        // 银行卡选择（充值和提现需要）
        if (this.operationType === 'recharge' || this.operationType === 'withdraw') {
          this.BankCardSelectorView()
        }
        
        // 收款信息（转账需要）
        if (this.operationType === 'transfer') {
          this.RecipientInfoView()
        }
        
        // 备注输入
        this.RemarkInputView()
        
        // 确认按钮
        this.ConfirmButtonView()
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 20 })
    }
    .width('100%')
    .layoutWeight(1)
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
  }

  @Builder AmountInputView() {
    Column() {
      Text('金额')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      Row() {
        Text('¥')
          .fontSize(24)
          .fontColor('#333333')
          .margin({ right: 8 })
        
        TextInput({ placeholder: '0.00', text: this.amount })
          .type(InputType.Number)
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .backgroundColor(Color.Transparent)
          .border({ width: 0 })
          .layoutWeight(1)
          .onChange((value: string) => {
            this.amount = value;
          })
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 20 })
      .backgroundColor(Color.White)
      .borderRadius(12)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder BankCardSelectorView() {
    Column() {
      Text(this.operationType === 'recharge' ? '充值到银行卡' : '提现到银行卡')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      Row() {
        if (this.selectedCard) {
          Column() {
            Text(this.selectedCard.bankName)
              .fontSize(16)
              .fontColor('#333333')
              .alignSelf(ItemAlign.Start)
            
            Text(BankCardApi.formatCardNumber(this.selectedCard.cardNumber))
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ top: 4 })
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)
        } else {
          Text('请选择银行卡')
            .fontSize(16)
            .fontColor('#999999')
            .layoutWeight(1)
        }
        
        Image($r('app.media.icon'))
          .width(16)
          .height(16)
          .fillColor('#CCCCCC')
      }
      .width('100%')
      .padding(16)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .onClick(() => {
        this.showCardSelector = true;
      })
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder RecipientInfoView() {
    Column() {
      Text('收款信息')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      Column() {
        TextInput({ placeholder: '请输入收款手机号', text: this.recipientPhone })
          .type(InputType.PhoneNumber)
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .margin({ bottom: 12 })
          .onChange((value: string) => {
            this.recipientPhone = value;
          })
        
        Text('或')
          .fontSize(14)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Center)
          .margin({ vertical: 8 })
        
        TextInput({ placeholder: '请输入收款账号', text: this.recipientAccount })
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .onChange((value: string) => {
            this.recipientAccount = value;
          })
      }
      .width('100%')
      .padding(16)
      .backgroundColor(Color.White)
      .borderRadius(12)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder RemarkInputView() {
    Column() {
      Text('备注（可选）')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })
      
      TextInput({ placeholder: '请输入备注信息', text: this.remark })
        .fontSize(16)
        .padding(16)
        .backgroundColor(Color.White)
        .borderRadius(12)
        .onChange((value: string) => {
          this.remark = value;
        })
    }
    .width('100%')
    .margin({ bottom: 30 })
  }

  @Builder ConfirmButtonView() {
    Button(this.isLoading ? '处理中...' : '确认操作')
      .width('100%')
      .height(48)
      .fontSize(16)
      .fontColor(Color.White)
      .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
      .borderRadius(8)
      .enabled(!this.isLoading)
      .onClick(() => {
        this.executeOperation();
      })
  }

  @Builder CardSelectorDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showCardSelector = false;
        })
      
      // 银行卡选择弹窗
      Column() {
        Text('选择银行卡')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        if (this.bankCards.length > 0) {
          Column() {
            ForEach(this.bankCards, (card: BankCard) => {
              Row() {
                Column() {
                  Text(card.bankName)
                    .fontSize(16)
                    .fontColor('#333333')
                    .alignSelf(ItemAlign.Start)
                  
                  Text(BankCardApi.formatCardNumber(card.cardNumber))
                    .fontSize(14)
                    .fontColor('#666666')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)
                
                if (this.selectedCard?.cardId === card.cardId) {
                  Text('✓')
                    .fontSize(16)
                    .fontColor('#007AFF')
                }
              }
              .width('100%')
              .padding(16)
              .onClick(() => {
                this.selectCard(card);
              })
            })
          }
          .width('100%')
        } else {
          Text('暂无可用银行卡')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ vertical: 20 })
        }
        
        Button('取消')
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F5F5F5')
          .borderRadius(6)
          .padding({ horizontal: 20, vertical: 8 })
          .margin({ top: 20 })
          .onClick(() => {
            this.showCardSelector = false;
          })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '30%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder PayPasswordDialog() {
    Column() {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.5)')
        .onClick(() => {
          this.showPayPasswordDialog = false;
          this.payPassword = '';
        })
      
      // 支付密码弹窗
      Column() {
        Text('请输入支付密码')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
        
        TextInput({ placeholder: '请输入6位支付密码', text: this.payPassword })
          .type(InputType.Password)
          .maxLength(6)
          .fontSize(16)
          .padding(16)
          .backgroundColor('#F8F8F8')
          .borderRadius(8)
          .onChange((value: string) => {
            this.payPassword = value;
          })
        
        Row() {
          Button('取消')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .onClick(() => {
              this.showPayPasswordDialog = false;
              this.payPassword = '';
            })
          
          Blank()
          
          Button(this.isLoading ? '处理中...' : '确认')
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isLoading ? '#CCCCCC' : '#007AFF')
            .borderRadius(6)
            .padding({ horizontal: 20, vertical: 8 })
            .enabled(!this.isLoading)
            .onClick(() => {
              this.confirmOperation();
            })
        }
        .width('100%')
        .margin({ top: 20 })
      }
      .width('80%')
      .padding(20)
      .backgroundColor(Color.White)
      .borderRadius(12)
      .position({ x: '10%', y: '35%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }
}
